const axios = require('axios');

/**
 * Comprehensive Mobile Shift Swap APIs Test
 * Tests all mobile shift swap endpoints as requested
 */

const BASE_URL = 'http://localhost:3000/api/v1/mobile';

// Test credentials
const testCredentials = {
  email: '<EMAIL>',
  password: 'Admin@123'
};

let authToken = '';

/**
 * Login and get authentication token
 */
async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`http://localhost:3000/api/v1/auth/login`, testCredentials);
    
    if (response.data.success && response.data.data && response.data.data.accessToken) {
      authToken = response.data.data.accessToken;
      console.log('✅ Login successful');
      return true;
    } else {
      console.error('❌ Login failed:', response.data.message);
      return false;
    }
  } catch (error) {
    console.error('❌ Login error:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 1: Get Target Employees
 */
async function testGetTargetEmployees() {
  try {
    console.log('\n📋 Testing: GET /target-employees');
    
    const response = await axios.get(`${BASE_URL}/target-employees`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      params: {
        designation_id: 1,
        exclude_self: true
      }
    });

    console.log('✅ Target employees retrieved successfully');
    console.log(`   Total employees: ${response.data.data.total_count}`);
    console.log(`   First employee: ${response.data.data.employees[0]?.full_name || 'None'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Get target employees failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 2: Get Target Employees by Date
 */
async function testGetTargetEmployeesByDate() {
  try {
    console.log('\n📋 Testing: GET /target-employees/:date');
    
    const testDate = '2025-12-01';
    const response = await axios.get(`${BASE_URL}/target-employees/${testDate}`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      params: {
        designation_id: 1,
        exclude_self: true
      }
    });

    console.log('✅ Target employees by date retrieved successfully');
    console.log(`   Date: ${response.data.data.date}`);
    console.log(`   Available employees: ${response.data.data.total_count}`);
    
    return true;
  } catch (error) {
    console.error('❌ Get target employees by date failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 3: Get My Swappable Shifts
 */
async function testGetMySwappableShifts() {
  try {
    console.log('\n📋 Testing: GET /my-swappable-shifts');
    
    const response = await axios.get(`${BASE_URL}/my-swappable-shifts`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      params: {
        start_date: '2025-12-01',
        end_date: '2025-12-15'
      }
    });

    console.log('✅ My swappable shifts retrieved successfully');
    console.log(`   Total shifts: ${response.data.data.shifts?.length || 0}`);
    
    return true;
  } catch (error) {
    console.error('❌ Get my swappable shifts failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 4: Get Available Swaps
 */
async function testGetAvailableSwaps() {
  try {
    console.log('\n📋 Testing: GET /available-swaps');
    
    const response = await axios.get(`${BASE_URL}/available-swaps`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      params: {
        start_date: '2025-12-01',
        end_date: '2025-12-15'
      }
    });

    console.log('✅ Available swaps retrieved successfully');
    console.log(`   Available swaps: ${response.data.data.swaps?.length || 0}`);
    
    return true;
  } catch (error) {
    console.error('❌ Get available swaps failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 5: Create Swap Request
 */
async function testCreateSwapRequest() {
  try {
    console.log('\n📋 Testing: POST /shift-swaps (Create)');
    
    const swapData = {
      requesterAssignmentId: 1,
      targetEmployeeId: 2,
      swapType: 'direct',
      reason: 'Need to attend family function on this day',
      notes: 'Urgent swap request',
      urgency: 'high'
    };

    const response = await axios.post(`${BASE_URL}/shift-swaps`, swapData, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Swap request created successfully');
    console.log(`   Swap ID: ${response.data.data.swap_request?.id || 'N/A'}`);
    console.log(`   Status: ${response.data.data.swap_request?.status || 'N/A'}`);
    
    return response.data.data.swap_request?.id;
  } catch (error) {
    console.error('❌ Create swap request failed:', error.response?.data?.message || error.message);
    return null;
  }
}

/**
 * Test 6: Get All Swap Requests
 */
async function testGetAllSwapRequests() {
  try {
    console.log('\n📋 Testing: GET /shift-swaps');
    
    const response = await axios.get(`${BASE_URL}/shift-swaps`, {
      headers: { 'Authorization': `Bearer ${authToken}` },
      params: {
        page: 1,
        limit: 10,
        status: 'pending'
      }
    });

    console.log('✅ All swap requests retrieved successfully');
    console.log(`   Total requests: ${response.data.data.total_count || 0}`);
    console.log(`   Current page: ${response.data.data.page || 1}`);
    
    return true;
  } catch (error) {
    console.error('❌ Get all swap requests failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 7: Update Swap Request
 */
async function testUpdateSwapRequest(swapId) {
  if (!swapId) {
    console.log('\n⏭️  Skipping update test - no swap ID available');
    return true;
  }

  try {
    console.log('\n📋 Testing: PUT /shift-swaps/:id (Update)');
    
    const updateData = {
      reason: 'Updated reason: Medical emergency',
      notes: 'Updated notes with more details',
      urgency: 'high'
    };

    const response = await axios.put(`${BASE_URL}/shift-swaps/${swapId}`, updateData, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Swap request updated successfully');
    console.log(`   Updated reason: ${response.data.data.swap_request?.reason || 'N/A'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Update swap request failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Test 8: Cancel Swap Request
 */
async function testCancelSwapRequest(swapId) {
  if (!swapId) {
    console.log('\n⏭️  Skipping cancel test - no swap ID available');
    return true;
  }

  try {
    console.log('\n📋 Testing: POST /shift-swaps/:id/cancel');
    
    const response = await axios.post(`${BASE_URL}/shift-swaps/${swapId}/cancel`, {
      reason: 'No longer needed'
    }, {
      headers: { 
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ Swap request cancelled successfully');
    console.log(`   Status: ${response.data.data.swap_request?.status || 'N/A'}`);
    
    return true;
  } catch (error) {
    console.error('❌ Cancel swap request failed:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Main test function
 */
async function runMobileShiftSwapTests() {
  console.log('🧪 Starting Mobile Shift Swap APIs Test\n');
  console.log('=' .repeat(60));
  
  // Step 1: Login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ Test failed: Could not login');
    return;
  }
  
  let testResults = [];
  let swapId = null;
  
  // Test all APIs
  testResults.push(['Target Employees', await testGetTargetEmployees()]);
  testResults.push(['Target Employees by Date', await testGetTargetEmployeesByDate()]);
  testResults.push(['My Swappable Shifts', await testGetMySwappableShifts()]);
  testResults.push(['Available Swaps', await testGetAvailableSwaps()]);
  
  swapId = await testCreateSwapRequest();
  testResults.push(['Create Swap Request', swapId !== null]);
  
  testResults.push(['Get All Swap Requests', await testGetAllSwapRequests()]);
  testResults.push(['Update Swap Request', await testUpdateSwapRequest(swapId)]);
  testResults.push(['Cancel Swap Request', await testCancelSwapRequest(swapId)]);
  
  // Summary
  console.log('\n' + '=' .repeat(60));
  console.log('📊 TEST RESULTS SUMMARY:');
  console.log('=' .repeat(60));
  
  testResults.forEach(([testName, success]) => {
    const status = success ? '✅ PASS' : '❌ FAIL';
    console.log(`${status} ${testName}`);
  });
  
  const passedTests = testResults.filter(([, success]) => success).length;
  const totalTests = testResults.length;
  
  console.log('\n' + '=' .repeat(60));
  console.log(`🎯 OVERALL RESULT: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All mobile shift swap APIs are working perfectly!');
  } else {
    console.log('⚠️  Some APIs need attention');
  }
}

// Run the test
runMobileShiftSwapTests().catch(console.error);
