const Joi = require('joi');

// Test with your exact parameters
async function testYourConstraints() {
  console.log('🧪 Testing Your Exact Parameters');
  
  try {
    // Import the validator
    const rotaScheduleValidator = require('./src/api/validators/rotaSchedule.validator.js');
    const generateAutoSchedule = rotaScheduleValidator.generateAutoSchedule;
    
    // Your exact test data
    const testData = {
      templateMode: {
        shiftTemplateId: 20,
        startDate: "2025-07-01",
        endDate: "2025-07-30",
        customName: "test scheduljhgeabxvvjadhjhgbfsdfdashkhhgffgffjugjhfjhcsulddydhhhhfghggsdAug"   
      }
      // Note: No constraints field provided (just like your request)
    };
    
    console.log('📋 Input Data:');
    console.log(JSON.stringify(testData, null, 2));
    
    const result = generateAutoSchedule.validate(testData);
    
    if (result.error) {
      console.log('❌ Validation Error:', result.error.details);
    } else {
      console.log('✅ Validation Success');
      console.log('\n📊 Constraints Result:');
      console.log(JSON.stringify(result.value.constraints, null, 2));
      
      // Check if constraints are properly set
      if (result.value.constraints && Object.keys(result.value.constraints).length > 0) {
        console.log('\n✅ SUCCESS: Constraints are properly set with default values!');
        console.log(`📊 Total constraint fields: ${Object.keys(result.value.constraints).length}`);
        
        // Check specific values
        console.log('\n🔍 Key Constraint Values:');
        console.log(`- maxConsecutiveDays: ${result.value.constraints.maxConsecutiveDays}`);
        console.log(`- maxShiftsPerEmployee: ${result.value.constraints.maxShiftsPerEmployee}`);
        console.log(`- rebalanceInterval: ${result.value.constraints.rebalanceInterval}`);
        console.log(`- autoAssignEmployees: ${result.value.constraints.autoAssignEmployees}`);
        
      } else {
        console.log('\n❌ ISSUE: Constraints are empty or not set properly');
      }
    }
    
    console.log('\n🎯 Test completed!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testYourConstraints();
