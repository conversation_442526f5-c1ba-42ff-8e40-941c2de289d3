const axios = require('axios');

// Test script to verify constraints storage and retrieval
async function testConstraintsStorage() {
  console.log('🧪 Testing Constraints Storage and Retrieval');
  
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:3000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'Admin@123'
    });
    
    const token = loginResponse.data.result.accessToken;
    console.log('✅ Login successful');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // Test 1: Create auto-schedule with constraints
    console.log('\n📋 Testing: Create auto-schedule with constraints');
    
    const scheduleData = {
      name: 'Test Constraints Schedule',
      description: 'Testing constraints storage',
      startDate: '2025-12-01',
      endDate: '2025-12-07',
      departmentId: 1,
      scheduleType: 'auto_generated'
    };
    
    // Test 1: Generate auto-schedule with constraints (template mode)
    console.log('\n📋 Testing: Generate auto-schedule with constraints');

    const generateData = {
      templateMode: {
        shiftTemplateId: 1,
        startDate: '2025-12-01',
        endDate: '2025-12-07',
        scheduleCategory: 'weekly',
        customName: 'Test Constraints Schedule',
        customDescription: 'Testing constraints storage'
      },
      constraints: {
        maxConsecutiveDays: 5,
        minRestHours: 12,
        maxWeeklyHours: 40,
        preferredShiftTypes: ['morning', 'evening'],
        avoidBackToBackShifts: true,
        balanceWorkload: true,
        respectEmployeePreferences: true,
        minimumStaffingLevels: {
          morning: 3,
          evening: 2,
          night: 1
        }
      }
    };

    const generateResponse = await axios.post(
      'http://localhost:3000/api/v1/rota-schedules/auto-generate',
      generateData,
      { headers }
    );

    const scheduleId = generateResponse.data.data.schedule.id;
    console.log(`✅ Auto-schedule generated with ID: ${scheduleId}`);
    
    console.log('✅ Auto-schedule generated successfully');

    // Test 2: Get schedule by ID and check constraints
    console.log('\n📋 Testing: Get schedule by ID and verify constraints');

    const getResponse = await axios.get(
      `http://localhost:3000/api/v1/rota-schedules/${scheduleId}?includeInstances=true`,
      { headers }
    );
    
    const schedule = getResponse.data.data || getResponse.data.result;
    
    console.log(`📊 Schedule Status: ${schedule.status}`);
    console.log(`📊 Schedule Type: ${schedule.scheduleType}`);
    
    // Check if constraints are returned for preview status
    if (schedule.status === 'preview') {
      console.log('\n✅ Schedule is in preview status - checking constraints...');
      
      if (schedule.constraints) {
        console.log('✅ Constraints found in response:');
        console.log(JSON.stringify(schedule.constraints, null, 2));
      } else {
        console.log('❌ No constraints found in response');
      }
      
      if (schedule.settings) {
        console.log('✅ Settings found in response:');
        console.log('📊 Generation Mode:', schedule.settings.mode);
        console.log('📊 Generated At:', schedule.settings.generatedAt);
        console.log('📊 Generated By:', schedule.settings.generatedBy);
        
        if (schedule.settings.constraints) {
          console.log('✅ Constraints also found in settings:');
          console.log(JSON.stringify(schedule.settings.constraints, null, 2));
        }
      } else {
        console.log('❌ No settings found in response');
      }
      
      if (schedule.conflicts) {
        console.log(`✅ Conflicts found: ${schedule.conflicts.length || 0} conflicts`);
      }
      
    } else {
      console.log(`⚠️  Schedule status is '${schedule.status}', not 'preview' - constraints may not be included`);
    }
    
    // Test 4: Check raw generationMetadata
    if (schedule.generationMetadata) {
      console.log('\n✅ Raw generationMetadata found:');
      const metadata = typeof schedule.generationMetadata === 'string' 
        ? JSON.parse(schedule.generationMetadata) 
        : schedule.generationMetadata;
      
      if (metadata.constraints) {
        console.log('✅ Constraints found in raw metadata:');
        console.log(JSON.stringify(metadata.constraints, null, 2));
      } else {
        console.log('❌ No constraints found in raw metadata');
      }
    } else {
      console.log('❌ No generationMetadata found');
    }
    
    console.log('\n🎯 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Run the test
testConstraintsStorage();
