const express = require('express');
const timesheetController = require('../controllers/timesheet.controller');
const timesheetValidator = require('../validators/timesheet.validator');
const { validate, validateQuery, validateParams } = require('../middlewares/validation.middleware');
const authenticate  = require('../middlewares/authentication.middleware');
const { hasPermission } = require('../middlewares/authorization.middleware');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

/**
 * @route POST /api/v1/timesheets
 * @desc Create timesheet entry
 * @access Private
 */
router.post(
  '/',
  validate(timesheetValidator.createTimesheet),
  hasPermission('timesheet:write'),
  timesheetController.createTimesheet
);

/**
 * @route GET /api/v1/timesheets
 * @desc Get timesheets with filters
 * @access Private
 */
router.get(
  '/',
  validateQuery(timesheetValidator.getTimesheets),
  hasPermission('timesheet:read'),
  timesheetController.getTimesheets
);

router.get(
  '/today',
  validateQuery(timesheetValidator.todayTimesheetRecord),
  hasPermission('timesheet:read'),
  timesheetController.todayTimesheetRecord
);

router.get(
  '/today-download',
  validateQuery(timesheetValidator.todayTimesheetRecord),
  hasPermission('timesheet:read'),
  timesheetController.todayTimesheetRecordDownload
)

router.get(
  '/custom',
  validateQuery(timesheetValidator.customTimesheetRecord),
  hasPermission('timesheet:read'),
  timesheetController.customTimesheetRecord
);

router.get(
  '/custom-download',
  validateQuery(timesheetValidator.customTimesheetRecord),
  hasPermission('timesheet:read'),
  timesheetController.customTimesheetRecordDownload
);

/**
 * @route GET /api/v1/timesheets/my
 * @desc Get employee's own timesheets
 * @access Private
 */
router.get(
  '/my',
  validateQuery(timesheetValidator.getMyTimesheets),
  hasPermission('timesheet:read'),
  timesheetController.getMyTimesheets
);

/**
 * @route GET /api/v1/timesheets/pending-approvals
 * @desc Get timesheets pending approval
 * @access Private
 */
router.get(
  '/pending-approvals',
  validateQuery(timesheetValidator.getPendingApprovals),
  hasPermission('timesheet:approve'),
  timesheetController.getPendingApprovals
);

/**
 * @route GET /api/v1/timesheets/chart/weekly
 * @desc Get weekly timesheet chart
 * @access Private
 */
router.get(
  '/chart/weekly',
  validateQuery(timesheetValidator.getWeeklyTimesheetChart),
  hasPermission('timesheet:read'),
  timesheetController.getWeeklyTimesheetChart
);

/**
 * @route GET /api/v1/timesheets/summary
 * @desc Get timesheet summary statistics
 * @access Private
 */
router.get(
  '/summary',
  validateQuery(timesheetValidator.getTimesheetSummary),
  hasPermission('timesheet:read'),
  timesheetController.getTimesheetSummary
);

/**
 * @route GET /api/v1/timesheets/:id
 * @desc Get timesheet by ID
 * @access Private
 */
router.get(
  '/:id',
  validateParams(timesheetValidator.timesheetIdParam),
  hasPermission('timesheet:read'),
  timesheetController.getTimesheetById
);

/**
 * @route PATCH /api/v1/timesheets/:id
 * @desc Update timesheet
 * @access Private
 */
router.patch(
  '/:id',
  validateParams(timesheetValidator.timesheetIdParam),
  validate(timesheetValidator.updateTimesheet),
  hasPermission('timesheet:write'),
  timesheetController.updateTimesheet
);

/**
 * @route POST /api/v1/timesheets/:id/approve-reject
 * @desc Approve or reject timesheet
 * @access Private
 */
/**
 * @route POST /api/v1/timesheets/bulk/approve-reject
 * @desc Bulk approve or reject timesheets
 * @access Private
 */
router.post(
  '/bulk/approve-reject',
  validate(timesheetValidator.bulkApproveRejectTimesheets),
  // hasPermission('timesheet:approve'),
  timesheetController.bulkApproveRejectTimesheets
);

router.post(
  '/:id/approve-reject',
  validateParams(timesheetValidator.timesheetIdParam),
  validate(timesheetValidator.approveRejectTimesheet),
  // hasPermission('timesheet:approve'),
  timesheetController.approveRejectTimesheet
);

/**
 * @route GET /api/v1/timesheets/employee/:id/graph
 * @desc Get employee timesheet graph
 * @access Private
 */
router.get(
  '/employee/:id/graph',
  validateParams(timesheetValidator.employeeIdParam),
  validateQuery(timesheetValidator.getUserTimesheetGraph),
  hasPermission('timesheet:read'),
  timesheetController.getUserTimesheetGraph
);

/**
 * @route GET /api/v1/timesheets/project/:projectId/analytics
 * @desc Get project timesheet analytics
 * @access Private
 */
router.get(
  '/project/:projectId/analytics',
  validateParams(timesheetValidator.projectIdParam),
  validateQuery(timesheetValidator.getProjectTimesheetAnalytics),
  hasPermission('timesheet:read'),
  timesheetController.getProjectTimesheetAnalytics
);

module.exports = router;
