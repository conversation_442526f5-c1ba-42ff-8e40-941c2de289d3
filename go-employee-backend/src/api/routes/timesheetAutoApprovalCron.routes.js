'use strict';

const express = require('express');
const timesheetAutoApprovalCronController = require('../controllers/timesheetAutoApprovalCron.controller');
const authenticate = require('../middlewares/authentication.middleware');
const { hasPermission, hasRole } = require('../middlewares/authorization.middleware');
const { validate } = require('../middlewares/validation.middleware');
const Joi = require('joi');

const router = express.Router();

// Apply authentication middleware to all routes
router.use(authenticate);
router.get(
  '/status',
  hasPermission('timesheet:read'),
  timesheetAutoApprovalCronController.getCronStatus
);
router.get(
  '/stats',
  hasPermission('timesheet:read'),
  timesheetAutoApprovalCronController.getDetailedStats
);

// Admin-only routes for cron management
// router.use(hasRole(['company_admin', 'super_admin','admin']));
router.post(
  '/start',
  timesheetAutoApprovalCronController.startCronJob
);
router.post(
  '/stop',
  timesheetAutoApprovalCronController.stopCronJob
);
router.post(
  '/trigger',
  validate(Joi.object({
    targetDate: Joi.date().iso().optional().messages({
      'date.format': 'Target date must be in YYYY-MM-DD format'
    })
  })),
  timesheetAutoApprovalCronController.manualTrigger
);
router.post(
  '/test-email',
  validate(Joi.object({
    email: Joi.string().email().required().messages({
      'string.email': 'Please provide a valid email address',
      'any.required': 'Email address is required'
    })
  })),
  timesheetAutoApprovalCronController.testEmailNotification
);

module.exports = router;
