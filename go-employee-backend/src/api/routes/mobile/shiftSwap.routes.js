'use strict';
 
/**
 * Mobile Shift Swap Routes - PRD Implementation
 *
 * Mobile-optimized API endpoints for shift swap management according to PRD:
 * - Mobile-optimized swap discovery
 * - Simplified swap request workflow
 * - Push notification integration
 * - Offline-friendly data structure
 * - Complete CRUD operations with mobile response format
 */

const express = require('express');
const Joi = require('joi');
const mobileShiftSwapController = require('../../controllers/mobile/shiftSwap.controller');
const authenticate = require('../../middlewares/authentication.middleware');
const { validate, validateQuery, validateParams } = require('../../middlewares/validation.middleware');
const mobileShiftSwapValidator = require('../../validators/mobile/shiftSwap.validator');

const router = express.Router();

// Apply authentication to all routes
router.use(authenticate);

// ==================== SWAP REQUESTS MANAGEMENT ====================

/**
 * @route GET /api/v1/mobile/shift-swaps
 * @desc Get all shift swap requests (my requests and requests for approval)
 * @access Private (Employee)
 */
router.get(
  '/shift-swaps',
  validateQuery(mobileShiftSwapValidator.getAllSwapRequests),
  mobileShiftSwapController.getAllSwapRequests
);

/**
 * @route GET /api/v1/mobile/employee-shift-swap
 * @desc Get shift swap requests assigned to current user for approval (similar to employee-leave)
 * @access Private (Employee/Manager/HR)
 */
router.get(
  '/employee-shift-swap',
  validateQuery(mobileShiftSwapValidator.getEmployeeShiftSwaps),
  mobileShiftSwapController.getEmployeeShiftSwaps
);

/**
 * @route POST /api/v1/mobile/shift-swaps
 * @desc Create new shift swap request
 * @access Private (Employee)
 */
router.post(
  '/shift-swaps',
  validate(mobileShiftSwapValidator.createSwapRequest),
  mobileShiftSwapController.createSwapRequest
);

/**
 * @route GET /api/v1/mobile/shift-swaps/:id
 * @desc Get swap request details by ID
 * @access Private (Employee)
 */
router.get(
  '/shift-swaps/:id',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  mobileShiftSwapController.getSwapRequestById
);

/**
 * @route PUT /api/v1/mobile/shift-swaps/:id
 * @desc Update swap request
 * @access Private (Employee)
 */
router.put(
  '/shift-swaps/:id',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  validate(mobileShiftSwapValidator.updateSwapRequest),
  mobileShiftSwapController.updateSwapRequest
);

// ==================== SWAP WORKFLOW OPERATIONS ====================

/**
 * @route POST /api/v1/mobile/shift-swaps/:id/cancel
 * @desc Cancel my swap request
 * @access Private (Employee)
 */
router.post(
  '/shift-swaps/:id/cancel',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  validate(mobileShiftSwapValidator.cancelSwapRequest),
  mobileShiftSwapController.cancelSwapRequest
);

/**
 * @route POST /api/v1/mobile/shift-swaps/:id/approve
 * @desc Approve swap request
 * @access Private (Manager/HR)
 */
router.post(
  '/shift-swaps/:id/approve',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  validate(mobileShiftSwapValidator.approveSwapRequest),
  mobileShiftSwapController.approveSwapRequest
);

/**
 * @route POST /api/v1/mobile/shift-swaps/:id/reject
 * @desc Reject swap request
 * @access Private (Manager/HR)
 */
router.post(
  '/shift-swaps/:id/reject',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  validate(mobileShiftSwapValidator.rejectSwapRequest),
  mobileShiftSwapController.rejectSwapRequest
);

/**
 * @route POST /api/v1/mobile/shift-swaps/:id/respond
 * @desc Respond to swap request (employee response - accept/decline)
 * @access Private (Employee)
 */
router.post(
  '/shift-swaps/:id/respond',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  validate(mobileShiftSwapValidator.respondToSwapRequest),
  mobileShiftSwapController.respondToSwapRequest
);

/**
 * @route POST /api/v1/mobile/shift-swaps/:id/execute
 * @desc Execute approved swap
 * @access Private (Manager/HR)
 */
router.post(
  '/shift-swaps/:id/execute',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  validate(mobileShiftSwapValidator.executeSwap),
  mobileShiftSwapController.executeSwap
);

// ==================== BULK OPERATIONS ====================

/**
 * @route POST /api/v1/mobile/shift-swaps/bulk-approve
 * @desc Bulk approve swap requests
 * @access Private (Manager/HR)
 */
router.post(
  '/shift-swaps/bulk-approve',
  validate(mobileShiftSwapValidator.bulkApproveSwaps),
  mobileShiftSwapController.bulkApproveSwaps
);

/**
 * @route PUT /api/v1/mobile/shift-swaps/approve-reject/:id
 * @desc Approve or reject swap request (unified endpoint)
 * @access Private (Manager/HR)
 */
router.put(
  '/shift-swaps/approve-reject/:id',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  validate(mobileShiftSwapValidator.approveRejectSwapRequest),
  mobileShiftSwapController.approveRejectSwapRequest
);

/**
 * @route PUT /api/v1/mobile/v2/shift-swaps/approve-reject
 * @desc Bulk approve or reject swap requests (v2)
 * @access Private (Manager/HR)
 */
router.put(
  '/v2/shift-swaps/approve-reject',
  validate(mobileShiftSwapValidator.bulkApproveRejectSwaps),
  mobileShiftSwapController.bulkApproveRejectSwaps
);

// ==================== SWAP DISCOVERY & ANALYTICS ====================

/**
 * @route GET /api/v1/mobile/available-swaps
 * @desc Get available shifts for swapping
 * @access Private (Employee)
 */
router.get(
  '/available-swaps',
  validateQuery(mobileShiftSwapValidator.getAvailableSwaps),
  mobileShiftSwapController.getAvailableSwaps
);

/**
 * @route GET /api/v1/mobile/my-swappable-shifts
 * @desc Get my swappable shifts
 * @access Private (Employee)
 */
router.get(
  '/my-swappable-shifts',
  validateQuery(mobileShiftSwapValidator.getMySwappableShifts),
  mobileShiftSwapController.getMySwappableShifts
);

/**
 * @route GET /api/v1/mobile/shift-swaps/pending-approvals
 * @desc Get pending approvals for current user
 * @access Private (Manager/HR)
 */
router.get(
  '/shift-swaps/pending-approvals',
  validateQuery(mobileShiftSwapValidator.getPendingApprovals),
  mobileShiftSwapController.getPendingApprovals
);

/**
 * @route GET /api/v1/mobile/shift-swaps/statistics
 * @desc Get swap statistics and analytics
 * @access Private (Employee)
 */
router.get(
  '/shift-swaps/statistics',
  validateQuery(mobileShiftSwapValidator.getSwapStatistics),
  mobileShiftSwapController.getSwapStatistics
);

/**
 * @route GET /api/v1/mobile/shift-swaps/:id/eligible-employees
 * @desc Get eligible employees for swap
 * @access Private (Employee)
 */
router.get(
  '/shift-swaps/:id/eligible-employees',
  validateParams(Joi.object({
    id: Joi.number().integer().positive().required()
  })),
  validateQuery(mobileShiftSwapValidator.getEligibleEmployees),
  mobileShiftSwapController.getEligibleEmployees
);

/**
 * @route GET /api/v1/mobile/target-employees
 * @desc Get all available target employees for shift swap
 * @access Private (Employee)
 */
router.get(
  '/target-employees',
  validateQuery(mobileShiftSwapValidator.getTargetEmployees),
  mobileShiftSwapController.getTargetEmployees
);

/**
 * @route GET /api/v1/mobile/target-employees/:date
 * @desc Get target employees available on specific date
 * @access Private (Employee)
 */
router.get(
  '/target-employees/:date',
  validateParams(Joi.object({
    date: Joi.date().iso().required()
  })),
  validateQuery(mobileShiftSwapValidator.getTargetEmployeesByDate),
  mobileShiftSwapController.getTargetEmployeesByDate
);

// ==================== NEW: SHIFT LOOKUP APIS ====================

/**
 * @route GET /api/v1/mobile/employee-shift/:date
 * @desc Get current employee's shift on specific date (employee ID from token)
 * @access Private (Employee)
 */
router.get(
  '/employee-shift/:date',
  validateParams(Joi.object({
    date: Joi.date().iso().required()
  })),
  mobileShiftSwapController.getEmployeeShiftByDate
);

/**
 * @route GET /api/v1/mobile/available-shifts/:date
 * @desc Get all available shifts on specific date
 * @access Private (Employee)
 */
router.get(
  '/available-shifts/:date',
  validateParams(Joi.object({
    date: Joi.date().iso().required()
  })),
  validateQuery(Joi.object({
    departmentId: Joi.number().integer().positive().optional(),
    designationId: Joi.number().integer().positive().optional(),
    includeAssignments: Joi.boolean().default(false)
  })),
  mobileShiftSwapController.getAvailableShiftsByDate
);

// ==================== LEGACY ENDPOINTS (for backward compatibility) ====================

/**
 * @route GET /api/v1/mobile/my-swap-requests
 * @desc Get my swap requests (sent and received) - Legacy endpoint
 * @access Private (Employee)
 */
router.get(
  '/my-swap-requests',
  validateQuery(mobileShiftSwapValidator.getMySwapRequests),
  mobileShiftSwapController.getMySwapRequests
);

/**
 * @route POST /api/v1/mobile/swap-requests
 * @desc Create swap request - Legacy endpoint
 * @access Private (Employee)
 */
router.post(
  '/swap-requests',
  validate(mobileShiftSwapValidator.createSwapRequest),
  mobileShiftSwapController.createSwapRequest
);

/**
 * @route GET /api/v1/mobile/swap-requests/:id
 * @desc Get swap request details - Legacy endpoint
 * @access Private (Employee)
 */
router.get(
  '/swap-requests/:id',
  validateParams(mobileShiftSwapValidator.idParam),
  mobileShiftSwapController.getSwapRequestDetails
);

/**
 * @route POST /api/v1/mobile/swap-requests/:id/respond
 * @desc Respond to swap request (accept/decline) - Legacy endpoint
 * @access Private (Employee)
 */
router.post(
  '/swap-requests/:id/respond',
  validateParams(mobileShiftSwapValidator.idParam),
  validate(mobileShiftSwapValidator.respondToSwapRequest),
  mobileShiftSwapController.respondToSwapRequest
);

/**
 * @route POST /api/v1/mobile/swap-requests/:id/cancel
 * @desc Cancel my swap request - Legacy endpoint
 * @access Private (Employee)
 */
router.post(
  '/swap-requests/:id/cancel',
  validateParams(mobileShiftSwapValidator.idParam),
  validate(mobileShiftSwapValidator.cancelSwapRequest),
  mobileShiftSwapController.cancelSwapRequest
);

module.exports = router;
