'use strict';

const express = require('express');
const mobileAuthRoutes = require('./auth.routes');
const mobileLeaveRoutes = require('./leave.routes');
const mobileAttendanceRoutes = require('./attendance.routes');
const mobileOvertimeRoutes = require('./overtime.routes');
const mobileDashboardRoutes = require('./dashboard.routes');
const mobileUserRoutes = require('./user.routes');
const mobileWfhRoutes = require('./wfh.routes');
const mobileExpenseRoutes = require('./expense.routes');
const mobileAssetRoutes = require('./asset.routes');
const mobileTimesheetRoutes = require('./timesheet.routes');
const mobileLocationRoutes = require('./location.routes');
const mobileCurrencyRoutes = require('./currency.routes');
const mobileFileUploadRoutes = require('./fileUpload.routes');
const mobileLopRoutes = require('./lop.routes');
const mobileEducationRoutes = require('./education.routes');
const mobileBankDetailRoutes = require('./bankDetail.routes');
const mobileWorkExperienceRoutes = require('./workExperience.routes');
const mobileDocumentRoutes = require('./document.routes');
const mobileShiftSwapRoutes = require('./shiftSwap.routes');

const router = express.Router();

// Mobile currency routes (public, no auth required)
router.use('/', mobileCurrencyRoutes);

// Mobile authentication routes (must come before user routes to avoid auth middleware conflicts)
router.use('/', mobileAuthRoutes);

// Mobile user routes
router.use('/', mobileUserRoutes);

// Mobile leave management routes
router.use('/', mobileLeaveRoutes);

// Mobile WFH routes
router.use('/', mobileWfhRoutes);

// Mobile expense routes
router.use('/', mobileExpenseRoutes);

// Mobile asset routes
router.use('/', mobileAssetRoutes);

// Mobile dashboard routes
router.use('/', mobileDashboardRoutes);

// Mobile attendance routes
router.use('/', mobileAttendanceRoutes);

// Mobile overtime routes
router.use('/', mobileOvertimeRoutes);

// Mobile timesheet routes
router.use('/', mobileTimesheetRoutes);

// Mobile location routes
router.use('/', mobileLocationRoutes);

// Mobile file upload routes
router.use('/', mobileFileUploadRoutes);

// Mobile LOP routes
router.use('/', mobileLopRoutes);

// Mobile education routes
router.use('/education', mobileEducationRoutes);

// Mobile bank detail routes
router.use('/', mobileBankDetailRoutes);

// Mobile work experience routes
router.use('/work-experience', mobileWorkExperienceRoutes);

// Mobile document routes
router.use('/', mobileDocumentRoutes);

// Mobile shift swap routes
router.use('/', mobileShiftSwapRoutes);

// 404 handler for mobile API routes
router.use((_, res) => {
  res.status(404).json({
    success: false,
    status_code: 404,
    message: 'Mobile API endpoint not found',
    result: {},
    time: Date.now()
  });
});

module.exports = router;
