'use strict';

const Joi = require('joi');

// Schedule creation validation (Enhanced for hybrid approach)
const createSchedule = Joi.object({
  name: Joi.string().min(3).max(100).required(),
  description: Joi.string().max(500).optional(),
  startDate: Joi.date().iso().required(),
  endDate: Joi.date().iso().required(),
  businessUnitId: Joi.number().integer().positive().optional(),
  departmentId: Joi.number().integer().positive().optional(),
  type: Joi.string().valid('manual', 'auto', 'template', 'hybrid').default('manual'),
  notes: Joi.string().max(1000).optional(),
  settings: Joi.object({
    allowOverlappingShifts: Joi.boolean().default(false),
    requireManagerApproval: Joi.boolean().default(true),
    autoNotifyEmployees: Joi.boolean().default(true),
    lockAfterPublish: Joi.boolean().default(true)
  }).optional(),

  // Enhanced: Hybrid shift sources support
  shiftSources: Joi.array().items(
    Joi.object({
      type: Joi.string().valid('template', 'rotaShift').required(),
      templateId: Joi.when('type', {
        is: 'template',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      rotaShiftId: Joi.when('type', {
        is: 'rotaShift',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      // ENHANCED: dates are optional for templates (auto-calculated from startDate/endDate)
      // Required only for rotaShift type for specific date selection
      dates: Joi.when('type', {
        is: 'template',
        then: Joi.array().items(Joi.date().iso()).min(1).optional(), // Optional for templates
        otherwise: Joi.array().items(Joi.date().iso()).min(1).required() // Required for rotaShift
      }),
      // Auto-calculate dates for templates (internal flag)
      autoCalculateDates: Joi.when('type', {
        is: 'template',
        then: Joi.boolean().default(true), // Auto-calculate for templates
        otherwise: Joi.forbidden()
      }),
      // Array format for customRequirements - consistent across all sources
      customRequirements: Joi.array().items(
        Joi.object({
          designationId: Joi.number().integer().positive().required(),
          requiredCount: Joi.number().integer().min(0).required()
        })
      ).optional()
    })
  ).optional(),

  // Enhanced: Employee assignments during creation (Template + RotaShift support)
  employeeAssignments: Joi.array().items(
    Joi.object({
      // Enhanced to support both template and direct assignments
      sourceType: Joi.string().valid('rotaShift', 'template').default('rotaShift'),

      // Template-specific fields (for template-based assignments)
      templateId: Joi.when('sourceType', {
        is: 'template',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      dayOfWeek: Joi.when('sourceType', {
        is: 'template',
        then: Joi.string().valid('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday').optional(),
        otherwise: Joi.forbidden()
      }),

      // Common fields (required for both types)
      rotaShiftId: Joi.number().integer().positive().required(),
      employeeId: Joi.number().integer().positive().required(),
      date: Joi.date().iso().required(), // Specific date for assignment
      designationId: Joi.number().integer().positive().required(),

      // Optional fields
      notes: Joi.string().max(500).optional(),
      assignmentType: Joi.string().valid('manual_assigned', 'template_assigned', 'auto_assigned', 'emergency_assigned').default('manual_assigned')
    })
  ).optional()
}).custom((value, helpers) => {
  if (new Date(value.endDate) <= new Date(value.startDate)) {
    return helpers.error('custom.endDateAfterStart');
  }

  const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
  if (daysDiff > 365) {
    return helpers.error('custom.maxDuration');
  }

  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date',
  'custom.maxDuration': 'Schedule duration cannot exceed 365 days'
});

// Schedule update validation (Enhanced for hybrid approach)
const updateSchedule = Joi.object({
  name: Joi.string().min(3).max(100).optional(),
  description: Joi.string().max(500).optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  businessUnitId: Joi.number().integer().positive().optional(),
  departmentId: Joi.number().integer().positive().optional(),
  type: Joi.string().valid('manual', 'auto', 'template', 'hybrid').optional(),
  status: Joi.string().valid('draft', 'published', 'archived', 'cancelled').optional(),
  notes: Joi.string().max(1000).optional(),
  settings: Joi.object({
    allowOverlappingShifts: Joi.boolean().optional(),
    requireManagerApproval: Joi.boolean().optional(),
    autoNotifyEmployees: Joi.boolean().optional(),
    lockAfterPublish: Joi.boolean().optional()
  }).optional(),

  // Enhanced: Complete replacement of shift sources
  shiftSources: Joi.array().items(
    Joi.object({
      type: Joi.string().valid('template', 'rotaShift').required(),
      templateId: Joi.when('type', {
        is: 'template',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      rotaShiftId: Joi.when('type', {
        is: 'rotaShift',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      // ENHANCED: dates are optional for templates (auto-calculated from schedule startDate/endDate)
      dates: Joi.when('type', {
        is: 'template',
        then: Joi.array().items(Joi.date().iso()).min(1).optional(), // Optional for templates
        otherwise: Joi.array().items(Joi.date().iso()).min(1).required() // Required for rotaShift
      }),
      // Auto-calculate dates for templates
      autoCalculateDates: Joi.when('type', {
        is: 'template',
        then: Joi.boolean().default(true),
        otherwise: Joi.forbidden()
      }),
      // Array format for customRequirements
      customRequirements: Joi.array().items(
        Joi.object({
          designationId: Joi.number().integer().positive().required(),
          requiredCount: Joi.number().integer().min(0).required()
        })
      ).optional()
    })
  ).optional(),

  // Enhanced: Incremental addition of shift sources
  addShiftSources: Joi.array().items(
    Joi.object({
      type: Joi.string().valid('template', 'rotaShift').required(),
      templateId: Joi.when('type', {
        is: 'template',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      rotaShiftId: Joi.when('type', {
        is: 'rotaShift',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      // ENHANCED: dates are optional for templates (auto-calculated from schedule startDate/endDate)
      dates: Joi.when('type', {
        is: 'template',
        then: Joi.array().items(Joi.date().iso()).min(1).optional(), // Optional for templates
        otherwise: Joi.array().items(Joi.date().iso()).min(1).required() // Required for rotaShift
      }),
      // Auto-calculate dates for templates
      autoCalculateDates: Joi.when('type', {
        is: 'template',
        then: Joi.boolean().default(true),
        otherwise: Joi.forbidden()
      }),
      // Array format for customRequirements
      customRequirements: Joi.array().items(
        Joi.object({
          designationId: Joi.number().integer().positive().required(),
          requiredCount: Joi.number().integer().min(0).required()
        })
      ).optional()
    })
  ).optional(),

  // Enhanced: Incremental removal of shift sources
  removeShiftSources: Joi.array().items(
    Joi.object({
      type: Joi.string().valid('template', 'rotaShift').required(),
      templateId: Joi.when('type', {
        is: 'template',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      rotaShiftId: Joi.when('type', {
        is: 'rotaShift',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      dates: Joi.array().items(Joi.date().iso()).optional()
    })
  ).optional(),

  // Enhanced: Employee assignments during update (Template + RotaShift support)
  employeeAssignments: Joi.array().items(
    Joi.object({
      // Enhanced to support both template and direct assignments
      type: Joi.string().valid('rotaShift', 'template').default('rotaShift'),

      // Template-specific fields
      templateId: Joi.when('type', {
        is: 'template',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden()
      }),
      dayOfWeek: Joi.when('type', {
        is: 'template',
        then: Joi.string().valid('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday').optional(),
        otherwise: Joi.forbidden()
      }),
      shiftInstanceId:Joi.when('type', {
        is: 'template',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden(),
      }),
      // Common fields
      rotaShiftId: Joi.when('type', {
        is: 'rotaShift',
        then: Joi.number().integer().positive().required(),
        otherwise: Joi.forbidden(),
      }),
      employeeId: Joi.number().integer().positive().required(),
      date: Joi.date().iso().required(),
      designationId: Joi.number().integer().positive().required(),

      // Optional fields
      notes: Joi.string().max(500).optional(),
      assignmentType: Joi.string().valid('manual_assigned', 'template_assigned', 'auto_assigned', 'emergency_assigned').default('manual_assigned')
    })
  ).optional(),

  // Enhanced: Granular assignment operations (ALTERNATIVE APPROACH)
  assignmentOperations: Joi.object({
    // Add new assignments
    add: Joi.array().items(
      Joi.object({
        rotaShiftId: Joi.number().integer().positive().required(),
        employeeId: Joi.number().integer().positive().required(),
        date: Joi.date().iso().required(),
        designationId: Joi.number().integer().positive().required(),
        notes: Joi.string().max(500).optional()
      })
    ).optional(),

    // Remove specific assignments
    remove: Joi.array().items(
      Joi.object({
        assignmentId: Joi.number().integer().positive().optional(),
        rotaShiftId: Joi.number().integer().positive().optional(),
        employeeId: Joi.number().integer().positive().optional(),
        date: Joi.date().iso().optional()
      }).or('assignmentId', 'rotaShiftId', 'employeeId', 'date')
    ).optional(),

    // Replace all assignments for specific dates
    replaceForDates: Joi.array().items(Joi.date().iso()).optional()
  }).optional()
}).min(1).custom((value, helpers) => {
  // Validate date logic
  if (value.endDate && value.startDate && new Date(value.endDate) <= new Date(value.startDate)) {
    return helpers.error('custom.endDateAfterStart');
  }

  // Validate that only one shift source operation is used
  const shiftSourceFields = [value.shiftSources, value.addShiftSources, value.removeShiftSources].filter(Boolean);
  if (shiftSourceFields.length > 1) {
    return helpers.error('custom.conflictingShiftSources');
  }

  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date',
  'custom.conflictingShiftSources': 'Cannot use multiple shift source operations simultaneously'
});

// Get all schedules with filtering (Enhanced for hybrid approach)
const getAllSchedules = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  status: Joi.string().valid('preview','draft', 'published', 'archived', 'cancelled').optional(),
  departmentId: Joi.number().integer().positive().optional(),
  startDate: Joi.date().iso().optional(),
  endDate: Joi.date().iso().optional(),
  search: Joi.string().max(100).optional(),
  sortBy: Joi.string().valid('name', 'startDate', 'endDate', 'status', 'type', 'createdAt', 'updatedAt').default('createdAt'),
  sortOrder: Joi.string().valid('ASC', 'DESC').default('DESC'),

  // Enhanced: Hybrid filtering options
  type: Joi.string().valid('manual', 'auto', 'template', 'hybrid').optional(),
  sourceType: Joi.string().valid('template', 'rotaShift', 'manual').optional(),
  sourceId: Joi.number().integer().positive().optional(),
  includeSourceStats: Joi.boolean().default(false),
  includeHybridMetadata: Joi.boolean().default(false)
}).custom((value, helpers) => {
  // Validate that sourceId requires sourceType
  if (value.sourceId && !value.sourceType) {
    return helpers.error('custom.sourceIdRequiresType');
  }

  return value;
}).messages({
  'custom.sourceIdRequiresType': 'sourceId requires sourceType to be specified'
});

// Get schedule by ID (Enhanced for hybrid approach)
const getScheduleById = Joi.object({
  includeInstances: Joi.boolean().default(false),
  includeAssignments: Joi.boolean().default(false),
  includeStatistics: Joi.boolean().default(false),

  // Enhanced: Hybrid-specific options
  includeSourceAnalysis: Joi.boolean().default(false),
  includeHybridMetadata: Joi.boolean().default(false),
  dateRange: Joi.object({
    startDate: Joi.date().iso().required(),
    endDate: Joi.date().iso().min(Joi.ref('startDate')).required()
  }).optional()
});

// Delete schedule
const deleteSchedule = Joi.object({
  force: Joi.boolean().default(false)
});

// Generate auto-schedule - ENHANCED for dual mode (existing schedule OR template-based)
const generateAutoSchedule = Joi.object({
  // MODE 1: Template-based generation (when no schedule ID in URL or creating new)
  templateMode: Joi.object({
    shiftTemplateId: Joi.number().integer().positive().required(),
    startDate: Joi.date().iso().required(),
    endDate: Joi.date().iso().greater(Joi.ref('startDate')).required(),
    scheduleCategory: Joi.string().valid('weekly', 'monthly', 'custom').default('weekly'),
    // Optional: User can provide custom name/description, otherwise auto-generated
    customName: Joi.string().min(3).max(100).optional(),
    customDescription: Joi.string().max(500).optional()
  }).optional(),

  // MODE 2: Demand forecasting-based generation (create new schedule from forecasts)
  forecastMode: Joi.object({
    startDate: Joi.date().iso().required(),
    endDate: Joi.date().iso().greater(Joi.ref('startDate')).required(),
    scheduleCategory: Joi.string().valid('weekly', 'monthly', 'custom').default('custom'),
    // Optional: User can provide custom name/description, otherwise auto-generated
    customName: Joi.string().min(3).max(100).optional(),
    customDescription: Joi.string().max(500).optional()
  }).optional(),

  // MODE 3: Existing schedule mode (current functionality)
  departmentIds: Joi.array().items(Joi.number().integer().positive()).min(1).optional(),

  // ENHANCED: Common constraints for both modes
  constraints: Joi.object({
    // Existing constraints
    autoAssignEmployees: Joi.boolean().default(true),
    respectAvailability: Joi.boolean().default(true),
    balanceWorkload: Joi.boolean().default(true),
    minimizeOvertime: Joi.boolean().default(true),
    maxConsecutiveDays: Joi.number().integer().min(1).max(14).default(14), // ✅ Updated
    minRestHours: Joi.number().integer().min(8).max(48).default(12),
    preferredEmployees: Joi.array().items(Joi.number().integer().positive()).optional(),
    excludedEmployees: Joi.array().items(Joi.number().integer().positive()).optional(),

    // ENHANCED: New designation-based constraints
    assignmentStrategy: Joi.string().valid('smart', 'rotation', 'balanced', 'skill_based').default('smart'),
    designationPriority: Joi.boolean().default(true),
    skillMatching: Joi.boolean().default(false),
    allowPartialAssignment: Joi.boolean().default(true),
    maxShiftsPerEmployee: Joi.number().integer().min(1).max(100).default(100), // ✅ Updated
    conflictResolution: Joi.string().valid('auto', 'manual', 'skip').default('auto'),

    // Assignment quotas
    minShiftsPerEmployee: Joi.number().integer().min(0).max(50).optional(),
    maxOvertimeHours: Joi.number().integer().min(0).max(20).default(10),

    // ✅ ENHANCED: Rotation frequency controls
    minDaysBetweenRotations: Joi.number().integer().min(1).max(14).default(3)
      .description('Minimum days between employee shift rotations'),

    maxRotationFrequency: Joi.number().min(0).max(1).default(0.5)
      .description('Maximum rotation frequency ratio (0.5 = 50% of shifts can be rotations)'),

    // ✅ ENHANCED: Long-term schedule controls
    enablePeriodicRebalancing: Joi.boolean().default(true)
      .description('Enable periodic workload rebalancing for long-term schedules'),

    rebalanceInterval: Joi.number().integer().min(3).max(30).default(5) // ✅ Updated
      .description('Days between workload rebalancing (for long-term schedules)'),

    // ✅ ENHANCED: Template gap handling
    handleTemplateGaps: Joi.boolean().default(true)
      .description('Handle weekends/holidays gaps in assignment logic'),

    // Team composition
    seniorEmployeeRatio: Joi.number().min(0).max(1).default(0.3),
    crossTrainedRequired: Joi.number().integer().min(0).max(5).default(0),

    // Flexibility options
    autoCreateWaitlist: Joi.boolean().default(false),
    notifyUnassigned: Joi.boolean().default(true),

    // Template-specific constraints (existing)
    useDesignationOverrides: Joi.boolean().default(true),
    applyDayTypeRules: Joi.boolean().default(true)
  }).default({
    // ✅ DEFAULT CONSTRAINTS - Applied when constraints not provided
    autoAssignEmployees: true,
    respectAvailability: true,
    balanceWorkload: true,
    minimizeOvertime: true,
    maxConsecutiveDays: 14,
    minRestHours: 12,
    assignmentStrategy: 'smart',
    designationPriority: true,
    skillMatching: false,
    allowPartialAssignment: true,
    maxShiftsPerEmployee: 100,
    conflictResolution: 'auto',
    maxOvertimeHours: 10,
    minDaysBetweenRotations: 3,
    maxRotationFrequency: 0.5,
    enablePeriodicRebalancing: true,
    rebalanceInterval: 5,
    handleTemplateGaps: true,
    seniorEmployeeRatio: 0.3,
    crossTrainedRequired: 0,
    autoCreateWaitlist: false,
    notifyUnassigned: true,
    useDesignationOverrides: true,
    applyDayTypeRules: true
  }).custom((value, helpers) => {
    // ✅ HANDLE EMPTY OBJECT - Apply defaults when empty object is passed
    if (value && typeof value === 'object' && Object.keys(value).length === 0) {
      return {
        autoAssignEmployees: true,
        respectAvailability: true,
        balanceWorkload: true,
        minimizeOvertime: true,
        maxConsecutiveDays: 14, // ✅ Updated value
        minRestHours: 12,
        assignmentStrategy: 'smart',
        designationPriority: true,
        skillMatching: false,
        allowPartialAssignment: true,
        maxShiftsPerEmployee: 100, // ✅ Updated value
        conflictResolution: 'auto',
        maxOvertimeHours: 10,
        minDaysBetweenRotations: 3,
        maxRotationFrequency: 0.5,
        enablePeriodicRebalancing: true,
        rebalanceInterval: 5, // ✅ Updated value
        handleTemplateGaps: true,
        seniorEmployeeRatio: 0.3,
        crossTrainedRequired: 0,
        autoCreateWaitlist: false,
        notifyUnassigned: true,
        useDesignationOverrides: true,
        applyDayTypeRules: true
      };
    }
    return value;
  }),

  // Demand forecasting (common for both modes)
  demandForecast: Joi.array().items(
    Joi.object({
      date: Joi.date().iso().required(),
      departmentId: Joi.number().integer().positive().optional(),
      shiftTemplateId: Joi.number().integer().positive().optional(),
      rotaShiftId: Joi.number().integer().positive().optional(),
      requiredCount: Joi.number().integer().min(1).max(50).required(),
      confidence: Joi.string().valid('low', 'medium', 'high').default('medium'),
      source: Joi.string().max(50).optional(),
      designationOverrides: Joi.array().items(
        Joi.object({
          designationId: Joi.number().integer().positive().required(),
          requiredCount: Joi.number().integer().min(0).max(20).required()
        })
      ).optional()
    })
  ).optional(),

  enableForecastOverride: Joi.boolean().default(false)
}).custom((value, helpers) => {
  // Validation logic for three modes
  const hasTemplateMode = value.templateMode;
  const hasForecastMode = value.forecastMode;
  const hasDepartmentIds = value.departmentIds && value.departmentIds.length > 0;

  // Count how many modes are provided
  const modeCount = [hasTemplateMode, hasForecastMode, hasDepartmentIds].filter(Boolean).length;

  // Only one mode should be provided
  if (modeCount > 1) {
    return helpers.error('custom.conflictingModes');
  }

  // At least one mode must be provided
  if (modeCount === 0) {
    return helpers.error('custom.missingMode');
  }

  return value;
}).messages({
  'custom.conflictingModes': 'Cannot use multiple modes simultaneously. Choose either templateMode, forecastMode, or departmentIds.',
  'custom.missingMode': 'One of templateMode (template-based), forecastMode (forecast-based), or departmentIds (existing schedule) must be provided.'
});

// ✅ ENHANCED: Approve auto-generated schedule preview
const approvePreview = Joi.object({
  notes: Joi.string().max(500).optional()
});

// Publish schedule
const publishSchedule = Joi.object({
  publishDate: Joi.date().iso().optional(),
  notifyEmployees: Joi.boolean().default(true),
  lockAssignments: Joi.boolean().default(true),
  publishNotes: Joi.string().max(500).optional(),
  skipValidation: Joi.boolean().default(false),
  forcePublish: Joi.boolean().default(false)
});

// Archive schedule
const archiveSchedule = Joi.object({
  reason: Joi.string().min(10).max(500).required()
});

// Clone schedule
const cloneSchedule = Joi.object({
  name: Joi.string().min(3).max(100).required(),
  startDate: Joi.date().iso().required(),
  endDate: Joi.date().iso().required(),
  includeAssignments: Joi.boolean().default(false),
  modifications: Joi.object({
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid('manual', 'auto_generated', 'template_based').optional(),
    departmentId: Joi.number().integer().positive().optional(), // ✅ Single department
    settings: Joi.object({
      allowOverlappingShifts: Joi.boolean().optional(),
      requireManagerApproval: Joi.boolean().optional(),
      autoNotifyEmployees: Joi.boolean().optional(),
      lockAfterPublish: Joi.boolean().optional()
    }).optional()
  }).optional()
}).custom((value, helpers) => {
  if (new Date(value.endDate) <= new Date(value.startDate)) {
    return helpers.error('custom.endDateAfterStart');
  }

  const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
  if (daysDiff > 365) {
    return helpers.error('custom.maxDuration');
  }

  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date',
  'custom.maxDuration': 'Schedule duration cannot exceed 365 days'
});

// Generate instances
const generateInstances = Joi.object({
  templateIds: Joi.array().items(Joi.number().integer().positive()).min(1).required(),
  startDate: Joi.date().iso().required(),
  endDate: Joi.date().iso().required(),
  enableForecastOverride: Joi.boolean().default(true),
  minConfidenceThreshold: Joi.number().min(0).max(100).default(70)
}).custom((value, helpers) => {
  if (new Date(value.endDate) <= new Date(value.startDate)) {
    return helpers.error('custom.endDateAfterStart');
  }
  return value;
}).messages({
  'custom.endDateAfterStart': 'End date must be after start date'
});

// Auto-assign employees
const autoAssignEmployees = Joi.object({
  respectAvailability: Joi.boolean().default(true),
  balanceWorkload: Joi.boolean().default(true),
  minimizeOvertime: Joi.boolean().default(true),
  maxConsecutiveDays: Joi.number().integer().min(1).max(14).default(7),
  minRestHours: Joi.number().min(0).max(24).default(12),
  preferredEmployees: Joi.array().items(Joi.number().integer().positive()).default([]),
  excludedEmployees: Joi.array().items(Joi.number().integer().positive()).default([])
});

// Validate schedule
const validateSchedule = Joi.object({
  checkConflicts: Joi.boolean().default(true),
  checkCoverage: Joi.boolean().default(true)
});

// Cancel schedule
const cancelSchedule = Joi.object({
  reason: Joi.string().min(10).max(500).required(),
  notifyEmployees: Joi.boolean().default(true)
});

// Get schedule conflicts
const getScheduleConflicts = Joi.object({
  conflictType: Joi.string().valid('availability', 'overlap', 'skill_mismatch', 'overtime').optional(),
  severity: Joi.string().valid('low', 'medium', 'high', 'critical').optional()
});

// Get schedule coverage
const getScheduleCoverage = Joi.object({
  departmentId: Joi.number().integer().positive().optional(),
  date: Joi.date().iso().optional()
});

// Bulk assign employees (Enhanced for scale)
const bulkAssignEmployees = Joi.object({
  assignments: Joi.array().items(
    Joi.object({
      shiftInstanceId: Joi.number().integer().positive().required(),
      employeeId: Joi.number().integer().positive().required(),
      priority: Joi.number().integer().min(0).max(10).default(0),
      notes: Joi.string().max(500).optional()
    })
  ).min(1).max(1000).required(), // Limit to 1000 assignments per batch
  strategy: Joi.string().valid('manual', 'auto_resolve_conflicts', 'skip_conflicts').default('manual')
});

// Update shift instance requirements
const updateShiftInstanceRequirements = Joi.object({
  designationRequirements: Joi.array().items(
    Joi.object({
      designationId: Joi.number().integer().positive().required(),
      requiredCount: Joi.number().integer().min(0).max(100).required(),
      assignedCount: Joi.number().integer().min(0).optional(),
      priority: Joi.number().integer().min(0).max(10).default(0)
    })
  ).min(1).required(),
  customRequirements: Joi.object().pattern(
    Joi.number().integer().positive(),
    Joi.alternatives().try(
      Joi.number().integer().min(0),
      Joi.object()
    )
  ).optional(),
  status: Joi.string().valid('draft', 'open', 'partial', 'full', 'published', 'locked', 'completed', 'cancelled').optional()
});

module.exports = {
  // Core CRUD
  createSchedule,
  updateSchedule,
  getAllSchedules,
  getScheduleById,
  deleteSchedule,

  // Enhanced Schedule Management (Hybrid Approach)
  bulkAssignEmployees,
  updateShiftInstanceRequirements,

  // Schedule Workflow
  generateAutoSchedule,
  approvePreview, // ✅ ENHANCED: Approve auto-generated schedule preview
  publishSchedule,
  archiveSchedule,
  cloneSchedule,
  generateInstances,
  autoAssignEmployees,
  validateSchedule,
  cancelSchedule,
  getScheduleConflicts,
  getScheduleCoverage
};
