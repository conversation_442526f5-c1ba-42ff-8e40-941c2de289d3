'use strict';

/**
 * Mobile Shift Swap Validators - PRD Implementation
 * 
 * Validation schemas for mobile shift swap management
 */

const Joi = require('joi');

// Common validation schemas
const idParam = Joi.object({
    id: Joi.number().integer().positive().required()
  })
};

// Get my swap requests
const getMySwapRequests =Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'executed', 'cancelled').optional(),
    type: Joi.string().valid('sent', 'received', 'all').default('all')
  })


// Create swap request
const createSwapRequest = Joi.object({
    targetAssignmentId: Joi.number().integer().positive().optional(),
    targetEmployeeId: Joi.number().integer().positive().optional(),
    swapType: Joi.string().valid('direct', 'open').default('direct'),
    reason: Joi.string().min(10).max(500).required(),
    notes: Joi.string().max(500).optional(),
    urgency: Joi.string().valid('low', 'medium', 'high').default('medium')
  }).custom((value, helpers) => {
    // For direct swaps, either targetAssignmentId or targetEmployeeId must be provided
    if (value.swapType === 'direct') {
      if (!value.targetAssignmentId && !value.targetEmployeeId) {
        return helpers.error('custom.targetRequired');
      }
    }
    
    // Cannot have both targetAssignmentId and targetEmployeeId
    if (value.targetAssignmentId && value.targetEmployeeId) {
      return helpers.error('custom.onlyOneTarget');
    }
    
    return value;
  }).messages({
    'custom.targetRequired': 'Either targetAssignmentId or targetEmployeeId is required for direct swaps',
    'custom.onlyOneTarget': 'Cannot specify both targetAssignmentId and targetEmployeeId'
  })

// Respond to swap request
const respondToSwapRequest =  Joi.object({
    action: Joi.string().valid('accept', 'decline').required(),
    notes: Joi.string().max(500).optional()
  })


// Cancel swap request
const cancelSwapRequest =  Joi.object({
    reason: Joi.string().min(10).max(500).required()
  })


// Get available swaps
const getAvailableSwaps =  Joi.object({
    myShiftId: Joi.number().integer().positive().optional(),
    startDate: Joi.date().iso().default(() => new Date().toISOString().split('T')[0]),
    endDate: Joi.date().iso().default(() => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0];
    }),
    departmentId: Joi.number().integer().positive().optional(),
    limit: Joi.number().integer().min(1).max(50).default(20)
  }).custom((value, helpers) => {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
    
    // Limit date range to 60 days for mobile
    const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
    if (daysDiff > 60) {
      return helpers.error('custom.maxDateRange');
    }
    
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date',
    'custom.maxDateRange': 'Date range cannot exceed 60 days'
  })


// Get my swappable shifts
const getMySwappableShifts =  Joi.object({
    startDate: Joi.date().iso().default(() => new Date().toISOString().split('T')[0]),
    endDate: Joi.date().iso().default(() => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0];
    }),
    limit: Joi.number().integer().min(1).max(50).default(20)
  }).custom((value, helpers) => {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
    
    // Limit date range to 60 days for mobile
    const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
    if (daysDiff > 60) {
      return helpers.error('custom.maxDateRange');
    }
    
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date',
    'custom.maxDateRange': 'Date range cannot exceed 60 days'
  })


// Get all swap requests
const getAllSwapRequests =  Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'executed', 'cancelled').optional(),
    requesterId: Joi.number().integer().positive().optional(),
    targetId: Joi.number().integer().positive().optional(),
    departmentId: Joi.number().integer().positive().optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    search: Joi.string().max(100).optional(),
    sortBy: Joi.string().valid('createdAt', 'requestDate', 'status', 'urgency').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  })


// Update swap request
const updateSwapRequest = Joi.object({
    reason: Joi.string().min(10).max(500).optional(),
    notes: Joi.string().max(500).optional(),
    urgency: Joi.string().valid('low', 'medium', 'high').optional()
  })


// Approve swap request
const approveSwapRequest = Joi.object({
    comments: Joi.string().max(500).optional(),
    notes: Joi.string().max(500).optional()
  })

// Reject swap request
const rejectSwapRequest =  Joi.object({
    rejectionReason: Joi.string().min(5).max(500).required(),
    comments: Joi.string().max(500).optional()
  })


// Execute swap
const executeSwap =  Joi.object({
    notes: Joi.string().max(500).optional(),
    executedAt: Joi.date().iso().default(() => new Date())
  })


// Bulk approve swaps
const bulkApproveSwaps = Joi.object({
    swapRequestIds: Joi.array().items(Joi.number().integer().positive()).min(1).max(50).required(),
    comments: Joi.string().max(500).optional()
  })


// Approve/Reject swap request (unified)
const approveRejectSwapRequest = Joi.object({
    status: Joi.string().valid('Approved', 'Rejected').required(),
    comment: Joi.string().max(500).optional(),
    rejectionReason: Joi.string().max(500).optional()
  })


// Bulk approve/reject swaps
const bulkApproveRejectSwaps = Joi.object({
    swapRequestIds: Joi.array().items(Joi.number().integer().positive()).min(1).max(50).required(),
    status: Joi.string().valid('Approved', 'Rejected').required(),
    comment: Joi.string().max(500).optional(),
    rejectionReason: Joi.string().max(500).optional()
  })


// Get pending approvals
const getPendingApprovals = 
  query: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20),
    departmentId: Joi.number().integer().positive().optional(),
    urgency: Joi.string().valid('low', 'medium', 'high').optional()
  })


// Get swap statistics
const getSwapStatistics =  Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    departmentId: Joi.number().integer().positive().optional()
  })

// Get eligible employees
const getEligibleEmployees = Joi.object({
    departmentId: Joi.number().integer().positive().optional(),
    shiftType: Joi.string().optional(),
    limit: Joi.number().integer().min(1).max(100).default(50)
  })


// Get employee shift swaps (for approval)
const getEmployeeShiftSwaps = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    size: Joi.number().integer().min(1).max(50).default(10),
    swap_status: Joi.string().valid('pending', 'approved', 'rejected').default('pending'),
    search: Joi.string().max(100).optional()
  })


module.exports = {
  idParam,
  getMySwapRequests,
  getAllSwapRequests,
  createSwapRequest,
  updateSwapRequest,
  respondToSwapRequest,
  cancelSwapRequest,
  approveSwapRequest,
  rejectSwapRequest,
  executeSwap,
  bulkApproveSwaps,
  approveRejectSwapRequest,
  bulkApproveRejectSwaps,
  getAvailableSwaps,
  getMySwappableShifts,
  getPendingApprovals,
  getSwapStatistics,
  getEligibleEmployees,
  getEmployeeShiftSwaps
};
