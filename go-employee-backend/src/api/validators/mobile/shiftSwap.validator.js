'use strict';

/**
 * Mobile Shift Swap Validators - PRD Implementation
 * 
 * Validation schemas for mobile shift swap management
 */

const Joi = require('joi');

// Common validation schemas
const idParam = Joi.object({
    id: Joi.number().integer().positive().required()
  })

// Get my swap requests
const getMySwapRequests =Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'executed', 'cancelled').optional(),
    type: Joi.string().valid('sent', 'received', 'all').default('all')
  })


// ✅ UPDATED: Create swap request - Matching web validator
const createSwapRequest = Joi.object({
  // ✅ REQUESTER INFORMATION - Auto-set from tenant context in controller
  requesterId: Joi.number().integer().positive().optional()
    .description('Employee ID who is requesting the swap (auto-set from context)'),

  // ✅ CURRENT SHIFT TO SWAP - Enhanced validation (matching web)
  currentShiftAssignmentId: Joi.number().integer().positive().required()
    .messages({
      'any.required': 'Current shift assignment ID is required',
      'number.positive': 'Current shift assignment ID must be a positive number',
      'number.base': 'Current shift assignment ID must be a valid number'
    })
    .description('Assignment ID of the shift they want to swap away'),

  // ✅ MOBILE COMPATIBILITY - Also accept mobile field names
  requesterAssignmentId: Joi.number().integer().positive().optional()
    .description('Mobile compatibility: Assignment ID of requester shift'),

  // ✅ SWAP DATE - Enhanced validation (matching web)
  swapDate: Joi.date().iso().min('now').required()
    .messages({
      'any.required': 'Swap date is required',
      'date.min': 'Swap date cannot be in the past',
      'date.base': 'Swap date must be a valid date',
      'date.format': 'Swap date must be in ISO format (YYYY-MM-DD)'
    })
    .description('Date when the swap should happen'),

  // ✅ DESIRED SHIFT - Default to null if not provided
  desiredShiftId: Joi.number().integer().positive().optional().allow(null).default(null)
    .description('Specific shift they want (if they have preference)'),

  // ✅ TARGET EMPLOYEE - Default to null if not provided
  targetEmployeeId: Joi.number().integer().positive().optional().allow(null).default(null)
    .description('Specific employee to swap with (if they have someone in mind)'),

  // ✅ MOBILE COMPATIBILITY - Target assignment
  targetAssignmentId: Joi.number().integer().positive().optional().allow(null).default(null)
    .description('Mobile compatibility: Target assignment ID'),

  // ✅ FLEXIBLE SWAP OPTIONS - Defaults handled by Joi
  swapWithAnyAvailableEmployee: Joi.boolean().default(false)
    .description('Accept swap with any available employee'),

  swapWithAnyAvailableShift: Joi.boolean().default(false)
    .description('Accept any available shift (flexible about timing)'),

  // ✅ MOBILE COMPATIBILITY - Swap type
  swapType: Joi.string().valid('direct', 'open').default('direct')
    .description('Mobile compatibility: Type of swap'),

  // ✅ REASON & URGENCY - Enhanced validation (matching web)
  reasonForSwap: Joi.string().valid(
    'personal_emergency',
    'medical_appointment',
    'family_commitment',
    'education_training',
    'transportation_issue',
    'work_life_balance',
    'other'
  ).optional()
    .description('Category of reason for swap'),

  reasonDescription: Joi.string().min(10).max(500).optional()
    .description('Detailed explanation of why swap is needed'),

  // ✅ MOBILE COMPATIBILITY - Simple reason field
  reason: Joi.string().min(10).max(500).optional()
    .description('Mobile compatibility: Simple reason field'),

  urgency: Joi.string().valid('low', 'medium', 'high', 'critical').default('medium')
    .messages({
      'any.only': 'Urgency must be one of: low, medium, high, critical'
    })
    .description('Urgency level of the swap request'),

  // ✅ ADDITIONAL INFORMATION - Defaults handled by Joi
  notes: Joi.string().max(1000).optional().allow(null).default(null)
    .description('Additional notes or special requirements'),

  isRecurring: Joi.boolean().default(false)
    .description('Whether this is a recurring swap request'),

  recurringPattern: Joi.when('isRecurring', {
    is: true,
    then: Joi.object({
      frequency: Joi.string().valid('weekly', 'bi-weekly', 'monthly').required(),
      endDate: Joi.date().iso().min(Joi.ref('swapDate')).required(),
      specificDays: Joi.array().items(Joi.number().integer().min(0).max(6)).optional()
        .description('Days of week (0=Sunday, 6=Saturday)')
    }).required(),
    otherwise: Joi.forbidden()
  })

}).custom((value, helpers) => {
  // ✅ FIELD COMPATIBILITY - Map mobile fields to web fields
  if (value.requesterAssignmentId && !value.currentShiftAssignmentId) {
    value.currentShiftAssignmentId = value.requesterAssignmentId;
  }

  if (value.reason && !value.reasonDescription) {
    value.reasonDescription = value.reason;
    value.reasonForSwap = 'other'; // Default category
  }

  // ✅ VALIDATION - At least one swap option must be specified
  const hasSpecificTarget = value.targetEmployeeId || value.desiredShiftId || value.targetAssignmentId;
  const hasFlexibleOption = value.swapWithAnyAvailableEmployee || value.swapWithAnyAvailableShift;

  if (!hasSpecificTarget && !hasFlexibleOption) {
    return helpers.error('custom.noSwapOptionSpecified');
  }

  // ✅ VALIDATION - Cannot have conflicting options
  if (value.targetEmployeeId && value.swapWithAnyAvailableEmployee) {
    return helpers.error('custom.conflictingEmployeeOptions');
  }

  if (value.desiredShiftId && value.swapWithAnyAvailableShift) {
    return helpers.error('custom.conflictingShiftOptions');
  }

  // ✅ VALIDATION - Recurring pattern
  if (value.isRecurring && value.recurringPattern) {
    const swapDate = new Date(value.swapDate);
    const endDate = new Date(value.recurringPattern.endDate);
    const daysDiff = Math.ceil((endDate - swapDate) / (1000 * 60 * 60 * 24));

    if (daysDiff > 365) {
      return helpers.error('custom.recurringTooLong');
    }
  }

  return value;
}).messages({
  'custom.noSwapOptionSpecified': 'At least one swap option must be specified (target employee, desired shift, or flexible options)',
  'custom.conflictingEmployeeOptions': 'Cannot specify both targetEmployeeId and swapWithAnyAvailableEmployee',
  'custom.conflictingShiftOptions': 'Cannot specify both desiredShiftId and swapWithAnyAvailableShift',
  'custom.recurringTooLong': 'Recurring pattern cannot exceed 365 days'
})



// Respond to swap request
const respondToSwapRequest =  Joi.object({
    action: Joi.string().valid('accept', 'decline').required(),
    notes: Joi.string().max(500).optional()
  })


// Cancel swap request
const cancelSwapRequest =  Joi.object({
    reason: Joi.string().min(10).max(500).required()
  })


// Get available swaps
const getAvailableSwaps =  Joi.object({
    myShiftId: Joi.number().integer().positive().optional(),
    startDate: Joi.date().iso().default(() => new Date().toISOString().split('T')[0]),
    endDate: Joi.date().iso().default(() => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0];
    }),
    departmentId: Joi.number().integer().positive().optional(),
    limit: Joi.number().integer().min(1).max(50).default(20)
  }).custom((value, helpers) => {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
    
    // Limit date range to 60 days for mobile
    const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
    if (daysDiff > 60) {
      return helpers.error('custom.maxDateRange');
    }
    
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date',
    'custom.maxDateRange': 'Date range cannot exceed 60 days'
  })


// Get my swappable shifts
const getMySwappableShifts =  Joi.object({
    startDate: Joi.date().iso().default(() => new Date().toISOString().split('T')[0]),
    endDate: Joi.date().iso().default(() => {
      const date = new Date();
      date.setDate(date.getDate() + 30);
      return date.toISOString().split('T')[0];
    }),
    limit: Joi.number().integer().min(1).max(50).default(20)
  }).custom((value, helpers) => {
    if (new Date(value.endDate) <= new Date(value.startDate)) {
      return helpers.error('custom.endDateAfterStart');
    }
    
    // Limit date range to 60 days for mobile
    const daysDiff = Math.ceil((new Date(value.endDate) - new Date(value.startDate)) / (1000 * 60 * 60 * 24));
    if (daysDiff > 60) {
      return helpers.error('custom.maxDateRange');
    }
    
    return value;
  }).messages({
    'custom.endDateAfterStart': 'End date must be after start date',
    'custom.maxDateRange': 'Date range cannot exceed 60 days'
  })


// Get all swap requests
const getAllSwapRequests =  Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20),
    status: Joi.string().valid('pending', 'approved', 'rejected', 'executed', 'cancelled').optional(),
    requesterId: Joi.number().integer().positive().optional(),
    targetId: Joi.number().integer().positive().optional(),
    departmentId: Joi.number().integer().positive().optional(),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    search: Joi.string().max(100).optional(),
    sortBy: Joi.string().valid('createdAt', 'requestDate', 'status', 'urgency').default('createdAt'),
    sortOrder: Joi.string().valid('asc', 'desc').default('desc')
  })


// ✅ UPDATED: Update swap request - Matching web validator
const updateSwapRequest = Joi.object({
  // SWAP DATE (can be updated if not yet processed)
  swapDate: Joi.date().iso().min('now').optional()
    .description('Updated date when the swap should happen'),

  // DESIRED SHIFT (can change preference)
  desiredShiftId: Joi.number().integer().positive().optional()
    .description('Updated desired shift preference'),

  // TARGET EMPLOYEE (can change target)
  targetEmployeeId: Joi.number().integer().positive().optional()
    .description('Updated target employee'),

  // ✅ MOBILE COMPATIBILITY - Target assignment
  targetAssignmentId: Joi.number().integer().positive().optional()
    .description('Mobile compatibility: Updated target assignment ID'),

  // FLEXIBLE SWAP OPTIONS (can toggle these)
  swapWithAnyAvailableEmployee: Joi.boolean().optional()
    .description('Updated preference for any available employee'),

  swapWithAnyAvailableShift: Joi.boolean().optional()
    .description('Updated preference for any available shift'),

  // REASON UPDATES
  reasonForSwap: Joi.string().valid(
    'personal_emergency', 'medical_appointment', 'family_commitment',
    'education_training', 'transportation_issue', 'work_life_balance', 'other'
  ).optional().description('Updated reason category'),

  reasonDescription: Joi.string().min(10).max(500).optional()
    .description('Updated detailed reason'),

  // ✅ MOBILE COMPATIBILITY - Simple reason field
  reason: Joi.string().min(10).max(500).optional()
    .description('Mobile compatibility: Updated simple reason field'),

  // URGENCY (can be updated)
  urgency: Joi.string().valid('low', 'medium', 'high', 'critical').optional()
    .description('Updated urgency level'),

  // ADDITIONAL NOTES
  notes: Joi.string().max(1000).optional()
    .description('Additional notes or updates'),

  // RECURRING PATTERN UPDATES
  isRecurring: Joi.boolean().optional(),
  recurringPattern: Joi.when('isRecurring', {
    is: true,
    then: Joi.object({
      frequency: Joi.string().valid('weekly', 'bi-weekly', 'monthly').required(),
      endDate: Joi.date().iso().required(),
      specificDays: Joi.array().items(Joi.number().integer().min(0).max(6)).optional()
    }).required(),
    otherwise: Joi.forbidden()
  }),

  // NOTIFICATION PREFERENCES
  notificationPreferences: Joi.object({
    emailNotification: Joi.boolean().optional(),
    smsNotification: Joi.boolean().optional(),
    pushNotification: Joi.boolean().optional()
  }).optional()

}).min(1).custom((value, helpers) => {
  // ✅ FIELD COMPATIBILITY - Map mobile fields to web fields
  if (value.reason && !value.reasonDescription) {
    value.reasonDescription = value.reason;
    if (!value.reasonForSwap) {
      value.reasonForSwap = 'other'; // Default category
    }
  }

  // Same validation logic as create
  if (value.targetEmployeeId && value.swapWithAnyAvailableEmployee) {
    return helpers.error('custom.conflictingEmployeeOptions');
  }

  if (value.desiredShiftId && value.swapWithAnyAvailableShift) {
    return helpers.error('custom.conflictingShiftOptions');
  }

  // If updating recurring pattern, validate dates
  if (value.isRecurring && value.recurringPattern && value.swapDate) {
    const swapDate = new Date(value.swapDate);
    const endDate = new Date(value.recurringPattern.endDate);
    const daysDiff = Math.ceil((endDate - swapDate) / (1000 * 60 * 60 * 24));

    if (daysDiff > 365) {
      return helpers.error('custom.recurringTooLong');
    }
  }

  return value;
}).messages({
  'custom.conflictingEmployeeOptions': 'Cannot specify both targetEmployeeId and swapWithAnyAvailableEmployee',
  'custom.conflictingShiftOptions': 'Cannot specify both desiredShiftId and swapWithAnyAvailableShift',
  'custom.recurringTooLong': 'Recurring pattern cannot exceed 365 days'
})


// ✅ UPDATED: Approve swap request - Matching web validator
const approveSwapRequest = Joi.object({
  notes: Joi.string().max(500).optional()
    .description('Approval notes or comments'),

  // ✅ MOBILE COMPATIBILITY - Also accept comments field
  comments: Joi.string().max(500).optional()
    .description('Mobile compatibility: Approval comments'),

  executeImmediately: Joi.boolean().default(true)
    .description('Auto-execute swap upon approval (default: true)'),

  executionNotes: Joi.string().max(500).optional()
    .description('Additional notes for execution process')
}).custom((value, helpers) => {
  // ✅ FIELD COMPATIBILITY - Map mobile fields to web fields
  if (value.comments && !value.notes) {
    value.notes = value.comments;
  }
  return value;
})

// ✅ UPDATED: Reject swap request - Matching web validator
const rejectSwapRequest = Joi.object({
  reason: Joi.string().valid(
    'policy_violation',
    'skill_mismatch',
    'scheduling_conflict',
    'insufficient_coverage',
    'other'
  ).required(),

  notes: Joi.string().max(500).optional(),

  // ✅ MOBILE COMPATIBILITY - Also accept old field names
  rejectionReason: Joi.string().min(5).max(500).optional()
    .description('Mobile compatibility: Rejection reason'),

  comments: Joi.string().max(500).optional()
    .description('Mobile compatibility: Rejection comments')
}).custom((value, helpers) => {
  // ✅ FIELD COMPATIBILITY - Map mobile fields to web fields
  if (value.rejectionReason && !value.reason) {
    value.reason = 'other'; // Default category
    value.notes = value.rejectionReason;
  }
  if (value.comments && !value.notes) {
    value.notes = value.comments;
  }
  return value;
})


// Execute swap
const executeSwap =  Joi.object({
    notes: Joi.string().max(500).optional(),
    executedAt: Joi.date().iso().default(() => new Date())
  })


// Bulk approve swaps
const bulkApproveSwaps = Joi.object({
    swapRequestIds: Joi.array().items(Joi.number().integer().positive()).min(1).max(50).required(),
    comments: Joi.string().max(500).optional()
  })


// Approve/Reject swap request (unified)
const approveRejectSwapRequest = Joi.object({
    status: Joi.string().valid('Approved', 'Rejected').required(),
    comment: Joi.string().max(500).optional(),
    rejectionReason: Joi.string().max(500).optional()
  })


// Bulk approve/reject swaps
const bulkApproveRejectSwaps = Joi.object({
    swapRequestIds: Joi.array().items(Joi.number().integer().positive()).min(1).max(50).required(),
    status: Joi.string().valid('Approved', 'Rejected').required(),
    comment: Joi.string().max(500).optional(),
    rejectionReason: Joi.string().max(500).optional()
  })


// Get pending approvals
const getPendingApprovals =  Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20),
    departmentId: Joi.number().integer().positive().optional(),
    urgency: Joi.string().valid('low', 'medium', 'high').optional()
  })


// Get swap statistics
const getSwapStatistics =  Joi.object({
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    departmentId: Joi.number().integer().positive().optional()
  })

// Get eligible employees
const getEligibleEmployees = Joi.object({
    departmentId: Joi.number().integer().positive().optional(),
    shiftType: Joi.string().optional(),
    limit: Joi.number().integer().min(1).max(100).default(50)
  })


// Get employee shift swaps (for approval)
const getEmployeeShiftSwaps = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    size: Joi.number().integer().min(1).max(50).default(10),
    swap_status: Joi.string().valid('pending', 'approved', 'rejected').default('pending'),
    search: Joi.string().max(100).optional()
  })


// ✅ NEW: Get target employees
const getTargetEmployees = Joi.object({
  designation_id: Joi.number().integer().positive().optional(),
  department_id: Joi.number().integer().positive().optional(),
  date: Joi.date().iso().optional(),
  exclude_self: Joi.boolean().default(true)
});

// ✅ NEW: Get target employees by date
const getTargetEmployeesByDate = Joi.object({
  designation_id: Joi.number().integer().positive().optional(),
  department_id: Joi.number().integer().positive().optional(),
  exclude_self: Joi.boolean().default(true)
});

module.exports = {
  idParam,
  getMySwapRequests,
  getAllSwapRequests,
  createSwapRequest,
  updateSwapRequest,
  respondToSwapRequest,
  cancelSwapRequest,
  approveSwapRequest,
  rejectSwapRequest,
  executeSwap,
  bulkApproveSwaps,
  approveRejectSwapRequest,
  bulkApproveRejectSwaps,
  getAvailableSwaps,
  getMySwappableShifts,
  getPendingApprovals,
  getSwapStatistics,
  getEligibleEmployees,
  getEmployeeShiftSwaps,

  // ✅ NEW: Target employee validators
  getTargetEmployees,
  getTargetEmployeesByDate
};
