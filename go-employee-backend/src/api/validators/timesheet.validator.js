const Joi = require('joi');

/**
 * Validation schema for creating timesheet
 */
const createTimesheet = Joi.object({
  taskId: Joi.number().integer().positive().optional(),
  projectId: Joi.number().integer().positive().optional(),
  businessUnitId: Joi.number().integer().positive().optional(),
  date: Joi.date().required(),
  duration: Joi.string().required().messages({
    'string.empty': 'Duration is required',
    'any.required': 'Duration is required'
  }),
  notes: Joi.string().optional().allow('', null),
  timesheetDocument: Joi.array().items(Joi.string()).optional()
}).custom((value, helpers) => {
  // At least one of taskId or projectId must be provided
  if (!value.taskId && !value.projectId) {
    return helpers.error('custom.taskOrProject');
  }
  return value;
}).messages({
  'custom.taskOrProject': 'Either taskId or projectId must be provided'
});

/**
 * Validation schema for updating timesheet
 */
const updateTimesheet = Joi.object({
  duration: Joi.string().optional(),
  notes: Joi.string().optional().allow('', null),
  timesheetDocument: Joi.array().items(Joi.string()).optional()
});

/**
 * Validation schema for approving/rejecting timesheet
 */
const approveRejectTimesheet = Joi.object({
  status: Joi.string().valid('Approved', 'Rejected', 'Onhold').required(),
  comment: Joi.string().optional().allow('', null)
});

/**
 * Validation schema for bulk approve/reject timesheets
 */
const bulkApproveRejectTimesheets = Joi.object({
  timesheet_id: Joi.array().items(Joi.number().integer().positive()).min(1).max(101).required(),
  status: Joi.string().valid('Approved', 'Rejected').required(),
  comment: Joi.string().max(500).optional()
});

/**
 * Validation schema for getting timesheets
 */
const getTimesheets = Joi.object({
  employeeId: Joi.number().integer().positive().optional(),
  taskId: Joi.number().integer().positive().optional(),
  projectId: Joi.number().integer().positive().optional(),
  status: Joi.string().valid('Pending', 'Approved', 'Rejected', 'Onhold').optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  approverId: Joi.number().integer().positive().optional(),
  search: Joi.string().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(50),
  sortBy: Joi.string().valid('date', 'createdAt', 'status', 'duration').default('date'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
});

/**
 * Validation schema for getting my timesheets
 */
const getMyTimesheets = Joi.object({
  taskId: Joi.number().integer().positive().optional(),
  projectId: Joi.number().integer().positive().optional(),
  status: Joi.string().valid('Pending', 'Approved', 'Rejected', 'Onhold').optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  search: Joi.string().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(50),
  sortBy: Joi.string().valid('date', 'createdAt', 'status', 'duration').default('date'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
});

/**
 * Validation schema for getting pending approvals
 */
const getPendingApprovals = Joi.object({
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  search: Joi.string().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(50),
  sortBy: Joi.string().valid('date', 'createdAt', 'status', 'duration').default('date'),
  sortOrder: Joi.string().valid('asc', 'desc').default('desc')
});

/**
 * Validation schema for weekly timesheet chart
 */
const getWeeklyTimesheetChart = Joi.object({
  employeeId: Joi.number().integer().positive().optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional()
});

/**
 * Validation schema for user timesheet graph
 */
const getUserTimesheetGraph = Joi.object({
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  groupBy: Joi.string().valid('day', 'week', 'month').default('day')
});

/**
 * Validation schema for project timesheet analytics
 */
const getProjectTimesheetAnalytics = Joi.object({
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional()
});

/**
 * Validation schema for timesheet summary
 */
const getTimesheetSummary = Joi.object({
  employeeId: Joi.number().integer().positive().optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional()
});

/**
 * Validation schema for timesheet ID parameter
 */
const timesheetIdParam = Joi.object({
  id: Joi.number().integer().positive().required()
});

/**
 * Validation schema for employee ID parameter
 */
const employeeIdParam = Joi.object({
  id: Joi.number().integer().positive().required()
});

/**
 * Validation schema for project ID parameter
 */
const projectIdParam = Joi.object({
  projectId: Joi.number().integer().positive().required()
});

const todayTimesheetRecord = Joi.object({
  status: Joi.string().valid('filled', 'not_filled').optional(),
  date: Joi.date().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  departmentId: Joi.number().integer().optional()
});

const customTimesheetRecord = Joi.object({
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(10),
  departmentId: Joi.number().integer().optional(),
  search: Joi.string().optional()
});

const generateTimesheet = Joi.object({
  startDate: Joi.date().optional(),
  endDate: Joi.date().optional(),
  email: Joi.string().email().optional(),
  informPM: Joi.boolean().optional().default(false)
});

module.exports = {
  createTimesheet,
  updateTimesheet,
  approveRejectTimesheet,
  bulkApproveRejectTimesheets,
  getTimesheets,
  getMyTimesheets,
  getPendingApprovals,
  getWeeklyTimesheetChart,
  getUserTimesheetGraph,
  getProjectTimesheetAnalytics,
  getTimesheetSummary,
  timesheetIdParam,
  employeeIdParam,
  projectIdParam,
  todayTimesheetRecord,
  customTimesheetRecord,
  generateTimesheet
};
