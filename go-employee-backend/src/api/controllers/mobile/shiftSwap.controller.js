'use strict';

/**
 * Mobile Shift Swap Controller - PRD Implementation
 *
 * Handles mobile-specific shift swap operations according to PRD specifications:
 * - Mobile-optimized swap discovery
 * - Simplified swap request workflow
 * - Push notification integration
 * - Offline-friendly data structure
 * - Complete CRUD operations with mobile response format
 */

const shiftSwapService = require('../../../services/rota/shiftSwapService');
const shiftSwapWorkflowService = require('../../../services/rota/shiftSwapWorkflowService');
const workflowFilterService = require('../../../services/common/workflowFilterService');
const approvalDetailsService = require('../../../services/workflow/approvalDetailsService');
const employeeService = require('../../../services/employee/employeeService');
const workflowInstanceService = require('../../../services/workflow/workflowInstanceService');
const { mobileSuccessResponse, mobilePaginatedResponse, mobileErrorResponse } = require('../../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../../common/errors');
const logger = require('../../../common/logging');
const {
  WorkflowAssignment,
  WorkflowInstance,
  ShiftSwapRequest,
  ShiftAssignment,
  RotaShiftInstance,
  RotaShift,
  ShiftTemplate,
  Employee,
  Department,
  Designation
} = require('../../../data/models');
const { Op } = require('sequelize');

/**
 * Mobile field transformation utilities
 */
const transformMobileToBackend = (mobileData) => {
  const backendData = {};

  // Map mobile field names to backend field names
  if (mobileData.requester_assignment_id !== undefined) backendData.requesterAssignmentId = mobileData.requester_assignment_id;
  if (mobileData.target_assignment_id !== undefined) backendData.targetAssignmentId = mobileData.target_assignment_id;
  if (mobileData.target_employee_id !== undefined) backendData.targetEmployeeId = mobileData.target_employee_id;
  if (mobileData.swap_type !== undefined) backendData.swapType = mobileData.swap_type;
  if (mobileData.reason !== undefined) backendData.reason = mobileData.reason;
  if (mobileData.notes !== undefined) backendData.notes = mobileData.notes;
  if (mobileData.urgency !== undefined) backendData.urgency = mobileData.urgency;

  // Handle standard field names (same in mobile and backend)
  if (mobileData.requesterAssignmentId !== undefined) backendData.requesterAssignmentId = mobileData.requesterAssignmentId;
  if (mobileData.targetAssignmentId !== undefined) backendData.targetAssignmentId = mobileData.targetAssignmentId;
  if (mobileData.targetEmployeeId !== undefined) backendData.targetEmployeeId = mobileData.targetEmployeeId;
  if (mobileData.swapType !== undefined) backendData.swapType = mobileData.swapType;

  return backendData;
};

const transformBackendToMobile = async (backendData, currentUserId, tenantContext = null) => {
  const swapData = backendData.toJSON ? backendData.toJSON() : backendData;

  const mobileData = {
    id: swapData.id,
    requester_id: swapData.requesterId,
    target_id: swapData.targetId,
    requester_assignment_id: swapData.requesterAssignmentId,
    target_assignment_id: swapData.targetAssignmentId,
    swap_date: swapData.swapDate,
    reason: swapData.reason,
    notes: swapData.notes,
    status: swapData.status,
    urgency: swapData.urgency,
    request_date: swapData.requestDate,
    response_date: swapData.responseDate,
  };

  // Get workflow status for approval information
  if (tenantContext) {
    try {
      const approverStatusMap = await workflowFilterService.getBulkApproverStatus(
        'shift_swap',
        [swapData.id],
        tenantContext
      );

      const approverStatus = approverStatusMap[swapData.id] || {};
      mobileData.can_approve = approverStatus.canApprove || false;
      mobileData.can_reject = approverStatus.canReject || false;
      mobileData.approver_status = approverStatus.decision || 'pending';
    } catch (error) {
      console.log(`⚠️ Could not get workflow status for swap ${swapData.id}:`, error.message);
    }
  }

  // Include related data if available
  if (swapData.requester) {
    mobileData.requester = {
      id: swapData.requester.id,
      name: `${swapData.requester.firstName} ${swapData.requester.lastName}`,
      employee_id: swapData.requester.employeeId,
      profile_image: swapData.requester.profileImage || ''
    };
  }

  if (swapData.target) {
    mobileData.target = {
      id: swapData.target.id,
      name: `${swapData.target.firstName} ${swapData.target.lastName}`,
      employee_id: swapData.target.employeeId,
      profile_image: swapData.target.profileImage || ''
    };
  }

  if (swapData.requesterAssignment) {
    mobileData.requester_shift = {
      id: swapData.requesterAssignment.id,
      shift_name: swapData.requesterAssignment.rotaShiftInstance?.rotaShift?.name || '',
      start_time: swapData.requesterAssignment.rotaShiftInstance?.rotaShift?.startTime || '',
      end_time: swapData.requesterAssignment.rotaShiftInstance?.rotaShift?.endTime || '',
      date: swapData.requesterAssignment.rotaShiftInstance?.date || ''
    };
  }

  if (swapData.targetAssignment) {
    mobileData.target_shift = {
      id: swapData.targetAssignment.id,
      shift_name: swapData.targetAssignment.rotaShiftInstance?.rotaShift?.name || '',
      start_time: swapData.targetAssignment.rotaShiftInstance?.rotaShift?.startTime || '',
      end_time: swapData.targetAssignment.rotaShiftInstance?.rotaShift?.endTime || '',
      date: swapData.targetAssignment.rotaShiftInstance?.date || ''
    };
  }

  return mobileData;
};

/**
 * Get all shift swap requests (my requests and requests for approval)
 * @route GET /api/v1/mobile/shift-swaps
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAllSwapRequests = async (req, res, next) => {
  try {
    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Get pagination and sorting parameters
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;

    // Get filter parameters
    let filters = {};
    if (req.query.status) filters.status = req.query.status;
    if (req.query.requesterId) filters.requesterId = req.query.requesterId;
    if (req.query.targetId) filters.targetId = req.query.targetId;
    if (req.query.departmentId) filters.departmentId = req.query.departmentId;
    if (req.query.startDate) filters.startDate = req.query.startDate;
    if (req.query.endDate) filters.endDate = req.query.endDate;
    if (req.query.search) filters.search = req.query.search;

    req.tenantContext.user = req.user;

    // Apply workflow-based filtering for mobile
    filters = await workflowFilterService.applyWorkflowFiltering(filters, 'shift_swap', req.tenantContext);

    // Get all swap requests
    const result = await shiftSwapService.getAllSwapRequests(
      filters,
      { page, limit },
      { sortBy: req.query.sortBy || 'createdAt', sortOrder: req.query.sortOrder || 'desc' },
      req.tenantContext
    );

    // Transform to mobile format
    const mobileSwapRequests = await Promise.all(
      result.swapRequests.map(swap => transformBackendToMobile(swap, employee.id, req.tenantContext))
    );

    return mobilePaginatedResponse(res, {
      message: 'Shift swap requests retrieved successfully',
      data: mobileSwapRequests,
      pagination: { total: result.total }
    });
  } catch (error) {
    logger.error('Mobile get all swap requests error:', error);
    next(error);
  }
};

/**
 * Create new shift swap request
 * @route POST /api/v1/mobile/shift-swaps
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createSwapRequest = async (req, res, next) => {
  try {
    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Transform mobile request to backend format
    const backendData = transformMobileToBackend(req.body);
    backendData.requesterId = employee.id;

    // Create swap request
    const swapRequest = await shiftSwapService.createSwapRequest(backendData, req.tenantContext);
    const mobileSwapRequest = await transformBackendToMobile(swapRequest, employee.id, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: 'Shift swap request created successfully',
      result: mobileSwapRequest
    }, 201);
  } catch (error) {
    logger.error('Mobile create swap request error:', error);
    next(error);
  }
};

/**
 * Get swap request details by ID
 * @route GET /api/v1/mobile/shift-swaps/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getSwapRequestById = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Get swap request details
    const swapRequest = await shiftSwapService.getSwapRequestById(id, req.tenantContext);

    if (!swapRequest) {
      return mobileSuccessResponse(res, {
        message: 'Swap request not found',
        result: null
      });
    }

    // Get approval details using the flexible approval system
    const approvalDetails = await approvalDetailsService.getUserApprovalDetails(
      'shift_swap',
      parseInt(id),
      req.tenantContext
    );

    // Transform to mobile format
    const mobileSwapRequest = await transformBackendToMobile(swapRequest, employee.id, req.tenantContext);

    // Add approval workflow information
    if (approvalDetails && approvalDetails.hasWorkflow) {
      const allApprovers = [
        ...(approvalDetails.approvers || []),
        ...(approvalDetails.approvalHistory || [])
      ];

      mobileSwapRequest.approvals = allApprovers.map(approval => ({
        approver_id: approval.approverId,
        approver_name: approval.approverName,
        status: approval.status === 'pending' ? 'Pending' :
                approval.status === 'completed' ?
                  (approval.decision === 'approved' ? 'Approved' : 'Rejected') : 'Pending',
        comments: approval.comments || '',
        step_name: approval.stepName || '',
        created_at: swapRequest.createdAt,
        updated_at: swapRequest.updatedAt
      }));
    }

    return mobileSuccessResponse(res, {
      message: 'Swap request retrieved successfully',
      result: mobileSwapRequest
    });
  } catch (error) {
    logger.error('Mobile get swap request by ID error:', error);
    next(error);
  }
};

/**
 * Update swap request
 * @route PUT /api/v1/mobile/shift-swaps/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Transform mobile request to backend format
    const backendData = transformMobileToBackend(req.body);

    // Update swap request
    const updatedSwapRequest = await shiftSwapService.updateSwapRequest(id, backendData, req.tenantContext);
    const mobileSwapRequest = await transformBackendToMobile(updatedSwapRequest, employee.id, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: 'Shift swap request updated successfully',
      result: mobileSwapRequest
    });
  } catch (error) {
    logger.error('Mobile update swap request error:', error);
    next(error);
  }
};

/**
 * Cancel swap request
 * @route POST /api/v1/mobile/shift-swaps/:id/cancel
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const cancelSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Cancel swap request
    await shiftSwapService.cancelSwapRequest(id, { reason }, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: 'Shift swap request cancelled successfully',
      result: {}
    });
  } catch (error) {
    logger.error('Mobile cancel swap request error:', error);
    next(error);
  }
};

/**
 * Approve swap request
 * @route POST /api/v1/mobile/shift-swaps/:id/approve
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const approveSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { comments, notes } = req.body;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Approve swap request using workflow service
    const approvalData = {
      comments: comments || notes || '',
      actionBy: req.tenantContext.userId,
      actionDate: new Date()
    };

    await shiftSwapWorkflowService.processSwapApproval(id, approvalData, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: 'Shift swap request approved successfully',
      result: {}
    });
  } catch (error) {
    logger.error('Mobile approve swap request error:', error);
    next(error);
  }
};

/**
 * Reject swap request
 * @route POST /api/v1/mobile/shift-swaps/:id/reject
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const rejectSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { rejectionReason, comments } = req.body;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Reject swap request using workflow service
    const rejectionData = {
      rejectionReason: rejectionReason || comments || 'Swap request rejected',
      actionBy: req.tenantContext.userId,
      actionDate: new Date()
    };

    await shiftSwapWorkflowService.processSwapRejection(id, rejectionData, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: 'Shift swap request rejected successfully',
      result: {}
    });
  } catch (error) {
    logger.error('Mobile reject swap request error:', error);
    next(error);
  }
};

/**
 * Respond to swap request (employee response - accept/decline)
 * @route POST /api/v1/mobile/shift-swaps/:id/respond
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const respondToSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { action, notes } = req.body;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Respond to swap request
    const responseData = {
      response: action, // 'accept' or 'decline'
      message: notes || '',
      respondedBy: employee.id
    };

    await shiftSwapService.respondToSwapRequest(id, responseData, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: `Swap request ${action}ed successfully`,
      result: {}
    });
  } catch (error) {
    logger.error('Mobile respond to swap request error:', error);
    next(error);
  }
};

/**
 * Execute approved swap
 * @route POST /api/v1/mobile/shift-swaps/:id/execute
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const executeSwap = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { notes, executedAt } = req.body;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Execute swap
    const executionData = {
      executedAt: executedAt || new Date(),
      executedBy: employee.id,
      notes: notes || ''
    };

    await shiftSwapService.executeSwap(id, executionData, req.tenantContext);

    return mobileSuccessResponse(res, {
      message: 'Shift swap executed successfully',
      result: {}
    });
  } catch (error) {
    logger.error('Mobile execute swap error:', error);
    next(error);
  }
};

/**
 * Bulk approve swap requests
 * @route POST /api/v1/mobile/shift-swaps/bulk-approve
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkApproveSwaps = async (req, res, next) => {
  try {
    const { swapRequestIds, comments } = req.body;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Process each swap request
    const results = [];
    const errors = [];

    for (const swapId of swapRequestIds) {
      try {
        const approvalData = {
          comments: comments || 'Mobile bulk approved',
          actionBy: req.tenantContext.userId,
          actionDate: new Date()
        };

        await shiftSwapWorkflowService.processSwapApproval(swapId, approvalData, req.tenantContext);

        results.push({
          id: swapId,
          success: true,
          message: 'Swap approved successfully'
        });
      } catch (error) {
        errors.push({
          id: swapId,
          success: false,
          error: error.message
        });
      }
    }

    return mobileSuccessResponse(res, {
      message: `Mobile bulk approval completed. ${results.length} successful, ${errors.length} failed.`,
      result: {
        successful: results,
        failed: errors,
        summary: {
          total: swapRequestIds.length,
          successful: results.length,
          failed: errors.length
        }
      }
    });
  } catch (error) {
    logger.error('Mobile bulk approve swaps error:', error);
    next(error);
  }
};

/**
 * Approve or reject swap request (unified endpoint)
 * @route PUT /api/v1/mobile/shift-swaps/approve-reject/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const approveRejectSwapRequest = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { status, comment, rejectionReason } = req.body;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    if (status === 'Approved') {
      const approvalData = {
        comments: comment || 'Mobile approved',
        actionBy: req.tenantContext.userId,
        actionDate: new Date()
      };

      await shiftSwapWorkflowService.processSwapApproval(id, approvalData, req.tenantContext);

      return mobileSuccessResponse(res, {
        message: 'Shift swap request approved successfully',
        result: {}
      });
    } else if (status === 'Rejected') {
      const rejectionData = {
        rejectionReason: rejectionReason || comment || 'Swap request rejected',
        actionBy: req.tenantContext.userId,
        actionDate: new Date()
      };

      await shiftSwapWorkflowService.processSwapRejection(id, rejectionData, req.tenantContext);

      return mobileSuccessResponse(res, {
        message: 'Shift swap request rejected successfully',
        result: {}
      });
    } else {
      throw new ValidationError('Invalid status. Must be "Approved" or "Rejected"');
    }
  } catch (error) {
    logger.error('Mobile approve/reject swap request error:', error);
    next(error);
  }
};

/**
 * Bulk approve or reject swap requests (v2)
 * @route PUT /api/v1/mobile/v2/shift-swaps/approve-reject
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkApproveRejectSwaps = async (req, res, next) => {
  try {
    const { swapRequestIds, status, comment, rejectionReason } = req.body;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Process each swap request
    const results = [];
    const errors = [];

    for (const swapId of swapRequestIds) {
      try {
        if (status === 'Approved') {
          const approvalData = {
            comments: comment || 'Mobile bulk approved',
            actionBy: req.tenantContext.userId,
            actionDate: new Date()
          };

          await shiftSwapWorkflowService.processSwapApproval(swapId, approvalData, req.tenantContext);
        } else if (status === 'Rejected') {
          const rejectionData = {
            rejectionReason: rejectionReason || comment || 'Mobile bulk rejected',
            actionBy: req.tenantContext.userId,
            actionDate: new Date()
          };

          await shiftSwapWorkflowService.processSwapRejection(swapId, rejectionData, req.tenantContext);
        } else {
          throw new ValidationError('Invalid status. Must be "Approved" or "Rejected"');
        }

        results.push({
          id: swapId,
          success: true,
          message: `Swap ${status.toLowerCase()} successfully`
        });
      } catch (error) {
        errors.push({
          id: swapId,
          success: false,
          error: error.message
        });
      }
    }

    return mobileSuccessResponse(res, {
      message: `Mobile bulk ${status.toLowerCase()} completed. ${results.length} successful, ${errors.length} failed.`,
      result: {
        successful: results,
        failed: errors,
        summary: {
          total: swapRequestIds.length,
          successful: results.length,
          failed: errors.length
        }
      }
    });
  } catch (error) {
    logger.error('Mobile bulk approve/reject swaps error:', error);
    next(error);
  }
};

/**
 * Get available shifts for swapping
 * @route GET /api/v1/mobile/available-swaps
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAvailableSwaps = async (req, res, next) => {
  try {
    const { startDate, endDate, shiftType, departmentId, limit } = req.query;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Get available swaps
    const availableSwaps = await shiftSwapService.getAvailableSwaps({
      startDate,
      endDate,
      shiftType,
      departmentId,
      excludeEmployeeId: employee.id,
      limit: parseInt(limit) || 20
    }, req.tenantContext);

    // Transform to mobile format
    const mobileAvailableSwaps = await Promise.all(
      availableSwaps.map(swap => transformBackendToMobile(swap, employee.id, req.tenantContext))
    );

    return mobileSuccessResponse(res, {
      message: 'Available swaps retrieved successfully',
      result: mobileAvailableSwaps
    });
  } catch (error) {
    logger.error('Mobile get available swaps error:', error);
    next(error);
  }
};

/**
 * Get my swappable shifts
 * @route GET /api/v1/mobile/my-swappable-shifts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getMySwappableShifts = async (req, res, next) => {
  try {
    const { startDate, endDate, limit } = req.query;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Get my swappable shifts
    const swappableShifts = await shiftSwapService.getMySwappableShifts(employee.id, {
      startDate,
      endDate,
      limit: parseInt(limit) || 20
    }, req.tenantContext);

    // Transform to mobile format
    const mobileSwappableShifts = swappableShifts.map(shift => ({
      id: shift.id,
      shift_name: shift.rotaShiftInstance?.rotaShift?.name || '',
      start_time: shift.rotaShiftInstance?.rotaShift?.startTime || '',
      end_time: shift.rotaShiftInstance?.rotaShift?.endTime || '',
      date: shift.rotaShiftInstance?.date || '',
      can_swap: shift.canSwap || true,
      swap_restrictions: shift.swapRestrictions || [],
      created_at: shift.createdAt,
      updated_at: shift.updatedAt
    }));

    return mobileSuccessResponse(res, {
      message: 'My swappable shifts retrieved successfully',
      result: mobileSwappableShifts
    });
  } catch (error) {
    logger.error('Mobile get my swappable shifts error:', error);
    next(error);
  }
};

/**
 * Get pending approvals for current user
 * @route GET /api/v1/mobile/shift-swaps/pending-approvals
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getPendingApprovals = async (req, res, next) => {
  try {
    const { page, limit, departmentId, urgency } = req.query;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Get pending approvals using workflow filter service
    let filters = {
      status: 'pending',
      departmentId,
      urgency
    };

    req.tenantContext.user = req.user;
    filters = await workflowFilterService.applyWorkflowFiltering(filters, 'shift_swap', req.tenantContext);

    const result = await shiftSwapService.getAllSwapRequests(
      filters,
      { page: parseInt(page) || 1, limit: parseInt(limit) || 20 },
      { sortBy: 'createdAt', sortOrder: 'desc' },
      req.tenantContext
    );

    // Transform to mobile format
    const mobilePendingApprovals = await Promise.all(
      result.swapRequests.map(swap => transformBackendToMobile(swap, employee.id, req.tenantContext))
    );

    return mobilePaginatedResponse(res, {
      message: 'Pending approvals retrieved successfully',
      data: mobilePendingApprovals,
      pagination: { total: result.total }
    });
  } catch (error) {
    logger.error('Mobile get pending approvals error:', error);
    next(error);
  }
};

/**
 * Get swap statistics and analytics
 * @route GET /api/v1/mobile/shift-swaps/statistics
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getSwapStatistics = async (req, res, next) => {
  try {
    const { startDate, endDate, departmentId } = req.query;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Get swap statistics
    const statistics = await shiftSwapService.getSwapStatistics({
      employeeId: employee.id,
      startDate,
      endDate,
      departmentId
    }, req.tenantContext);

    // Transform to mobile format
    const mobileStatistics = {
      total_requests: statistics.totalRequests || 0,
      approved_requests: statistics.approvedRequests || 0,
      rejected_requests: statistics.rejectedRequests || 0,
      pending_requests: statistics.pendingRequests || 0,
      executed_swaps: statistics.executedSwaps || 0,
      my_requests: statistics.myRequests || 0,
      requests_for_me: statistics.requestsForMe || 0,
      approval_rate: statistics.approvalRate || 0,
      response_time_avg: statistics.avgResponseTime || 0,
      most_swapped_shift: statistics.mostSwappedShift || null,
      department_stats: statistics.departmentStats || []
    };

    return mobileSuccessResponse(res, {
      message: 'Swap statistics retrieved successfully',
      result: mobileStatistics
    });
  } catch (error) {
    logger.error('Mobile get swap statistics error:', error);
    next(error);
  }
};

/**
 * Get eligible employees for swap
 * @route GET /api/v1/mobile/shift-swaps/:id/eligible-employees
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getEligibleEmployees = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { departmentId, shiftType, limit } = req.query;

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Get eligible employees for swap
    const eligibleEmployees = await shiftSwapService.getEligibleEmployees(id, {
      departmentId,
      shiftType,
      limit: parseInt(limit) || 50
    }, req.tenantContext);

    // Transform to mobile format
    const mobileEligibleEmployees = eligibleEmployees.map(emp => ({
      id: emp.id,
      name: `${emp.firstName} ${emp.lastName}`,
      employee_id: emp.employeeId,
      profile_image: emp.profileImage || '',
      department: emp.department?.name || '',
      designation: emp.designation?.name || '',
      shift_compatibility: emp.shiftCompatibility || 'compatible',
      availability_score: emp.availabilityScore || 100
    }));

    return mobileSuccessResponse(res, {
      message: 'Eligible employees retrieved successfully',
      result: mobileEligibleEmployees
    });
  } catch (error) {
    logger.error('Mobile get eligible employees error:', error);
    next(error);
  }
};

/**
 * Get shift swap requests assigned to current user for approval (similar to employee-leave)
 * @route GET /api/v1/mobile/employee-shift-swap
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getEmployeeShiftSwaps = async (req, res, next) => {
  try {
    let { page = 1, size = 10, swap_status = 'pending', search } = req.query;
    let limit = size;
    swap_status = swap_status.toLowerCase();

    let assignmentDecision = [];
    let approvalMobileStatus = null;
    if (swap_status === 'pending') {
      assignmentDecision = ['pending'];
      approvalMobileStatus = 'Pending';
    } else if (swap_status === 'approved') {
      assignmentDecision = ['approved'];
      approvalMobileStatus = 'Approved';
    } else if (swap_status === 'rejected') {
      assignmentDecision = ['rejected', 'cancelled'];
      approvalMobileStatus = 'Rejected';
    }

    // Get current user's employee record
    const employee = await employeeService.getEmployeeByUserId(req.user.id, req.tenantContext);

    if (!employee) {
      throw new NotFoundError('Employee record not found');
    }

    // Get workflow assignments for shift swap requests assigned to current user
    const myAssignments = await WorkflowAssignment.findAll({
      where: {
        assigneeId: employee?.id,
        companyId: req?.tenantContext?.companyId
      },
      include: [
        {
          model: WorkflowInstance,
          as: 'instance',
          where: {
            entityType: 'shift_swap',
          },
          required: true
        }
      ]
    });

    let myAssignmentsSwapIds = myAssignments?.map(assignment => assignment?.instance?.entityId);

    // Get overall approved swap requests
    const myAssignmentsOverallApproved = await ShiftSwapRequest.findAll({
      where: {
        id: {
          [Op.in]: myAssignmentsSwapIds
        },
        status: 'approved'
      }
    });
    let myAssignmentsOverallApprovedIds = myAssignmentsOverallApproved?.map(swap => swap?.id);

    // Get overall rejected swap requests
    const myAssignmentsOverallRejected = await ShiftSwapRequest.findAll({
      where: {
        id: {
          [Op.in]: myAssignmentsSwapIds
        },
        status: 'rejected'
      }
    });
    let myAssignmentsOverallRejectedIds = myAssignmentsOverallRejected?.map(swap => swap?.id);

    // Get pending assignments based on decision status
    const pendingAssignments = await WorkflowAssignment.findAll({
      where: {
        assigneeId: employee.id,
        decision: {
          [Op.in]: assignmentDecision
        },
        companyId: req.tenantContext.companyId
      },
      include: [
        {
          model: WorkflowInstance,
          as: 'instance',
          where: {
            entityType: 'shift_swap',
          },
          required: true
        }
      ]
    });

    const assignedSwapIds = pendingAssignments?.map(assignment => assignment.instance.entityId);
    let swapIdsSet = new Set([...assignedSwapIds]);

    // Filter based on status
    if (assignmentDecision[0] == 'pending') {
      for (let id of myAssignmentsOverallApprovedIds) {
        swapIdsSet.delete(id);
      }
      for (let id of myAssignmentsOverallRejectedIds) {
        swapIdsSet.delete(id);
      }
    }

    if (assignmentDecision[0] == 'approved') {
      for (let id of myAssignmentsOverallApprovedIds) {
        swapIdsSet.add(id);
      }
    }

    if (assignmentDecision[0] == 'rejected') {
      for (let id of myAssignmentsOverallRejectedIds) {
        swapIdsSet.add(id);
      }
    }

    let swapIdsToInclude = [...swapIdsSet];

    if (swapIdsToInclude.length === 0) {
      return mobilePaginatedResponse(res, {
        message: 'No shift swap requests found for approval',
        data: [],
        pagination: { total: 0 }
      });
    }

    // Build filters for swap requests
    const filters = {};

    if (swapIdsToInclude.length > 0) {
        filters.id = swapIdsToInclude;
    }

    if (search) {
      filters.search = search;
    }
    // Add company filter
    filters.companyId = req.tenantContext.companyId;

    // Get swap requests
    const result = await shiftSwapService.getAllSwapRequests(
      filters,
      { page: parseInt(page), limit: parseInt(limit) },
      { sortBy: 'createdAt', sortOrder: 'desc' },
      req.tenantContext
    );

    // Transform to mobile format with workflow status
    const mobileSwapRequests = await Promise.all(
      result.swapRequests.map(async swapRequest => {
        let workflowStatus = null;
        try {
          workflowStatus = await workflowInstanceService.getWorkflowStatus('shift_swap', swapRequest.id, req.tenantContext);
        } catch (error) {
          console.error(`\n== Failed to get workflow status for Swap ID ${swapRequest.id}:`, error);
        }

        // Transform to mobile format
        const mobileSwapRequest = await transformBackendToMobile(swapRequest, employee.id, req.tenantContext);

        // Add approval information
        const approvals = [];
        approvals.push({
          id: null,
          approval_id: employee?.id,
          swap_request_id: swapRequest.id,
          approval_comment: null,
          approval_status: approvalMobileStatus,
          created_by: swapRequest.requesterId,
          modified_by: null,
          createdAt: swapRequest.createdAt,
          updatedAt: swapRequest.updatedAt,
          deletedAt: null
        });

        // Add approvals to mobile response
        mobileSwapRequest.approvals = approvals;

        return mobileSwapRequest;
      })
    );

    return mobilePaginatedResponse(res, {
      message: 'Shift swap requests for approval retrieved successfully',
      data: mobileSwapRequests,
      pagination: { total: result.total }
    });
  } catch (error) {
    logger.error('Mobile get employee shift swaps error:', error);
    next(error);
  }
};

/**
 * ✅ NEW: Get all available target employees for shift swap
 * @route GET /api/v1/mobile/target-employees
 */
const getTargetEmployees = async (req, res, next) => {
  try {
    const { tenantContext } = req;
    const { designation_id, department_id, date, exclude_self = true } = req.query;

    // Get all active employees in the business unit
    const employees = await employeeService.getEmployeesByBusinessUnit(
      tenantContext.businessUnitId,
      tenantContext
    );

    // Filter employees based on criteria
    let filteredEmployees = employees.filter(emp => {
      // Filter by status
      if (emp.status !== 'active') return false;

      // Filter by designation if provided
      if (designation_id && emp.designationId !== parseInt(designation_id)) return false;

      // Filter by department if provided
      if (department_id && emp.departmentId !== parseInt(department_id)) return false;

      // Exclude self if requested
      if (exclude_self && emp.userId === tenantContext.userId) return false;

      return true;
    });

    // Transform to mobile format
    const mobileEmployees = filteredEmployees.map(emp => ({
      employee_id: emp.id,
      employee_code: emp.employeeId,
      first_name: emp.firstName,
      last_name: emp.lastName,
      full_name: `${emp.firstName} ${emp.lastName}`,
      email: emp.contactEmail,
      phone: emp.phoneNumber,
      designation: emp.designation ? {
        id: emp.designation.id,
        name: emp.designation.name,
        code: emp.designation.code
      } : null,
      department: emp.department ? {
        id: emp.department.id,
        name: emp.department.name
      } : null,
      profile_image: emp.profileImage,
      is_available: true // Will be calculated based on date if provided
    }));

    return mobileSuccessResponse(res, {message:'Target employees retrieved successfully',
      employees: mobileEmployees,
      total_count: mobileEmployees.length,
      filters_applied: {
        designation_id: designation_id || null,
        department_id: department_id || null,
        date: date || null,
        exclude_self
      }
    });

  } catch (error) {
    logger.error('Error getting target employees:', error);
    next(error);
  }
};

/**
 * ✅ NEW: Get target employees available on specific date
 * @route GET /api/v1/mobile/target-employees/:date
 */
const getTargetEmployeesByDate = async (req, res, next) => {
  try {
    const { tenantContext } = req;
    const { date } = req.params;
    const { designation_id, department_id, exclude_self = true } = req.query;

    // Get employees with their availability on the specific date
    const availableEmployees = await shiftSwapService.getAvailableEmployeesForSwap(
      date,
      designation_id,
      null, // shiftId
      tenantContext
    );

    // Transform to mobile format
    const mobileEmployees = availableEmployees.map(emp => ({
      employee_id: emp.employeeId,
      employee_code: emp.employeeId,
      first_name: emp.firstName,
      last_name: emp.lastName,
      full_name: `${emp.firstName} ${emp.lastName}`,
      email: emp.email,
      phone: emp.phone,
      designation: emp.designation ? {
        id: emp.designation.id,
        name: emp.designation.name
      } : null,
      current_shift: emp.currentShift ? {
        id: emp.currentShift.id,
        name: emp.currentShift.name,
        start_time: emp.currentShift.startTime,
        end_time: emp.currentShift.endTime,
        assignment_id: emp.currentShift.assignmentId
      } : null,
      availability_status: 'available',
      can_swap: true
    }));

    return mobileSuccessResponse(res, {message:`Target employees for ${date} retrieved successfully`,
      date,
      employees: mobileEmployees,
      total_count: mobileEmployees.length,
      filters_applied: {
        designation_id: designation_id || null,
        department_id: department_id || null,
        exclude_self
      }
    })

  } catch (error) {
    logger.error('Error getting target employees by date:', error);
    next(error);
  }
};

// ==================== NEW: SHIFT LOOKUP METHODS ====================

/**
 * Get employee's shift on specific date
 * @route GET /api/v1/mobile/employee-shift/:employeeId/:date
 */
const getEmployeeShiftByDate = async (req, res, next) => {
  try {
    const {date } = req.params;
    const tenantContext = req.tenantContext;

    console.log(`📋 Getting shift for employee ${employeeId} on ${date}`);

    // Get employee's shift assignment for the date
    const shiftAssignment = await ShiftAssignment.findOne({
      where: {
        employeeId: parseInt(employeeId),
        date: date,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      },
      include: [
        {
          model: RotaShiftInstance,
          as: 'rotaShiftInstance',
          include: [
            {
              model: RotaShift,
              as: 'rotaShift',
              include: [
                {
                  model: ShiftTemplate,
                  as: 'shiftTemplate'
                }
              ]
            }
          ]
        },
        {
          model: Employee,
          as: 'employee',
          attributes: ['id', 'employeeCode', 'firstName', 'lastName', 'email']
        }
      ]
    });

    if (!shiftAssignment) {
      return mobileErrorResponse(res, 'No shift assignment found for this employee on the specified date', 404);
    }

    // Format response
    const shiftData = {
      assignment_id: shiftAssignment.id,
      employee: {
        id: shiftAssignment.employee.id,
        employee_code: shiftAssignment.employee.employeeCode,
        name: `${shiftAssignment.employee.firstName} ${shiftAssignment.employee.lastName}`,
        email: shiftAssignment.employee.email
      },
      date: shiftAssignment.date,
      shift: {
        id: shiftAssignment.rotaShiftInstance?.rotaShift?.id,
        name: shiftAssignment.rotaShiftInstance?.rotaShift?.name,
        template: {
          id: shiftAssignment.rotaShiftInstance?.rotaShift?.shiftTemplate?.id,
          name: shiftAssignment.rotaShiftInstance?.rotaShift?.shiftTemplate?.name,
          start_time: shiftAssignment.rotaShiftInstance?.rotaShift?.shiftTemplate?.startTime,
          end_time: shiftAssignment.rotaShiftInstance?.rotaShift?.shiftTemplate?.endTime,
          duration_hours: shiftAssignment.rotaShiftInstance?.rotaShift?.shiftTemplate?.durationHours
        }
      },
      status: shiftAssignment.status,
      can_swap: shiftAssignment.status === 'assigned' && new Date(date) > new Date(),
      created_at: shiftAssignment.createdAt,
      updated_at: shiftAssignment.updatedAt
    };

    return mobileSuccessResponse(res, shiftData, 'Employee shift retrieved successfully');

  } catch (error) {
    console.error('❌ Error getting employee shift by date:', error);
    return next(error);
  }
};

/**
 * Get all available shifts on specific date
 * @route GET /api/v1/mobile/available-shifts/:date
 */
const getAvailableShiftsByDate = async (req, res, next) => {
  try {
    const { date } = req.params;
    const { departmentId, designationId, includeAssignments } = req.query;
    const tenantContext = req.tenantContext;

    console.log(`📋 Getting available shifts for date ${date}`);

    // Build where conditions
    const whereConditions = {
      date: date,
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId
    };

    // Get all shift instances for the date
    const shiftInstances = await RotaShiftInstance.findAll({
      where: whereConditions,
      include: [
        {
          model: RotaShift,
          as: 'rotaShift',
          where: departmentId ? { departmentId: parseInt(departmentId) } : {},
          include: [
            {
              model: ShiftTemplate,
              as: 'shiftTemplate'
            },
            {
              model: Department,
              as: 'department',
              attributes: ['id', 'name']
            }
          ]
        },
        ...(includeAssignments ? [{
          model: ShiftAssignment,
          as: 'assignments',
          include: [
            {
              model: Employee,
              as: 'employee',
              attributes: ['id', 'employeeCode', 'firstName', 'lastName'],
              include: designationId ? [{
                model: Designation,
                as: 'designation',
                where: { id: parseInt(designationId) },
                attributes: ['id', 'name']
              }] : [{
                model: Designation,
                as: 'designation',
                attributes: ['id', 'name']
              }]
            }
          ]
        }] : [])
      ],
      order: [
        ['rotaShift', 'shiftTemplate', 'startTime', 'ASC']
      ]
    });

    // Format response
    const shiftsData = shiftInstances.map(instance => ({
      instance_id: instance.id,
      date: instance.date,
      shift: {
        id: instance.rotaShift.id,
        name: instance.rotaShift.name,
        department: {
          id: instance.rotaShift.department.id,
          name: instance.rotaShift.department.name
        },
        template: {
          id: instance.rotaShift.shiftTemplate.id,
          name: instance.rotaShift.shiftTemplate.name,
          start_time: instance.rotaShift.shiftTemplate.startTime,
          end_time: instance.rotaShift.shiftTemplate.endTime,
          duration_hours: instance.rotaShift.shiftTemplate.durationHours
        }
      },
      required_count: instance.requiredCount,
      assigned_count: instance.assignedCount,
      available_slots: Math.max(0, instance.requiredCount - instance.assignedCount),
      has_availability: instance.assignedCount < instance.requiredCount,
      ...(includeAssignments && {
        assignments: instance.assignments?.map(assignment => ({
          assignment_id: assignment.id,
          employee: {
            id: assignment.employee.id,
            employee_code: assignment.employee.employeeCode,
            name: `${assignment.employee.firstName} ${assignment.employee.lastName}`,
            designation: assignment.employee.designation ? {
              id: assignment.employee.designation.id,
              name: assignment.employee.designation.name
            } : null
          },
          status: assignment.status
        })) || []
      })
    }));

    // Filter by availability if needed
    const availableShifts = shiftsData.filter(shift => shift.has_availability);

    return mobileSuccessResponse(res, {
      date: date,
      total_shifts: shiftsData.length,
      available_shifts: availableShifts.length,
      shifts: availableShifts,
      filters_applied: {
        department_id: departmentId || null,
        designation_id: designationId || null,
        include_assignments: includeAssignments || false
      }
    }, `Available shifts for ${date} retrieved successfully`);

  } catch (error) {
    console.error('❌ Error getting available shifts by date:', error);
    return next(error);
  }
};

// Legacy endpoints for backward compatibility
const getMySwapRequests = getAllSwapRequests;
const getSwapRequestDetails = getSwapRequestById;

module.exports = {
  // Main CRUD operations
  getAllSwapRequests,
  createSwapRequest,
  getSwapRequestById,
  updateSwapRequest,

  // Workflow operations
  cancelSwapRequest,
  approveSwapRequest,
  rejectSwapRequest,
  respondToSwapRequest,
  executeSwap,

  // Bulk operations
  bulkApproveSwaps,
  approveRejectSwapRequest,
  bulkApproveRejectSwaps,

  // Discovery and analytics
  getAvailableSwaps,
  getMySwappableShifts,
  getPendingApprovals,
  getSwapStatistics,
  getEligibleEmployees,

  // ✅ NEW: Target employee APIs
  getTargetEmployees,
  getTargetEmployeesByDate,

  // ✅ NEW: Shift lookup APIs
  getEmployeeShiftByDate,
  getAvailableShiftsByDate,

  // Approver-specific endpoints
  getEmployeeShiftSwaps,

  // Legacy endpoints (backward compatibility)
  getMySwapRequests,
  getSwapRequestDetails,

  // Utility functions
  transformMobileToBackend,
  transformBackendToMobile
};