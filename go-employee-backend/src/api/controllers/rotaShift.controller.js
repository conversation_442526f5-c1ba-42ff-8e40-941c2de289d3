'use strict';

/**
 * Rota Shift Template Controller - PRD Implementation
 * 
 * <PERSON>les shift template management according to PRD specifications:
 * - Templates are self-contained (no ShiftType dependency)
 * - Define timing, staffing, designation requirements
 * - Templates are independent of dates/schedules/employees
 * - Support template library features
 */

const rotaShiftService = require('../../services/rota/rotaShiftService');
const { successResponse } = require('../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../common/errors');

/**
 * Get all shift templates with advanced filtering
 * @route GET /api/v1/rota-shifts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAllShiftTemplates = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      departmentId,
      designationId,
      category,
      search,
      status,
      sortBy = 'priority',
      sortOrder = 'DESC'
    } = req.query;

    const filters = {
      page: parseInt(page),
      limit: parseInt(limit),
      departmentId: departmentId ? parseInt(departmentId) : null,
      designationId: designationId ? parseInt(designationId) : null,
      category,
      search,
      isActive: status === 'active' ? true : false,
      sortBy,
      sortOrder
    };

    const result = await rotaShiftService.getAllTemplates(filters, req.tenantContext);

    return successResponse(res, {
      message: 'Shift templates retrieved successfully',
      data: result.templates,
      pagination: result.pagination,
      filters: filters
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get shift template by ID with usage analytics
 * @route GET /api/v1/rota-shifts/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getShiftTemplateById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { includeUsage = false } = req.query;

    const template = await rotaShiftService.getTemplateById(
      id, 
      req.tenantContext,
      { includeUsage: includeUsage === 'true' }
    );

    if (!template) {
      return res.status(404).json({
        success: false,
        message: 'Shift template not found'
      });
    }

    return successResponse(res, {
      message: 'Shift template retrieved successfully',
      data: template
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create new shift template
 * @route POST /api/v1/rota-shifts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createShiftTemplate = async (req, res, next) => {
  try {
    const templateData = {
      ...req.body,
      companyId: req.tenantContext.companyId,
      businessUnitId: req.tenantContext.businessUnitId,
      createdById: req.tenantContext.userId
    };

    const template = await rotaShiftService.createTemplate(templateData, req.tenantContext);

    return successResponse(res, {
      message: 'Shift template created successfully',
      data: template
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update shift template
 * @route PUT /api/v1/rota-shifts/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateShiftTemplate = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = {
      ...req.body,
      updatedById: req.tenantContext.userId
    };

    const template = await rotaShiftService.updateTemplate(id, updateData, req.tenantContext);

    return successResponse(res, {
      message: 'Shift template updated successfully',
      data: template
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete shift template (soft delete)
 * @route DELETE /api/v1/rota-shifts/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteShiftTemplate = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { force = false } = req.query;

    await rotaShiftService.deleteTemplate(id, req.tenantContext, { force: force === 'true' });

    return successResponse(res, {
      message: 'Shift template deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Duplicate shift template
 * @route POST /api/v1/rota-shifts/:id/duplicate
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const duplicateShiftTemplate = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { name, modifications = {} } = req.body;

    const duplicatedTemplate = await rotaShiftService.duplicateTemplate(
      id, 
      { name, ...modifications }, 
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Shift template duplicated successfully',
      data: duplicatedTemplate
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Get template usage analytics
 * @route GET /api/v1/rota-shifts/:id/usage
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getTemplateUsage = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { startDate, endDate, period = '30d' } = req.query;

    const usage = await rotaShiftService.getTemplateUsage(id, {
      startDate,
      endDate,
      period
    }, req.tenantContext);

    return successResponse(res, {
      message: 'Template usage analytics retrieved successfully',
      data: usage
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get template categories
 * @route GET /api/v1/rota-shifts/categories
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getTemplateCategories = async (req, res, next) => {
  try {
    const categories = await rotaShiftService.getTemplateCategories(req.tenantContext);

    return successResponse(res, {
      message: 'Template categories retrieved successfully',
      data: categories
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Search templates with advanced filters
 * @route GET /api/v1/rota-shifts/search
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const searchTemplates = async (req, res, next) => {
  try {
    const {
      q: query,
      filters = {},
      page = 1,
      limit = 10
    } = req.query;

    const searchResults = await rotaShiftService.searchTemplates(
      query,
      { ...filters, page: parseInt(page), limit: parseInt(limit) },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Template search completed successfully',
      data: searchResults.templates,
      pagination: searchResults.pagination,
      searchQuery: query
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk create templates
 * @route POST /api/v1/rota-shifts/bulk-create
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkCreateTemplates = async (req, res, next) => {
  try {
    const { templates } = req.body;

    if (!templates || !Array.isArray(templates) || templates.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Templates array is required'
      });
    }

    const result = await rotaShiftService.bulkCreateTemplates(templates, req.tenantContext);

    return successResponse(res, {
      message: `Bulk template creation completed: ${result.successful} successful, ${result.failed} failed`,
      data: result
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk update templates
 * @route PUT /api/v1/rota-shifts/bulk-update
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkUpdateTemplates = async (req, res, next) => {
  try {
    const { updates } = req.body;

    if (!updates || !Array.isArray(updates) || updates.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Updates array is required'
      });
    }

    const result = await rotaShiftService.bulkUpdateTemplates(updates, req.tenantContext);

    return successResponse(res, {
      message: `Bulk template update completed: ${result.successful} successful, ${result.failed} failed`,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  // Core CRUD Operations
  getAllShiftTemplates,
  getShiftTemplateById,
  createShiftTemplate,
  updateShiftTemplate,
  deleteShiftTemplate,
  
  // Template Library Features
  duplicateShiftTemplate,
  getTemplateUsage,
  getTemplateCategories,
  searchTemplates,
  
  // Bulk Operations
  bulkCreateTemplates,
  bulkUpdateTemplates
};
