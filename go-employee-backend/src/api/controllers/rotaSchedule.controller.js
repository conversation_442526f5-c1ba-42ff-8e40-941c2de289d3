'use strict';

/**
 * Rota Schedule Controller - PRD Implementation
 * 
 * Handles schedule management according to PRD specifications:
 * - Schedule lifecycle management (draft → published → archived)
 * - Instance generation from templates
 * - Employee assignment workflow
 * - Schedule status control and permissions
 * - Auto-schedule integration
 */

const rotaScheduleService = require('../../services/rota/rotaScheduleService');
const autoScheduleService = require('../../services/rota/autoScheduleService');
const { successResponse } = require('../../common/utils/response');
const { ValidationError, NotFoundError } = require('../../common/errors');
const moment = require('moment');

/**
 * Get all schedules with advanced filtering
 * @route GET /api/v1/rota-schedules
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getAllSchedules = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      status ='draft',
      departmentId,
      startDate,
      endDate,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC'
    } = req.query;

    const filters = {
      page: parseInt(page),
      limit: parseInt(limit),
      status,
      departmentId: departmentId ? parseInt(departmentId) : null,
      startDate,
      endDate,
      search,
      sortBy,
      type:'template',
      scheduleType: 'manual',
      sortOrder
    };

    const result = await rotaScheduleService.getAllSchedules(filters, req.tenantContext);

    return successResponse(res, {
      message: 'Schedules retrieved successfully',
      data: result.schedules,
      pagination: result.pagination,
      filters: filters
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get schedule by ID with detailed information
 * @route GET /api/v1/rota-schedules/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getScheduleById = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { 
      includeInstances = true, 
      includeAssignments = true,
      includeStatistics = true 
    } = req.query;

    const options = {
      includeInstances,
      includeAssignments,
      includeStatistics,
    };

    const schedule = await rotaScheduleService.getScheduleById(id, req.tenantContext, options);

    if (!schedule) {
      return res.status(404).json({
        success: false,
        message: 'Schedule not found'
      });
    }

    return successResponse(res, {
      message: 'Schedule retrieved successfully',
      data: schedule
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create new schedule
 * @route POST /api/v1/rota-schedules
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createSchedule = async (req, res, next) => {
  try {
    const scheduleData = {
      ...req.body,
      companyId: req.tenantContext.companyId,
      businessUnitId: req.body.businessUnitId || req.tenantContext.businessUnitId,
      createdById: req.tenantContext.userId,
      scheduleType:'manual'
    };

    const schedule = await rotaScheduleService.createSchedule(scheduleData, req.tenantContext);

    return successResponse(res, {
      message: 'Schedule created successfully',
      data: schedule,
      meta: {
        totalShiftInstances: schedule.shiftInstances?.length || 0,
        fillRate: schedule.metrics?.fillRate || '0%'
      }
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update schedule with hybrid approach support (Enhanced)
 * @route PUT /api/v1/rota-schedules/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateSchedule = async (req, res, next) => {
  try {
    const { id } = req.params;
    const updateData = req.body;

    const result = await rotaScheduleService.updateSchedule(
      parseInt(id),
      updateData,
      req.tenantContext
    );

    // Extract update summary if available
    const { updateSummary, ...schedule } = result;

    return successResponse(res, {
      message: 'Schedule updated successfully',
      data: schedule,
      meta: {
        shiftInstancesModified: updateSummary?.shiftInstancesModified || 0,
        lastModified: updateSummary?.lastModified,
        modifiedBy: updateSummary?.modifiedBy,
        totalShiftInstances: schedule.shiftInstances?.length || 0,
        fillRate: schedule.metrics?.fillRate || '0%'
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete schedule
 * @route DELETE /api/v1/rota-schedules/:id
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteSchedule = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { force = false } = req.query;

    await rotaScheduleService.deleteSchedule(id, req.tenantContext, { force: force === 'true' });

    return successResponse(res, {
      message: 'Schedule deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk assign employees to schedule shifts (Enhanced for scale)
 * @route POST /api/v1/rota-schedules/:id/bulk-assign
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkAssignEmployees = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { assignments, strategy = 'manual' } = req.body;

    if (!assignments || !Array.isArray(assignments) || assignments.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Assignments array is required and cannot be empty'
      });
    }

    const results = await rotaScheduleService.bulkAssignEmployees(
      parseInt(id),
      assignments,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Bulk assignment completed',
      data: results,
      meta: {
        strategy,
        totalProcessed: results.totalProcessed,
        successRate: `${((results.successful.length / results.totalProcessed) * 100).toFixed(1)}%`
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update shift instance requirements (Schedule-specific modifications)
 * @route PUT /api/v1/rota-schedules/shift-instances/:instanceId/requirements
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateShiftInstanceRequirements = async (req, res, next) => {
  try {
    const { instanceId } = req.params;
    const requirements = req.body;

    const updatedInstance = await rotaScheduleService.updateShiftInstanceRequirements(
      parseInt(instanceId),
      requirements,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Shift instance requirements updated successfully',
      data: updatedInstance
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Generate auto-schedule - ENHANCED for dual mode
 * @route POST /api/v1/rota-schedules/:id/auto-generate (existing schedule mode)
 * @route POST /api/v1/rota-schedules/auto-generate (template-based mode)
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const generateAutoSchedule = async (req, res, next) => {
  try {
    const { id } = req.params; // Optional - only for existing schedule mode
    const {
      templateMode,
      forecastMode, // NEW: Demand forecasting mode
      departmentIds,
      constraints = {},
      demandForecast = [],
      enableForecastOverride = false
    } = req.body;

    let scheduleRequest;

    // MODE 1: Template-based generation (create new schedule from template)
    if (templateMode) {
      console.log('🎯 Template-based schedule generation mode');

      const {
        shiftTemplateId,
        startDate,
        endDate,
        scheduleCategory,
        customName,
        customDescription
      } = templateMode;

      // Get template details for auto-generating name/description
      const template = await rotaScheduleService.getShiftTemplateById(shiftTemplateId, req.tenantContext);
      if (!template) {
        return res.status(404).json({
          success: false,
          message: 'Shift template not found'
        });
      }

      // Auto-generate schedule name and description using service
      const generatedName = customName || rotaScheduleService.generateScheduleName(template, startDate, endDate);
      const generatedDescription = customDescription || rotaScheduleService.generateScheduleDescription(template, startDate, endDate, scheduleCategory);

      // ✅ ENHANCED: Create new auto-generated schedule with preview status
      const newSchedule = await rotaScheduleService.createSchedule({
        name: generatedName,
        description: generatedDescription,
        startDate,
        endDate,
        category: scheduleCategory,

        // ✅ AUTO-SCHEDULE: Set type and status
        scheduleType: 'auto_generated', // Differentiate from manual schedules
        status: 'preview', // Auto-generated schedules start in preview

        type:'template',
        businessUnitId: req.tenantContext.businessUnitId,
        createdById:req.tenantContext.userId
      }, req.tenantContext);

      scheduleRequest = {
        mode: 'template',
        scheduleId: newSchedule.id,
        shiftTemplateId,
        businessUnitId: req.tenantContext.businessUnitId,
        startDate,
        endDate,
        constraints,
        demandForecast,
        enableForecastOverride
      };

      console.log(`✅ New schedule created: ${newSchedule.id} - ${generatedName}`);
    }
    // MODE 2: Demand forecasting-based generation (create new schedule from forecasts)
    else if (forecastMode) {
      console.log('🎯 Demand forecasting-based schedule generation mode');

      const {
        startDate,
        endDate,
        scheduleCategory = 'custom',
        customName,
        customDescription
      } = forecastMode;

      // Auto-generate schedule name and description for forecast-based schedules
      const generatedName = customName || `Forecast Schedule ${moment(startDate).format('MMM DD')} - ${moment(endDate).format('MMM DD, YYYY')}`;
      const generatedDescription = customDescription || `Auto-generated schedule based on demand forecasting from ${startDate} to ${endDate}`;

      // Create new forecast-based schedule
      const newSchedule = await rotaScheduleService.createSchedule({
        name: generatedName,
        description: generatedDescription,
        startDate,
        endDate,
        category: scheduleCategory,
        scheduleType: 'auto_generated',
        status: 'preview',
        type: 'auto', // Using 'auto' for demand forecasting-based schedules
        businessUnitId: req.tenantContext.businessUnitId,
        createdById: req.tenantContext.userId
      }, req.tenantContext);

      scheduleRequest = {
        mode: 'forecast',
        scheduleId: newSchedule.id,
        businessUnitId: req.tenantContext.businessUnitId,
        startDate,
        endDate,
        constraints
      };

      console.log(`✅ New forecast-based schedule created: ${newSchedule.id} - ${generatedName}`);
    }
    // MODE 3: Existing schedule mode (current functionality)
    else if (id && departmentIds) {
      console.log('🎯 Existing schedule generation mode');

      // Get existing schedule details
      const schedule = await rotaScheduleService.getScheduleById(id, req.tenantContext);
      if (!schedule) {
        return res.status(404).json({
          success: false,
          message: 'Schedule not found'
        });
      }

      scheduleRequest = {
        mode: 'existing',
        scheduleId: id,
        businessUnitId: schedule.businessUnitId,
        departmentIds: departmentIds || [],
        startDate: schedule.startDate,
        endDate: schedule.endDate,
        constraints,
        demandForecast,
        enableForecastOverride
      };

      console.log(`✅ Using existing schedule: ${id} - ${schedule.name}`);
    }
    else {
      return res.status(400).json({
        success: false,
        message: 'Invalid request: Provide templateMode for template-based schedule, forecastMode for demand forecasting-based schedule, or use existing schedule with departmentIds'
      });
    }

    // Generate auto-schedule using service
    const result = await autoScheduleService.generateAutoSchedule(scheduleRequest, req.tenantContext);

    return successResponse(res, {
      message: `Auto-schedule generated successfully using ${scheduleRequest.mode} mode`,
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * ✅ ENHANCED: Move auto-generated schedule from preview to draft
 * @route POST /api/v1/rota-schedules/:id/approve-preview
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const approvePreview = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { notes } = req.body;

    // Get schedule and validate it's in preview status
    const schedule = await rotaScheduleService.getScheduleById(id, req.tenantContext);

    if (!schedule) {
      return res.status(404).json({
        success: false,
        message: 'Schedule not found'
      });
    }

    if (schedule.scheduleType !== 'auto_generated') {
      return res.status(400).json({
        success: false,
        message: 'Only auto-generated schedules can be approved from preview'
      });
    }

    if (schedule.status !== 'preview') {
      return res.status(400).json({
        success: false,
        message: `Schedule is not in preview status. Current status: ${schedule.status}`
      });
    }

    // Update schedule status to draft
    const updatedSchedule = await rotaScheduleService.updateSchedule(
      id,
      {
        status: 'draft',
        approvedAt: new Date(),
        approvedBy: req.tenantContext.userId,
        approvalNotes: notes
      },
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Auto-generated schedule approved and moved to draft successfully',
      data: updatedSchedule
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Publish schedule with comprehensive validation and notifications
 * @route POST /api/v1/rota-schedules/:id/publish
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const publishSchedule = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      publishDate,
      notifyEmployees = true,
      lockAssignments = true,
      publishNotes,
      skipValidation = false,
      forcePublish = false
    } = req.body;

    console.log(`🚀 Publishing schedule ${id} with options:`, {
      notifyEmployees,
      lockAssignments,
      skipValidation,
      forcePublish
    });

    const publishData = {
      publishDate: publishDate || new Date(),
      notifyEmployees,
      lockAssignments,
      publishNotes,
      skipValidation,
      forcePublish,
      publishedBy: req.tenantContext.userId
    };

    const result = await rotaScheduleService.publishSchedule(id, publishData, req.tenantContext);

    // Enhanced response with validation results
    const response = {
      message: 'Schedule published successfully',
      data: {
        schedule: result,
        publishInfo: {
          publishedAt: result.publishedAt,
          publishedBy: result.publishedBy,
          notificationsSent: publishData.notifyEmployees,
          assignmentsLocked: publishData.lockAssignments
        }
      }
    };

    // Include validation results if available
    if (result.publishValidation) {
      response.data.validation = {
        passed: result.publishValidation.passed,
        warnings: result.publishValidation.warnings,
        statistics: result.publishValidation.stats
      };
    }

    return successResponse(res, response);
  } catch (error) {
    console.error('❌ Schedule publish failed in controller:', error.message);
    next(error);
  }
};

/**
 * Archive schedule
 * @route POST /api/v1/rota-schedules/:id/archive
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const archiveSchedule = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason } = req.body;

    const archiveData = {
      reason,
      archivedBy: req.tenantContext.userId,
      archivedAt: new Date()
    };

    const schedule = await rotaScheduleService.archiveSchedule(id, archiveData, req.tenantContext);

    return successResponse(res, {
      message: 'Schedule archived successfully',
      data: schedule
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Clone schedule
 * @route POST /api/v1/rota-schedules/:id/clone
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const cloneSchedule = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      name,
      startDate,
      endDate,
      includeAssignments = false,
      modifications = {}
    } = req.body;

    const cloneData = {
      name,
      startDate,
      endDate,
      includeAssignments,
      modifications,
      createdById: req.tenantContext.userId
    };

    const clonedSchedule = await rotaScheduleService.cloneSchedule(id, cloneData, req.tenantContext);

    return successResponse(res, {
      message: 'Schedule cloned successfully',
      data: clonedSchedule
    }, 201);
  } catch (error) {
    next(error);
  }
};



/**
 * Generate instances for schedule
 * @route POST /api/v1/rota-schedules/:id/generate-instances
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const generateInstances = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      templateIds,
      startDate,
      endDate,
      enableForecastOverride = true,
      minConfidenceThreshold = 70
    } = req.body;

    const generationRequest = {
      scheduleId: id,
      templateIds,
      startDate,
      endDate,
      enableForecastOverride,
      minConfidenceThreshold
    };

    const result = await rotaScheduleService.generateScheduleInstances(generationRequest, req.tenantContext);

    return successResponse(res, {
      message: 'Schedule instances generated successfully',
      data: result
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Auto-assign employees to schedule
 * @route POST /api/v1/rota-schedules/:id/auto-assign
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const autoAssignEmployees = async (req, res, next) => {
  try {
    const { id } = req.params;
    const {
      respectAvailability = true,
      balanceWorkload = true,
      minimizeOvertime = true,
      maxConsecutiveDays = 7,
      minRestHours = 12,
      preferredEmployees = [],
      excludedEmployees = []
    } = req.body;

    const constraints = {
      respectAvailability,
      balanceWorkload,
      minimizeOvertime,
      maxConsecutiveDays,
      minRestHours,
      preferredEmployees,
      excludedEmployees
    };

    const result = await rotaScheduleService.autoAssignSchedule(id, constraints, req.tenantContext);

    return successResponse(res, {
      message: 'Auto-assignment completed successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Validate schedule
 * @route POST /api/v1/rota-schedules/:id/validate
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const validateSchedule = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { checkConflicts = true, checkCoverage = true } = req.body;

    const validation = await rotaScheduleService.validateSchedule(id, {
      checkConflicts,
      checkCoverage
    }, req.tenantContext);

    return successResponse(res, {
      message: 'Schedule validation completed',
      data: validation
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Cancel schedule
 * @route POST /api/v1/rota-schedules/:id/cancel
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const cancelSchedule = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { reason, notifyEmployees = true } = req.body;

    const result = await rotaScheduleService.cancelSchedule(id, {
      reason,
      notifyEmployees,
      cancelledBy: req.tenantContext.userId
    }, req.tenantContext);

    return successResponse(res, {
      message: 'Schedule cancelled successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get schedule conflicts
 * @route GET /api/v1/rota-schedules/:id/conflicts
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getScheduleConflicts = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { conflictType, severity } = req.query;

    const conflicts = await rotaScheduleService.getScheduleConflicts(id, {
      conflictType,
      severity
    }, req.tenantContext);

    return successResponse(res, {
      message: 'Schedule conflicts retrieved successfully',
      data: conflicts
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get schedule coverage analysis
 * @route GET /api/v1/rota-schedules/:id/coverage
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getScheduleCoverage = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { departmentId, date } = req.query;

    const coverage = await rotaScheduleService.getScheduleCoverage(id, {
      departmentId: departmentId ? parseInt(departmentId) : null,
      date
    }, req.tenantContext);

    return successResponse(res, {
      message: 'Schedule coverage analysis retrieved successfully',
      data: coverage
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get schedule statistics
 * @route GET /api/v1/rota-schedules/:id/statistics
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getScheduleStatistics = async (req, res, next) => {
  try {
    const { id } = req.params;

    const statistics = await rotaScheduleService.getScheduleStatistics(id, req.tenantContext);

    return successResponse(res, {
      message: 'Schedule statistics retrieved successfully',
      data: statistics
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  // Core CRUD Operations
  getAllSchedules,
  getScheduleById,
  createSchedule,
  updateSchedule,
  deleteSchedule,

  // Enhanced Schedule Management (Hybrid Approach)
  bulkAssignEmployees,
  updateShiftInstanceRequirements,

  // Schedule Workflow
  generateAutoSchedule,
  approvePreview, // ✅ ENHANCED: Approve auto-generated schedule preview
  publishSchedule,
  archiveSchedule,
  cloneSchedule,

  // Schedule Actions
  generateInstances,
  autoAssignEmployees,
  validateSchedule,
  cancelSchedule,

  // Schedule Analytics
  getScheduleStatistics,
  getScheduleConflicts,
  getScheduleCoverage
};
