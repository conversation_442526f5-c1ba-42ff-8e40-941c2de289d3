'use strict';

const { Policy, PolicyCategory, PolicyDepartment, PolicyAcknowledgement, Employee, Role, UserRole, User } = require('../../data/models');
const { Op } = require('sequelize');
const { ApiError, ValidationError } = require('../../common/errors');
const { getPaginationParams, getSortingParams } = require('../../common/utils/pagination');
const { successResponse, createPagination } = require('../../common/utils/response');
const moment = require('moment');
const emailService = require('../../services/email/emailService');

const getAllPolicies = async (req, res, next) => {
  try {
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'created_at', 'desc');
    
    const condition = { companyId: req?.tenantContext?.companyId };
    if (req.query.search) {
      condition[Op.or] = [
        {
          title : {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        },
        {
          description: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        },
        {
          content: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        }
      ]
    }
    if (req.query.status) condition.status = req.query.status;
    if (req.query.policy_category_id) condition.policyCategoryId = req.query.policy_category_id;
    
    // if (req.query.title) condition.title = { [Op.like]: `%${req.query.title}%` };

    
    const order = [[sorting.sortBy, sorting.sortOrder.toUpperCase()]];
    
    const policies = await Policy.findAndCountAll({
      where: condition,
      limit: pagination.limit,
      offset: pagination.offset,
      order,
      include: [
        {
          model: PolicyCategory,
          as: 'PolicyCategory',
          attributes: ['id', 'name']
        }
      ]
    });

    const modifiedPolicies = policies.rows.map( policy => {
      let policyPlain = policy.toJSON();
      let canApprove = false, myStatus = null;
      for (let approver of policyPlain?.approverIds) {
        if (approver?.id === req?.tenantContext?.employeeId) {
          canApprove = true;
          myStatus = approver?.status;
          break;
        }
      }
      return {
        ...policyPlain,
        canApprove,
        myStatus
      }
    });
  
    return successResponse(res, {
      message: 'Policies retrieved successfully',
      data: modifiedPolicies, // policies.rows,
      pagination: createPagination(pagination.page, pagination.limit, policies.count)
    });
  } catch (error) {
    next(error);
  }
};

const getPolicyById = async (req, res, next) => {
  try {
    const { id } = req.params;
    
    const policy = await Policy.findOne(
      {
        where: {
          id: id,
          companyId: req?.tenantContext?.companyId
        },
        include: [
          {
            model: PolicyCategory,
            as: 'PolicyCategory',
            attributes: ['id', 'name']
          }
        ]
    });
    
    if (!policy) {
      throw new ApiError(404, 'Policy not found');
    }
    
    res.status(200).json({
      success: true,
      status_code: 200,
      message: 'Policy retrieved successfully',
      result: policy,
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create new policy
 */
const createPolicy = async (req, res, next) => {
  const transaction = await Policy.sequelize.transaction();
  try {
    let { 
      title, 
      description, 
      content, 
      policy_category_id, 
      status = 'draft',
      effective_date,
      expiry_date,
      requires_acknowledgment = false,
      business_unit_id,
      target_department_ids,
      recurrence,
      approver_ids: approverIds
    } = req.body;


    if (approverIds?.length > 0) {
      let tempData = [];
      for(let id of approverIds) {
        tempData.push({ id: id, status: 'Pending'});
      }
      approverIds = tempData;
    }


    // Check if policy with same title already exists for this company
    const existingPolicy = await Policy.findOne({
      where: { 
        title, 
        companyId: req.tenantContext.companyId 
      },
      transaction
    });

    if (existingPolicy) {
      await transaction.rollback();
      return res.status(400).json({
        success: false,
        status_code: 400,
        message: 'Policy with this title already exists',
        result: {},
        time: Date.now()
      });
    }

    // Calculate renewal date based on recurrence
    let renewalDate = null;
    if (recurrence && effective_date) {
      renewalDate = moment(effective_date);
      switch (recurrence) {
        case 'ANNUALLY':
          renewalDate.add(1, 'year');
          break;
        case 'BI_ANNUALLY':
          renewalDate.add(6, 'months');
          break;
        case 'QUARTERLY':
          renewalDate.add(3, 'months');
          break;
        case 'MONTHLY':
          renewalDate.add(1, 'month');
          break;
      }
    }

    // Create policy
    const policyData = {
      title,
      description,
      content,
      policy_category_id: policy_category_id,
      status,
      effectiveDate: effective_date,
      expiryDate: expiry_date,
      requiresAcknowledgment: requires_acknowledgment,
      businessUnitId: business_unit_id || [req.tenantContext.businessUnitId],
      targetDepartmentIds: target_department_ids,
      companyId: req.tenantContext.companyId,
      policyCategoryId: policy_category_id,
      createdById: req?.tenantContext?.employeeId,
      recurrence,
      renewalDate,
      approverIds
    };

    const policy = await Policy.create(policyData, { transaction });

    for (let approver of approverIds) {
      let approverInfo = await Employee.findOne({
        where: {
          id: approver?.id
        }
      });
      if (approverInfo?.contactEmail) {
        let emailData = {
          firstName: `${approverInfo?.firstName}`,
          lastName: `${approverInfo?.lastName}`,
          policyName: policyData?.title,
          policyDescription: policyData?.description,
          policyStatus: policyData?.status,
          policyEffectiveDate: policyData?.effectiveDate,
          policyExpiryDate: policyData?.expiryDate,
          policyAction: 'create'
        };
        await emailService.sendMail(
          approverInfo?.contactEmail,
          emailData,
          `Action required for policy`,
          "policy.ejs"
        );
      }
    }

    await transaction.commit();

    return successResponse(res, {
      message: 'Policy created successfully',
      data: policy
    }, 201);

  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

/**
 * Get policy list with filters
 */
const getPolicyList = async (req, res, next) => {
  try {
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'desc');
    
    const condition = { companyId: req.tenantContext.companyId };
    
    if (req.query.title) condition.title = { [Op.like]: `%${req.query.title}%` };
    if (req.query.status) condition.status = req.query.status;
    if (req.query.categoryId) condition.categoryId = req.query.categoryId;
    if (req.query.businessUnitId) condition.businessUnitId = req.query.businessUnitId;

    const policies = await Policy.findAndCountAll({
      where: condition,
      limit: pagination.limit,
      offset: pagination.offset,
      order: [[sorting.sortBy, sorting.sortOrder.toUpperCase()]],
      include: [
        {
          model: PolicyCategory,
          as: 'category',
          attributes: ['id', 'name']
        },
        {
          model: PolicyDepartment,
          as: 'departments',
          attributes: ['departmentId']
        }
      ]
    });

    return successResponse(res, {
      message: 'Policies retrieved successfully',
      data: policies.rows,
      pagination: createPagination(pagination.page, pagination.limit, policies.count)
    });

  } catch (error) {
    next(error);
  }
};

/**
 * Update policy
 */
const updatePolicy = async (req, res, next) => {
  const transaction = await Policy.sequelize.transaction();
  try {
    const { id } = req.params;
    const updateData = req.body;

    updateData.policyCategoryId = updateData?.policy_category_id;
    updateData.businessUnitId = updateData?.business_unit_id;
    updateData.effectiveDate = updateData?.effective_date;
    updateData.expiryDate = updateData?.expiry_date;
    updateData.requiresAcknowledgment = updateData?.requires_acknowledgment;
    updateData.targetDepartmentIds = updateData?.target_department_ids;
    updateData.approverIds = updateData?.approver_ids;
    updateData.updatedById = req?.tenantContext?.employeeId;

    delete updateData.policy_category_id;
    delete updateData.business_unit_id;
    delete updateData.effective_date;
    delete updateData.expiry_date;
    delete updateData.requires_acknowledgment;
    delete updateData.target_department_ids;
    delete updateData.approver_ids;

    if (updateData?.approverIds?.length > 0) {
      let tempData = [];
      for(let id of updateData?.approverIds) {
        tempData.push({ id: id, status: 'Pending'});
      }
      updateData.approverIds = tempData;
    }

    const policy = await Policy.findOne({
      where: { 
        id: Number(id), 
        companyId: req.tenantContext.companyId 
      }
    });

    if (!policy) {
      await transaction.rollback();
      throw new ApiError(404, 'Policy not found');
    }

    // Update renewal date if recurrence or effective date changed
    if (updateData.recurrence || updateData.effective_date) {
      const effectiveDate = updateData.effective_date || policy.effectiveDate;
      const recurrence = updateData.recurrence || policy.recurrence;
      
      if (recurrence && effectiveDate) {
        let renewalDate = moment(effectiveDate);
        switch (recurrence) {
          case 'ANNUALLY':
            renewalDate.add(1, 'year');
            break;
          case 'BI_ANNUALLY':
            renewalDate.add(6, 'months');
            break;
          case 'QUARTERLY':
            renewalDate.add(3, 'months');
            break;
          case 'MONTHLY':
            renewalDate.add(1, 'month');
            break;
        }
        updateData.renewalDate = renewalDate.toDate();
      }
    }

    let updatedPolicy = await Policy.update(updateData, { where: { id: policy?.id }, transaction, returning: true });

    if (updatedPolicy[1]?.[0]?.dataValues) {
      let newPolicy = updatedPolicy[1]?.[0]?.dataValues;
      for (let approver of newPolicy?.approverIds) {
        let approverInfo = await Employee.findOne({
          where: {
            id: approver?.id
          }
        });
        if (approverInfo?.contactEmail) {
          let emailData = {
            firstName: `${approverInfo?.firstName}`,
            lastName: `${approverInfo?.lastName}`,
            policyName: newPolicy?.title,
            policyDescription: newPolicy?.description,
            policyStatus: newPolicy?.status,
            policyEffectiveDate: newPolicy?.effectiveDate,
            policyExpiryDate: newPolicy?.expiryDate,
            policyAction: 'update'
          };
          await emailService.sendMail(
            approverInfo?.contactEmail,
            emailData,
            `Action required for policy`,
            "policy.ejs"
          );
        }
      }
    }

    await transaction.commit();

    return successResponse(res, {
      message: 'Policy updated successfully',
      data: policy
    });

  } catch (error) {
    console.log('\n== error', error);
    await transaction.rollback();
    next(error);
  }
};

/**
 * Delete policy
 */
const deletePolicy = async (req, res, next) => {
  const transaction = await Policy.sequelize.transaction();
  try {
    const { id } = req.params;

    const policy = await Policy.findOne({
      where: { 
        id, 
        companyId: req.tenantContext.companyId 
      }
    });

    if (!policy) {
      throw new ApiError(404, 'Policy not found');
    }

    await policy.destroy({ transaction });

    await transaction.commit();

    return successResponse(res, {
      message: 'Policy deleted successfully',
      data: {}
    });

  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

const approveRejectPolicy = async (req, res, next) => {
  const transaction = await Policy.sequelize.transaction();
  try {
    const { id } = req.params;

    const policy = await Policy.findOne({
      where: { 
        id, 
        companyId: req.tenantContext.companyId 
      }
    });

    if (!policy) {
      throw new ApiError(404, 'Policy not found');
    }

    let employeeId = req?.tenantContext?.employeeId;
    let isApprover = false, isApprovedFinally = true;

    let newData = null;
    if (policy?.approverIds) {
      for (let approver of policy?.approverIds) {
        if (approver?.id === employeeId) {
          isApprover = true;
          approver.status = (req?.body?.decision === 'approve') ? 'Approved' : 'Rejected' 
        }
      }
      newData = policy?.approverIds.map(approver => 
        approver?.id === employeeId 
          ? { ...approver, status: (req?.body?.decision === 'approve') ? 'Approved' : 'Rejected' }
          : approver
      );
    }

    if (!isApprover) {
      throw new ApiError(404, 'Not authorized to perform the action');
    } else {
      policy.set('approverIds', newData);
      policy.changed('approverIds', true); // Required to Update JSONB
      await policy.save({ transaction });
    }

    for (let approver of newData) {
      if (approver?.status !== 'Approved') {
        isApprovedFinally = false;
      }
    }
    if (isApprovedFinally) {
      // Create policy acknowledgements for all users in the company
      let empCondition = { companyId: req.tenantContext.companyId };
      if (policy?.targetDepartmentIds) {
        empCondition.departmentId = {
          [Op.in]: policy?.targetDepartmentIds
        }
      }

      const companyEmployees = await Employee.findAll({
        where: empCondition,
        attributes: ['id'],
        transaction
      });

      if (companyEmployees?.length > 0) {
        const acknowledgementData = companyEmployees.map(emp => ({
          employeeId: emp.id,
          policyId: policy?.id,
          companyId: req.tenantContext.companyId,
          policyCategoryId: policy?.policy_category_id,
          status: 'pending'
        }));

        await PolicyAcknowledgement.bulkCreate(acknowledgementData, { transaction });
      }

      await policy.update({
        status: 'active'
      }, {
        transaction
      });

      await transaction.commit();
      return successResponse(res, {
        message: `Policy ${req?.body?.decision === 'approve' ? 'approved' : 'rejected' } successfully`,
        data: {}
      });
    } else {
      await transaction.commit();
      return successResponse(res, {
        message: `Policy ${req?.body?.decision === 'approve' ? 'approved' : 'rejected' } successfully`,
        data: {}
      });
    }

  } catch (error) {
    await transaction.rollback();
    next(error);
  }
};

const uploadPolicyAttachment = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

const getPolicyAttachments = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

const deletePolicyAttachment = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get policy acknowledgments
 */
const getPolicyAcknowledgments = async (req, res, next) => {
  try {
    const { id } = req.params;
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'desc');

    const policy = await Policy.findOne({
      where: { 
        id, 
        companyId: req.tenantContext.companyId 
      }
    });

    if (!policy) {
      throw new ApiError(404, 'Policy not found');
    }

    const condition = { policyId: id };
    if (req.query.status) condition.status = req.query.status;

    const acknowledgments = await PolicyAcknowledgement.findAndCountAll({
      where: condition,
      limit: pagination.limit,
      offset: pagination.offset,
      order: [[sorting.sortBy, sorting.sortOrder.toUpperCase()]],
      include: [
        {
          model: User,
          as: 'user',
          attributes: ['id', 'firstName', 'lastName', 'email', 'employeeId']
        }
      ]
    });

    return successResponse(res, {
      message: 'Policy acknowledgments retrieved successfully',
      data: acknowledgments.rows,
      pagination: createPagination(pagination.page, pagination.limit, acknowledgments.count)
    });

  } catch (error) {
    next(error);
  }
};

/**
 * Acknowledge policy
 */
const acknowledgePolicy = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { comments } = req.body;

    const policy = await Policy.findOne({
      where: { 
        id, 
        companyId: req.tenantContext.companyId 
      }
    });

    if (!policy) {
      throw new ApiError(404, 'Policy not found');
    }

    const acknowledgment = await PolicyAcknowledgement.findOne({
      where: {
        policyId: id,
        userId: req.user.id
      }
    });

    if (!acknowledgment) {
      throw new ApiError(404, 'Policy acknowledgment record not found');
    }

    if (acknowledgment.status === 'acknowledged') {
      throw new ApiError(400, 'Policy already acknowledged');
    }

    await acknowledgment.update({
      status: 'acknowledged',
      acknowledgedAt: new Date(),
      comments
    });

    return successResponse(res, {
      message: 'Policy acknowledged successfully',
      data: acknowledgment
    });

  } catch (error) {
    next(error);
  }
};

/**
 * Get employee policy acknowledgments
 */
const getEmployeePolicyAcknowledgments = async (req, res, next) => {
  try {
    const { employeeId } = req.params;
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'desc');

    const condition = { 
      userId: employeeId,
      companyId: req.tenantContext.companyId 
    };
    if (req.query.status) condition.status = req.query.status;

    const acknowledgments = await PolicyAcknowledgement.findAndCountAll({
      where: condition,
      limit: pagination.limit,
      offset: pagination.offset,
      order: [[sorting.sortBy, sorting.sortOrder.toUpperCase()]],
      include: [
        {
          model: Policy,
          as: 'policy',
          attributes: ['id', 'title', 'description', 'status', 'effectiveDate', 'expiryDate'],
          include: [
            {
              model: PolicyCategory,
              as: 'category',
              attributes: ['id', 'name']
            }
          ]
        }
      ]
    });

    return successResponse(res, {
      message: 'Employee policy acknowledgments retrieved successfully',
      data: acknowledgments.rows,
      pagination: createPagination(pagination.page, pagination.limit, acknowledgments.count)
    });

  } catch (error) {
    next(error);
  }
};

const getPolicyVersions = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

const getPolicyVersionById = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

const createPolicyVersion = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

const getAllPolicyCategories = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

const getPolicyAcknowledgmentAnalytics = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

const getPolicyCategoryAnalytics = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

const bulkDeletePolicies = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

const bulkUpdatePolicyStatus = async (req, res, next) => {
  try {
    res.status(501).json({ 
      success: false,
      status_code: 501,
      message: 'Not implemented yet',
      result: {},
      time: Date.now()
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Approve policy
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const approvePolicy = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { comment } = req.body; // Optional comment

    // Use policy workflow service for approval
    const approvalData = {
      comments: comment || 'Approved',
      actionBy: req.tenantContext.userId,
      actionDate: new Date()
    };

    const result = await policyWorkflowService.processPolicyApproval(id, approvalData, req.tenantContext);

    return successResponse(res, {
      message: result.message || 'Policy approved successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Reject policy
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const rejectPolicy = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { rejectionReason } = req.body;

    if (!rejectionReason) {
      throw new ValidationError('Rejection reason is required');
    }

    // Use policy workflow service for rejection
    const rejectionData = {
      rejectionReason: rejectionReason,
      actionBy: req.tenantContext.userId,
      actionDate: new Date()
    };

    const result = await policyWorkflowService.processPolicyRejection(id, rejectionData, req.tenantContext);

    return successResponse(res, {
      message: result.message || 'Policy rejected successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk approve policies
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkApprovePolicies = async (req, res, next) => {
  try {
    const { policyIds, comment } = req.body;

    if (!policyIds || !Array.isArray(policyIds) || policyIds.length === 0) {
      throw new ValidationError('Policy IDs array is required and cannot be empty');
    }

    console.log(`🔍 DEBUG: Bulk approving ${policyIds.length} policies:`, policyIds);

    const results = [];
    const errors = [];

    // Process each policy
    for (const policyId of policyIds) {
      try {
        console.log(`🔍 DEBUG: Processing policy approval for ID: ${policyId}`);

        const approvalData = {
          comments: comment || 'Bulk approved',
          actionBy: req.tenantContext.userId,
          actionDate: new Date()
        };

        await policyWorkflowService.processPolicyApproval(policyId, approvalData, req.tenantContext);

        results.push({
          policyId: policyId,
          success: true,
          message: 'Policy approved successfully'
        });

        console.log(`✅ DEBUG: Policy ${policyId} approved successfully`);
      } catch (error) {
        console.error(`❌ DEBUG: Error approving policy ${policyId}:`, error.message);
        errors.push({
          policyId: policyId,
          success: false,
          error: error.message
        });
      }
    }

    return successResponse(res, {
      message: `Bulk approval completed. ${results.length} successful, ${errors.length} failed.`,
      data: {
        successful: results,
        failed: errors,
        summary: {
          total: policyIds.length,
          successful: results.length,
          failed: errors.length
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk reject policies
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkRejectPolicies = async (req, res, next) => {
  try {
    const { policyIds, rejectionReason } = req.body;

    if (!policyIds || !Array.isArray(policyIds) || policyIds.length === 0) {
      throw new ValidationError('Policy IDs array is required and cannot be empty');
    }

    if (!rejectionReason) {
      throw new ValidationError('Rejection reason is required for bulk rejection');
    }

    console.log(`🔍 DEBUG: Bulk rejecting ${policyIds.length} policies:`, policyIds);

    const results = [];
    const errors = [];

    // Process each policy
    for (const policyId of policyIds) {
      try {
        console.log(`🔍 DEBUG: Processing policy rejection for ID: ${policyId}`);

        const rejectionData = {
          rejectionReason: rejectionReason,
          actionBy: req.tenantContext.userId,
          actionDate: new Date()
        };

        await policyWorkflowService.processPolicyRejection(policyId, rejectionData, req.tenantContext);

        results.push({
          policyId: policyId,
          success: true,
          message: 'Policy rejected successfully'
        });

        console.log(`✅ DEBUG: Policy ${policyId} rejected successfully`);
      } catch (error) {
        console.error(`❌ DEBUG: Error rejecting policy ${policyId}:`, error.message);
        errors.push({
          policyId: policyId,
          success: false,
          error: error.message
        });
      }
    }

    return successResponse(res, {
      message: `Bulk rejection completed. ${results.length} successful, ${errors.length} failed.`,
      data: {
        successful: results,
        failed: errors,
        summary: {
          total: policyIds.length,
          successful: results.length,
          failed: errors.length
        }
      }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  getAllPolicies,
  getPolicyById,
  createPolicy,
  updatePolicy,
  deletePolicy,
  approveRejectPolicy,
  uploadPolicyAttachment,
  getPolicyAttachments,
  deletePolicyAttachment,
  getPolicyAcknowledgments,
  acknowledgePolicy,
  getEmployeePolicyAcknowledgments,
  getPolicyVersions,
  getPolicyVersionById,
  createPolicyVersion,
  getAllPolicyCategories,
  getPolicyAcknowledgmentAnalytics,
  getPolicyCategoryAnalytics,
  bulkDeletePolicies,
  bulkUpdatePolicyStatus,
  getPolicyList,
  approvePolicy,
  rejectPolicy,
  bulkApprovePolicies,
  bulkRejectPolicies
};
