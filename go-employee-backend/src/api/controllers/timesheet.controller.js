const timesheetService = require('../../services/project/timesheetService');
const timesheetApprovalService = require('../../services/project/timesheetApprovalService');
const timesheetAnalyticsService = require('../../services/project/timesheetAnalyticsService');
const { successResponse } = require('../../common/utils/response');
const { Employee, UserTimesheet, Project, Attendance, sequelize } = require('../../data/models');
const moment = require('moment');
const { Op } = require('sequelize');
const { getPaginationParams, getSortingParams } = require('../../common/utils/pagination');
const { createPagination } = require('../../common/utils/response');
const xlsx = require('xlsx');

/**
 * Create a new timesheet entry
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createTimesheet = async (req, res, next) => {
  try {
    const timesheet = await timesheetService.createTimesheet(req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Timesheet created successfully',
      data: timesheet
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get timesheet by ID
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getTimesheetById = async (req, res, next) => {
  try {
    const timesheet = await timesheetService.getTimesheetById(
      req.params.id,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Timesheet retrieved successfully',
      data: timesheet
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get timesheets with filters and pagination
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getTimesheets = async (req, res, next) => {
  try {
    const filters = {
      employeeId: req.query.employeeId,
      taskId: req.query.taskId,
      projectId: req.query.projectId,
      status: req.query.status,
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      approverId: req.query.approverId,
      search: req.query.search
    };

    const pagination = {
      page: req.query.page,
      limit: req.query.limit,
      sortBy: req.query.sortBy,
      sortOrder: req.query.sortOrder
    };

    const result = await timesheetService.getTimesheets(filters, pagination, req.tenantContext);

    return successResponse(res, {
      message: 'Timesheets retrieved successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get employee's own timesheets
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getMyTimesheets = async (req, res, next) => {
  try {
    const filters = {
      employeeId: req.tenantContext.employeeId,
      taskId: req.query.taskId,
      projectId: req.query.projectId,
      status: req.query.status,
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      search: req.query.search
    };

    const pagination = {
      page: req.query.page,
      limit: req.query.limit,
      sortBy: req.query.sortBy || 'date',
      sortOrder: req.query.sortOrder || 'desc'
    };

    const result = await timesheetService.getTimesheets(filters, pagination, req.tenantContext);

    return successResponse(res, {
      message: 'My timesheets retrieved successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update timesheet
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateTimesheet = async (req, res, next) => {
  try {
    const timesheet = await timesheetService.updateTimesheet(
      req.params.id,
      req.body,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Timesheet updated successfully',
      data: timesheet
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Approve or reject timesheet
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const approveRejectTimesheet = async (req, res, next) => {
  try {
    const timesheet = await timesheetApprovalService.approveRejectTimesheet(
      req.params.id,
      req.body,
      req.tenantContext
    );

    return successResponse(res, {
      message: `Timesheet ${req.body.status.toLowerCase()} successfully`,
      data: timesheet
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Bulk approve or reject timesheets
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const bulkApproveRejectTimesheets = async (req, res, next) => {
  let alreadyRolled = { value: false };
  let transaction = await sequelize.transaction();
  try {
    let { timesheet_id, status, comment } = req.body;
    let timesheetIds = [...timesheet_id];

    if (!['Approved', 'Rejected', 'Onhold'].includes(status)) {
      throw new ValidationError('Status must be Approved, Rejected, or Onhold');
    }

    // Bulk approve or reject timesheets
    const result = await timesheetApprovalService.bulkApproveRejectTimesheetService(
      timesheetIds,
      { status, comment },
      req.tenantContext,
      transaction,
      alreadyRolled
    );
    if (!result && !alreadyRolled?.value) {
      console.log("\n====rollback1");
      alreadyRolled.value = true;
      await transaction.rollback();
    }
    if (!alreadyRolled?.value) {
      await transaction.commit();
    }
    return successResponse(res, {
      message: `Timesheet ${req.body.status.toLowerCase()} successfully`,
      data: ''
    });
  } catch (error) {
    if (!alreadyRolled?.value) {
      console.log("\n====rollback2");
      alreadyRolled.value = true;
      await transaction.rollback();
    }
    logger.error('Bulk approve/reject timesheets error:', error);
    return res.status(400).json(transformToMobileResponse('', `Bulk timesheet operation failed`, 400));
  }
};

/**
 * Get timesheets pending approval
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getPendingApprovals = async (req, res, next) => {
  try {
    const filters = {
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      search: req.query.search
    };

    const pagination = {
      page: req.query.page,
      limit: req.query.limit,
      sortBy: req.query.sortBy || 'date',
      sortOrder: req.query.sortOrder || 'desc'
    };

    const result = await timesheetApprovalService.getPendingApprovals(
      filters,
      pagination,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Pending approvals retrieved successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get weekly timesheet chart
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getWeeklyTimesheetChart = async (req, res, next) => {
  try {
    const filters = {
      employeeId: req.query.employeeId || req.tenantContext.employeeId,
      startDate: req.query.startDate,
      endDate: req.query.endDate
    };

    const chartData = await timesheetAnalyticsService.getWeeklyTimesheetChart(
      filters,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Weekly timesheet chart retrieved successfully',
      data: chartData
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get user timesheet graph
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getUserTimesheetGraph = async (req, res, next) => {
  try {
    const filters = {
      startDate: req.query.startDate,
      endDate: req.query.endDate,
      groupBy: req.query.groupBy
    };

    const graphData = await timesheetAnalyticsService.getUserTimesheetGraph(
      req.params.id,
      filters,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'User timesheet graph retrieved successfully',
      data: graphData
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get project timesheet analytics
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getProjectTimesheetAnalytics = async (req, res, next) => {
  try {
    const filters = {
      startDate: req.query.startDate,
      endDate: req.query.endDate
    };

    const analytics = await timesheetAnalyticsService.getProjectTimesheetAnalytics(
      req.params.projectId,
      filters,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Project timesheet analytics retrieved successfully',
      data: analytics
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get timesheet summary statistics
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getTimesheetSummary = async (req, res, next) => {
  try {
    const filters = {
      employeeId: req.query.employeeId,
      startDate: req.query.startDate,
      endDate: req.query.endDate
    };

    const summary = await timesheetAnalyticsService.getTimesheetSummary(
      filters,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Timesheet summary retrieved successfully',
      data: summary
    });
  } catch (error) {
    next(error);
  }
};

const todayTimesheetRecord = async (req, res, next) => {
  try {
    let processedEmployees = null;
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'desc');
    let baseWhere = {
      companyId: req?.tenantContext?.companyId
    };
    if (req?.tenantContext?.businessUnitId) {
      baseWhere.businessUnitId = req?.tenantContext?.businessUnitId;
    }
    if (req?.query?.date) {
      baseWhere.date = req?.query?.date;
    } else {
      baseWhere.date = moment.utc().format('YYYY-MM-DD');
    }

    let attendanceCondition = { ...baseWhere };
    if (req?.query?.departmentId) {
      attendanceCondition.departmentId = req?.query?.departmentId
    }
    let allEmployees = await Attendance.findAll({
      where: {
        ...attendanceCondition,
        clockInTime: {
          [Op.not]: null
        }
      }
    });
    let allEmpSet = new Set(allEmployees?.map(attendance => attendance.employeeId));

    let filledTimesheets = await UserTimesheet.findAll({
      where: {
        ...baseWhere,
        employeeId: {
          [Op.in]: [...allEmpSet]
        },
        status: {
          [Op.not]: 'Rejected'
        }
      }
    });
    let empFilledSet = new Set(filledTimesheets?.map(ts => ts?.employeeId));

    let reqEmployeesNF = null, reqEmployeesF = null;
    let notFilledSet = new Set([...allEmpSet]);
    for(let id of empFilledSet) {
      notFilledSet.delete(id);
    }
    reqEmployeesNF = await Employee.findAndCountAll({
      where: {
        id: {
          [Op.in]: [...notFilledSet]
        }
      },
      attributes: ['id', 'first_name', 'last_name', 'company_id', 'business_unit_id', 'department_id'],
      include: [
        {
          model: Attendance,
          as: 'attendances',
          attributes: ['work_hours'],
          where: {
            date: baseWhere?.date
          }
        }
      ],
      distinct: true,
      limit: pagination?.limit,
      offset: pagination?.offset
    });
    let reqEmployeesNFmodified = reqEmployeesNF?.rows?.map(
      row => {
        let attendanceTotalHours = 0;
        if (row?.dataValues?.attendances) {
          for (let attendance of row?.dataValues?.attendances) {
            attendanceTotalHours += Number(attendance?.dataValues?.work_hours);
          }
        }
        return {
          ...row?.dataValues,
          timesheets: null,
          timesheetTotalHours: null,
          attendanceTotalHours: attendanceTotalHours
        }
      }
    );

    reqEmployeesF = await Employee.findAndCountAll({
      where: {
        id: {
          [Op.in]: [...empFilledSet]
        }
      }, 
      attributes: ['id', 'first_name', 'last_name', 'company_id', 'business_unit_id', 'department_id'],
      include: [
        {
          model: Attendance,
          as: 'attendances',
          attributes: ['work_hours'],
          where: {
            date: baseWhere?.date
          }
        },
        {
          model: UserTimesheet,
          as: 'timesheets',
          attributes: [
            [sequelize.literal("ROUND(EXTRACT(HOUR FROM duration::time) + EXTRACT(MINUTE FROM duration::time) / 60.0, 2)"), 'duration_hours'],
            'project_id'
          ],
          where: {
            date: baseWhere?.date,
            deletedAt: null,
            status: {
              [Op.not]: 'Rejected'
            }
          }
        }
      ],
      distinct: true,
      limit: pagination?.limit,
      offset: pagination?.offset
    });

    processedEmployees = await Promise.all(
      reqEmployeesF?.rows?.map(async (row) => {
        let attendanceTotalHours = 0;
        if (row?.dataValues?.attendances) {
          for (let attendance of row?.dataValues?.attendances) {
            attendanceTotalHours += Number(attendance?.dataValues?.work_hours);
          }
        }
        
        let timesheetTotalHours = 0;
        if (row?.dataValues?.timesheets) {
          for (let ts of row?.dataValues?.timesheets) {
            let projectInfo = await Project.findOne({
              where: { id: ts?.dataValues?.project_id },
              attributes: ['projectName']
            });

            ts.dataValues.projectName = projectInfo?.dataValues?.projectName || '';

            timesheetTotalHours += Number(ts?.dataValues?.duration_hours);
          }
        }
        
        row.dataValues.timesheetTotalHours = Number(timesheetTotalHours.toFixed(2));
        row.dataValues.attendanceTotalHours = Number(attendanceTotalHours.toFixed(2));
        return row;
      }) || []
    );

    if (req?.query?.status === 'filled') {
      return successResponse(res, {
        message: `Successfully fetched employees who filled timesheet for date ${baseWhere?.date}`,
        data: processedEmployees || {},
        pagination: createPagination(pagination?.page, pagination?.limit, reqEmployeesF?.count)
      });
    } else if (req?.query?.status === 'not_filled') {
      return successResponse(res, {
        message: `Successfully fetched employees who didn't fill timesheet for date ${baseWhere?.date}`,
        data: reqEmployeesNFmodified || {},
        pagination: createPagination(pagination?.page, pagination?.limit, reqEmployeesNF?.count)
      });
    } else {
      return successResponse(res, {
        message: `Successfully fetched data for date ${baseWhere?.date}`,
        data: [...reqEmployeesNFmodified, ...processedEmployees] || {},
        pagination: createPagination(pagination?.page, pagination?.limit, reqEmployeesNF?.count)
      });
    }

  } catch (error) {
    next(error);
  }
}

const todayTimesheetRecordDownload = async (req, res, next) => {
  try {
    let baseWhere = {
      companyId: req?.tenantContext?.companyId
    };
    if (req?.tenantContext?.businessUnitId) {
      baseWhere.businessUnitId = req?.tenantContext?.businessUnitId;
    }
    if (req?.query?.date) {
      baseWhere.date = req?.query?.date;
    } else {
      baseWhere.date = moment.utc().format('YYYY-MM-DD');
    }

    let attendanceCondition = { ...baseWhere };
    if (req?.query?.departmentId) {
      attendanceCondition.departmentId = req?.query?.departmentId
    }
    let allEmployees = await Attendance.findAll({
      where: {
        ...attendanceCondition,
        clockInTime: {
          [Op.not]: null
        }
      }
    });
    let allEmpSet = new Set(allEmployees?.map(attendance => attendance.employeeId));

    let filledTimesheets = await UserTimesheet.findAll({
      where: {
        ...baseWhere,
        employeeId: {
          [Op.in]: [...allEmpSet]
        },
        status: {
          [Op.not]: 'Rejected'
        }
      }
    });
    let empFilledSet = new Set(filledTimesheets?.map(ts => ts?.employeeId));

    let notFilledSet = new Set([...allEmpSet]);
    for(let id of empFilledSet) {
      notFilledSet.delete(id);
    }

    let reqEmployeesNF = await Employee.findAndCountAll({
      where: {
        id: {
          [Op.in]: [...notFilledSet]
        }
      },
      attributes: ['id', 'first_name', 'last_name', 'company_id', 'business_unit_id', 'department_id'],
      include: [
        {
          model: Attendance,
          as: 'attendances',
          attributes: ['work_hours'],
          where: {
            date: baseWhere?.date
          }
        }
      ]
      // No pagination in Download
    });
    
    let reqEmployeesF = await Employee.findAndCountAll({
      where: {
        id: {
          [Op.in]: [...empFilledSet]
        }
      }, 
      attributes: ['id', 'first_name', 'last_name', 'company_id', 'business_unit_id', 'department_id'],
      include: [
        {
          model: Attendance,
          as: 'attendances',
          attributes: ['work_hours'],
          where: {
            date: baseWhere?.date
          }
        },
        {
          model: UserTimesheet,
          as: 'timesheets',
          attributes: [
            [sequelize.literal("ROUND(EXTRACT(HOUR FROM duration::time) + EXTRACT(MINUTE FROM duration::time) / 60.0, 2)"), 'duration_hours'],
            'project_id'
          ],
          where: {
            date: baseWhere?.date,
            deletedAt: null,
            status: {
              [Op.not]: 'Rejected'
            }
          }
        }
      ],
    });

    let reqEmployees = null;
    if (req?.query?.status === 'filled') {
      reqEmployees = reqEmployeesF;
    } else if (req?.query?.status === 'not_filled') {
      reqEmployees = reqEmployeesNF;
    } else {
      reqEmployees = [...reqEmployeesNF?.rows, ...reqEmployeesF?.rows];
    }

    const excelData = [];

    if (req?.query?.status) {
      for (let row of reqEmployees?.rows || []) {
        let attendanceTotalHours = 0;
        if (row?.dataValues?.attendances) {
          for (let attendance of row?.dataValues?.attendances) {
            attendanceTotalHours += Number(attendance?.dataValues?.work_hours);
          }
        }

        const baseEmployeeData = {
          'Date': baseWhere?.date || '',
          'Employee Id': row?.dataValues?.id || '',
          'First Name': row?.dataValues?.first_name || '',
          'Last Name': row?.dataValues?.last_name || '',
          'Company Id': row?.dataValues?.company_id || '',
          'Business Unit Id': row?.dataValues?.business_unit_id || '',
          'Department Id': row?.dataValues?.department_id || '',
          'Attendance Hours': Number(attendanceTotalHours.toFixed(2)) || '',
        };

        if (row?.dataValues?.timesheets && row.dataValues.timesheets.length > 0) {
          // Create one row per timesheet
          for (let ts of row.dataValues.timesheets) {
            let projectInfo = await Project.findOne({
              where: { id: ts?.dataValues?.project_id },
              attributes: ['projectName']
            });
            excelData.push({
              ...baseEmployeeData,
              'Project Id': ts?.dataValues?.project_id || '',
              'Project Name': projectInfo?.dataValues?.projectName || '',
              'Timesheet Hours': Number(ts?.dataValues?.duration_hours || 0),
            });
          }
        } else {
          // Employee with no timesheets
          excelData.push({
            ...baseEmployeeData,
            'Project Id': '',
            'Project Name': '',
            'Timesheet Hours': '',
          });
        }
      }
    } else {
      for (let row of reqEmployees || []) {
        let attendanceTotalHours = 0;
        if (row?.dataValues?.attendances) {
          for (let attendance of row?.dataValues?.attendances) {
            attendanceTotalHours += Number(attendance?.dataValues?.work_hours);
          }
        }

        const baseEmployeeData = {
          'Date': baseWhere?.date || '',
          'Employee Id': row?.dataValues?.id || '',
          'First Name': row?.dataValues?.first_name || '',
          'Last Name': row?.dataValues?.last_name || '',
          'Company Id': row?.dataValues?.company_id || '',
          'Business Unit Id': row?.dataValues?.business_unit_id || '',
          'Department Id': row?.dataValues?.department_id || '',
          'Attendance Hours': Number(attendanceTotalHours.toFixed(2)) || '',
        };

        if (row?.dataValues?.timesheets && row.dataValues.timesheets.length > 0) {
          // Create one row per timesheet
          for (let ts of row.dataValues.timesheets) {
            let projectInfo = await Project.findOne({
              where: { id: ts?.dataValues?.project_id },
              attributes: ['projectName']
            });
            excelData.push({
              ...baseEmployeeData,
              'Project Id': ts?.dataValues?.project_id || '',
              'Project Name': projectInfo?.dataValues?.projectName || '',
              'Timesheet Hours': Number(ts?.dataValues?.duration_hours || 0),
            });
          }
        } else {
          // Employee with no timesheets
          excelData.push({
            ...baseEmployeeData,
            'Project Id': '',
            'Project Name': '',
            'Timesheet Hours': '',
          });
        }

      }
    }

    const workbook = xlsx.utils.book_new();
    const worksheet = xlsx.utils.json_to_sheet(excelData);

    // Update column widths for new columns
    const columnWidths = [
      { wch: 15 }, 
      { wch: 8 },  
      { wch: 25 }, 
      { wch: 25 }, 
      { wch: 8 },  
      { wch: 8 },  
      { wch: 15 }, 
      { wch: 15 }, 
      { wch: 10 }, 
      { wch: 30 }, 
      { wch: 15 }, 
    ];

    worksheet['!cols'] = columnWidths;

    const range = xlsx.utils.decode_range(worksheet['!ref']);
    const headerStyle = {
      font: { bold: true },
      fill: { fgColor: { rgb: "CCCCCC" } }
    };
  
    xlsx.utils.book_append_sheet(workbook, worksheet, 'Daily Timesheet Records');
    
    const excelBuffer = xlsx.write(workbook, {
        type: 'buffer',
        bookType: 'xlsx'
    });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=daily_timesheet_records_${baseWhere?.date}_${ (req?.query?.status === 'not_filled') ? 'not_filled' : (req?.query?.status === 'filled') ? 'filled' : '' }.xlsx`);
    res.setHeader('Content-Length', excelBuffer.length);

    return res.send(excelBuffer);

  } catch (error) {
    next(error);
  }
}

const customTimesheetRecord = async (req, res, next) => {
  try {
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'desc');
    let timesheetCondn = {
      companyId: req?.tenantContext?.companyId,
      deletedAt: null
    };
    if (req?.tenantContext?.businessUnitId) {
      timesheetCondn.businessUnitId = req?.tenantContext?.businessUnitId;
    }

    if (req?.query?.startDate && req?.query?.endDate) {
      timesheetCondn.date = {
        [Op.between]: [req.query.startDate, req.query.endDate]
      };
    } else if (req?.query?.startDate) {
      timesheetCondn.date = {
        [Op.gte]: req.query.startDate
      };
    } else if (req?.query?.endDate) {
      timesheetCondn.date = {
        [Op.lte]: req.query.endDate
      };
    }

    if (req?.query?.projectId) {
      timesheetCondn.projectId = req?.query?.projectId;
    }

    let employeeCondn = {
      companyId: req?.tenantContext?.companyId  
    };
    if (req?.tenantContext?.businessUnitId) {
      employeeCondn.businessUnitId = req?.tenantContext?.businessUnitId;
    }
    if (req?.query?.search) {
      employeeCondn[Op.or] = [
        {
          firstName: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        },
        {
          middleName: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        },
        {
          lastName: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        }
      ]
    }
    if (req?.query?.departmentId) {
      employeeCondn.departmentId = req?.query?.departmentId
    }

    let reqTimesheets = await UserTimesheet.findAndCountAll({
      where: timesheetCondn,
      include: [
        {
          model: Employee,
          as: 'employee',
          where: employeeCondn,
          required: true
        }
      ],
      distinct: true,
      limit: pagination?.limit,
      offset: pagination?.offset
    })

    return successResponse(res, {
      message: `Successfully fetched data`,
      data: reqTimesheets?.rows || {},
      pagination: createPagination(pagination?.page, pagination?.limit, reqTimesheets?.count)
    });

  } catch (error) {
    next(error);
  }
}

const customTimesheetRecordDownload = async (req, res, next) => {
  try {
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'desc');
    let timesheetCondn = {
      companyId: req?.tenantContext?.companyId,
      deletedAt: null
    };
    if (req?.tenantContext?.businessUnitId) {
      timesheetCondn.businessUnitId = req?.tenantContext?.businessUnitId;
    }

    if (req?.query?.startDate && req?.query?.endDate) {
      timesheetCondn.date = {
        [Op.between]: [req.query.startDate, req.query.endDate]
      };
    } else if (req?.query?.startDate) {
      timesheetCondn.date = {
        [Op.gte]: req.query.startDate
      };
    } else if (req?.query?.endDate) {
      timesheetCondn.date = {
        [Op.lte]: req.query.endDate
      };
    }

    if (req?.query?.projectId) {
      timesheetCondn.projectId = req?.query?.projectId;
    }

    let employeeCondn = {
      companyId: req?.tenantContext?.companyId  
    };
    if (req?.tenantContext?.businessUnitId) {
      employeeCondn.businessUnitId = req?.tenantContext?.businessUnitId;
    }
    if (req?.query?.search) {
      employeeCondn[Op.or] = [
        {
          firstName: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        },
        {
          middleName: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        },
        {
          lastName: {
            [Op.iLike]: `%${req?.query?.search}%`
          }
        }
      ]
    }
    if (req?.query?.departmentId) {
      employeeCondn.departmentId = req?.query?.departmentId
    }

    let reqTimesheets = await UserTimesheet.findAndCountAll({
      where: timesheetCondn,
      include: [
        {
          model: Employee,
          as: 'employee',
          where: employeeCondn,
          required: true
        }
      ],
    });

    const excelData = reqTimesheets?.rows?.map(
      ts => ({
        'Id': ts?.dataValues?.id,
        'Company Id': ts?.dataValues?.companyId,
        'Business Unit Id': ts?.dataValues?.businessUnitId,
        'Employee Id': ts?.dataValues?.employeeId,
        'Task Id': ts?.dataValues?.taskId,
        'Project Id': ts?.dataValues?.projectId,
        'Date': ts?.dataValues?.date,
        'Duration': ts?.dataValues?.duration,
        'Notes': ts?.dataValues?.notes,
        'Status': ts?.dataValues?.status,
        'Approver Id 1': ts?.dataValues?.approverId,
        'Approver Id 2': ts?.dataValues?.approverId2,
        'Approver Status 1': ts?.dataValues?.approverStatus,
        'Approver Status 2': ts?.dataValues?.approverStatus2,
        'Emp First Name': ts?.dataValues?.employee?.dataValues?.firstName,
        'Emp Second Name': ts?.dataValues?.employee?.dataValues?.lastName
      })
    );

    const workbook = xlsx.utils.book_new();
    const worksheet = xlsx.utils.json_to_sheet(excelData);

    const columnWidths = [
      { wch: 8 },
      { wch: 8 },
      { wch: 8 },
      { wch: 8 },
      { wch: 8 },
      { wch: 8 },
      { wch: 25 },
      { wch: 25 },
      { wch: 25 },
      { wch: 25 },
      { wch: 8 },
      { wch: 8 },
      { wch: 25 },
      { wch: 25 },
      { wch: 25 },
      { wch: 25 },
    ];

    worksheet['!cols'] = columnWidths;

    const range = xlsx.utils.decode_range(worksheet['!ref']);
    const headerStyle = {
      font: { bold: true },
      fill: { fgColor: { rgb: "CCCCCC" } }
    };

    xlsx.utils.book_append_sheet(workbook, worksheet, 'Custom DSR Report')

    const excelBuffer = xlsx.write(workbook, {
      type: 'buffer',
      bookType: 'xlsx'
    });

    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename=custom_timesheet_report.xlsx`);
    res.setHeader('Content-Length', excelBuffer.length);

    return res.send(excelBuffer);

  } catch (error) {
    next(error);
  }
}

const generateTimesheet = async (req, res, next) => {
  try {

  } catch (error) {
    next(error);
  }
}

module.exports = {
  createTimesheet,
  getTimesheetById,
  getTimesheets,
  getMyTimesheets,
  updateTimesheet,
  approveRejectTimesheet,
  bulkApproveRejectTimesheets,
  getPendingApprovals,
  getWeeklyTimesheetChart,
  getUserTimesheetGraph,
  getProjectTimesheetAnalytics,
  getTimesheetSummary,
  todayTimesheetRecord,
  todayTimesheetRecordDownload,
  customTimesheetRecord,
  customTimesheetRecordDownload,
  generateTimesheet
};
