'use strict';

const projectService = require('../../services/project/projectService');
const clientService = require('../../services/project/clientService');
const taskService = require('../../services/project/taskService');
const milestoneService = require('../../services/project/milestoneService');
const projectResourceService = require('../../services/project/projectResourceService');
const { successResponse } = require('../../common/utils/response');
const { NotFoundError, ForbiddenError } = require('../../common/errors');
const { getPaginationParams, getSortingParams } = require('../../common/utils/pagination');
const { createPagination } = require('../../common/utils/response');
const { Project, ProjectBillingCycle, Role, ProjectResource, ServiceType, Holiday, sequelize } = require('../../data/models')
const { Op } = require('sequelize');
const moment = require('moment');
const { custom } = require('joi');
// const ServiceType = require('../../data/models').ServiceType;

/**
 * Validate project ownership for tenant context
 * @param {number} projectId - Project ID
 * @param {Object} tenantContext - Tenant context
 * @returns {Promise<Object>} Project if valid
 * @throws {NotFoundError|ForbiddenError} If project not found or access denied
 */
const validateProjectAccess = async (projectId, tenantContext) => {
  try {
    const project = await projectService.getProjectById(projectId, tenantContext);
    return project;
  } catch (error) {
    if (error instanceof NotFoundError) {
      // Don't reveal whether project exists in another tenant
      throw new NotFoundError('Project not found');
    }
    throw error;
  }
};

/**
 * Get all projects
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getProjects = async (req, res, next) => {
  try {
    let userRoles = req?.user?.roles;
    let companyAdminCheck = false, bUAdminCheck = false;

    let roleCondition = {
      name: {
        [Op.in]: userRoles
      }
    };
    if (req?.tenantContext?.companyId) {
      roleCondition.companyId = req?.tenantContext?.companyId;
    }
    if (req?.tenantContext?.businessUnitId) {
      roleCondition.businessUnitId = req?.tenantContext?.businessUnitId;
    }
    let roleCheck = await Role.findAll({
      where: roleCondition
    });

    for (let role of roleCheck) {
      let r = role.toJSON();
      if (r?.name === 'company_admin') {
        companyAdminCheck = true;
      } else if (r?.name?.toLowerCase().includes('admin')) {
        bUAdminCheck = true;
      }
    }

    // Get pagination and sorting parameters
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'projectName', 'asc');

    // Get filter parameters
    const filters = {};

    if (companyAdminCheck) {
      filters.companyId = req?.tenantContext?.companyId;
    } else if (bUAdminCheck) {
      filters.businessUnitId = [Number(req?.tenantContext?.businessUnitId)];
    }

    if (req.query.status) filters.status = req.query.status;
    if (req.query.projectStatus) filters.projectStatus = req.query.projectStatus;
    if (req.query.clientId) filters.clientId = req.query.clientId;
    if (req.query.groupId) filters.groupId = req.query.groupId;
    if (req.query.projectType) filters.projectType = req.query.projectType;
    if (req.query.projectSource) filters.projectSource = req.query.projectSource;
    if (req.query.billingType) filters.billingType = req.query.billingType;
    if (req.query.startDate) filters.startDate = req.query.startDate;
    if (req.query.endDate) filters.endDate = req.query.endDate;
    if (req.query.search) filters.search = req.query.search;
    if (req.query.employeeId) filters.employeeId = req.query.employeeId;
    if (req?.query?.businessUnitId) {
      if (filters?.businessUnitId) {
        filters.businessUnitId.push(Number(req?.query?.businessUnitId));
      } else {
        filters.businessUnitId = [Number(req?.query?.businessUnitId)];
      }
    }
    if (req?.query?.serviceTypeId) {
      if (Array.isArray(req?.query?.serviceTypeId)) {
        filters.serviceTypeId = req?.query?.serviceTypeId?.map(id => Number(id));
      } else {
        filters.serviceTypeId = req?.query?.serviceTypeId?.split(',').map(id => Number(id));
      }
    }

    // Get all projects
    const result = await projectService.getProjects(
      filters,
      pagination,
      sorting,
      req.tenantContext,
      (companyAdminCheck || bUAdminCheck)
    );

    
    return successResponse(res, {
      message: 'Projects retrieved successfully',
      data: result.projects,
      pagination: createPagination(pagination.page, pagination.limit, result.total)
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get project by ID
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getProjectById = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get project by ID
    const project = await projectService.getProjectById(id, req.tenantContext);

    return successResponse(res, {
      message: 'Project retrieved successfully',
      data: project
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create new project
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createProject = async (req, res, next) => {
  try {
    // Create project
    const project = await projectService.createProject(req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Project created successfully',
      data: project
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update project
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateProject = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Update project
    const project = await projectService.updateProject(id, req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Project updated successfully',
      data: project
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete project
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteProject = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Delete project
    await projectService.deleteProject(id, req.tenantContext);

    return successResponse(res, {
      message: 'Project deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get project statistics
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getProjectStatistics = async (req, res, next) => {
  try {
    // Get project statistics
    const stats = await projectService.getProjectStatistics(req.tenantContext);

    return successResponse(res, {
      message: 'Project statistics retrieved successfully',
      data: stats
    });
  } catch (error) {
    next(error);
  }
};

// Client Controllers

/**
 * Get all clients
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getClients = async (req, res, next) => {
  try {
    // Get pagination and sorting parameters
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'clientName', 'asc');

    // Get filter parameters
    const filters = {};
    if (req.query.status) filters.status = req.query.status;
    if (req.query.countryId) filters.countryId = req.query.countryId;
    if (req.query.search) filters.search = req.query.search;

    // Get all clients
    const result = await clientService.getClients(
      filters,
      pagination,
      sorting,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Clients retrieved successfully',
      data: result.clients,
      pagination: createPagination(pagination.page, pagination.limit, result.total)
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get client by ID
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getClientById = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get client by ID
    const client = await clientService.getClientById(id, req.tenantContext);

    return successResponse(res, {
      message: 'Client retrieved successfully',
      data: client
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create new client
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createClient = async (req, res, next) => {
  try {
    // Create client
    const client = await clientService.createClient(req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Client created successfully',
      data: client
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update client
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateClient = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Update client
    const client = await clientService.updateClient(id, req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Client updated successfully',
      data: client
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete client
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteClient = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Delete client
    await clientService.deleteClient(id, req.tenantContext);

    return successResponse(res, {
      message: 'Client deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

// Task Controllers

/**
 * Get all tasks
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getTasks = async (req, res, next) => {
  try {
    // Get pagination and sorting parameters
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'taskName', 'asc');

    // Get filter parameters
    const filters = {};
    if (req.query.projectId) filters.projectId = req.query.projectId;
    if (req.query.status) filters.status = req.query.status;
    if (req.query.customerId) filters.customerId = req.query.customerId;
    if (req.query.milestoneId) filters.milestoneId = req.query.milestoneId;
    if (req.query.moduleName) filters.moduleName = req.query.moduleName;
    if (req.query.startDate) filters.startDate = req.query.startDate;
    if (req.query.endDate) filters.endDate = req.query.endDate;
    if (req.query.search) filters.search = req.query.search;
    if (req.query.employeeId) filters.employeeId = req.query.employeeId;

    // Get all tasks
    const result = await taskService.getTasks(
      filters,
      pagination,
      sorting,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Tasks retrieved successfully',
      data: result.tasks,
      pagination: createPagination(pagination.page, pagination.limit, result.total)
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get task by ID
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getTaskById = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get task by ID
    const task = await taskService.getTaskById(id, req.tenantContext);

    return successResponse(res, {
      message: 'Task retrieved successfully',
      data: task
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create new task
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createTask = async (req, res, next) => {
  try {
    // Create task
    const task = await taskService.createTask(req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Task created successfully',
      data: task
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update task
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateTask = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Update task
    const task = await taskService.updateTask(id, req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Task updated successfully',
      data: task
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete task
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteTask = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Delete task
    await taskService.deleteTask(id, req.tenantContext);

    return successResponse(res, {
      message: 'Task deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get tasks by project
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getTasksByProject = async (req, res, next) => {
  try {
    const { projectId } = req.params;

    // Validate project access before retrieving tasks
    await validateProjectAccess(projectId, req.tenantContext);

    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'taskName', 'asc');
    const filters = {};
    if (req.query.status) filters.status = req.query.status;
    if (req.query.clientId) filters.customerId = req.query.clientId;
    // if (req.query.milestoneId) filters.milestoneId = req.query.milestoneId;
    // if (req.query.moduleName) filters.moduleName = req.query.moduleName;
    if (req.query.startDate) filters.startDate = req.query.startDate;
    if (req.query.endDate) filters.endDate = req.query.endDate;
    if (req.query.search) filters.search = req.query.search;
    if (req.query.employeeId) filters.employeeId = req.query.employeeId;

    // Get tasks by project
    const { rows : tasks, totalCount } = await taskService.getTasksByProject(projectId, filters, pagination, sorting, req.tenantContext);

    return successResponse(res, {
      message: 'Project tasks retrieved successfully',
      data: tasks,
      pagination: createPagination(pagination.page, pagination.limit, totalCount)
    });
  } catch (error) {
    next(error);
  }
};

// Milestone Controllers

/**
 * Get all milestones
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getMilestones = async (req, res, next) => {
  try {
    // Get pagination and sorting parameters
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'milestoneName', 'asc');

    // Get filter parameters
    const filters = {};
    if (req.query.projectId) filters.projectId = req.query.projectId;
    if (req.query.status) filters.status = req.query.status;
    if (req.query.customerId) filters.customerId = req.query.customerId;
    if (req.query.startDate) filters.startDate = req.query.startDate;
    if (req.query.endDate) filters.endDate = req.query.endDate;
    if (req.query.search) filters.search = req.query.search;

    // Get all milestones
    const result = await milestoneService.getMilestones(
      filters,
      pagination,
      sorting,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Milestones retrieved successfully',
      data: result.milestones,
      pagination: createPagination(pagination.page, pagination.limit, result.total)
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create new milestone
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createMilestone = async (req, res, next) => {
  try {
    // Create milestone
    const milestone = await milestoneService.createMilestone(req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Milestone created successfully',
      data: milestone
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update milestone
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateMilestone = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Update milestone
    const milestone = await milestoneService.updateMilestone(id, req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Milestone updated successfully',
      data: milestone
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete milestone
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteMilestone = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Delete milestone
    await milestoneService.deleteMilestone(id, req.tenantContext);

    return successResponse(res, {
      message: 'Milestone deleted successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get milestone details with tasks grouped by status
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getMilestoneDetails = async (req, res, next) => {
  try {
    const { id } = req.params;
    const tenantContext = req.tenantContext;

    // Get milestone with tasks
    const milestone = await milestoneService.getMilestoneById(id, tenantContext);
    
    if (!milestone) {
      throw new NotFoundError('Milestone does not exist');
    }

    // Group tasks by status
    const todoTasks = [];
    const inprogressTasks = [];
    const testingTasks = [];
    const completedTasks = [];

    if (milestone.tasks && milestone.tasks.length > 0) {
      milestone.tasks.forEach(task => {
        if (task.status === 'completed') {
          completedTasks.push(task);
        } else if (task.status === 'testing') {
          testingTasks.push(task);
        } else if (task.status === 'inprogress') {
          inprogressTasks.push(task);
        } else if (task.status === 'todo') {
          todoTasks.push(task);
        }
      });
    }

    // Format response
    const result = {
      ...milestone.toJSON(),
      todoTasks,
      workingTasks: inprogressTasks,
      testingTasks,
      completedTasks
    };
    
    // Remove the original tasks array to avoid duplication
    delete result.tasks;

    return successResponse(res, {
      message: 'Milestone details retrieved successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get current in-progress milestone details for a project
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getCurrentMilestoneDetails = async (req, res, next) => {
  try {
    const { projectId } = req.params;
    const tenantContext = req.tenantContext;

    // Find in-progress milestone for the project
    const milestone = await milestoneService.getCurrentMilestone(projectId, tenantContext);
    
    if (!milestone) {
      return successResponse(res, {
        message: 'No in-progress milestone found',
        data: {}
      });
    }

    // Group tasks by status
    const todoTasks = [];
    const inprogressTasks = [];
    const testingTasks = [];
    const completedTasks = [];

    if (milestone.tasks && milestone.tasks.length > 0) {
      milestone.tasks.forEach(task => {
        if (task.status === 'completed') {
          completedTasks.push(task);
        } else if (task.status === 'testing') {
          testingTasks.push(task);
        } else if (task.status === 'inprogress') {
          inprogressTasks.push(task);
        } else if (task.status === 'todo') {
          todoTasks.push(task);
        }
      });
    }

    // Format response
    const result = {
      ...milestone.toJSON(),
      todoTasks,
      workingTasks: inprogressTasks,
      testingTasks,
      completedTasks
    };
    
    // Remove the original tasks array to avoid duplication
    delete result.tasks;

    return successResponse(res, {
      message: 'Current milestone details retrieved successfully',
      data: result
    });
  } catch (error) {
    next(error);
  }
};

// Project Resource Controllers

/**
 * Add project resources
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const addProjectResources = async (req, res, next) => {
  try {
    // Add project resources
    const resources = await projectResourceService.addProjectResources(req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Project resources added successfully',
      data: resources
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Edit project resource
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const editProjectResource = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Edit project resource
    const resource = await projectResourceService.editProjectResource(id, req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Project resource updated successfully',
      data: resource
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Remove project resource
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const removeProjectResource = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Remove project resource
    await projectResourceService.removeProjectResource(id, req.tenantContext);

    return successResponse(res, {
      message: 'Project resource removed successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get project resources by project
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getProjectResourcesByProject = async (req, res, next) => {
  try {
    const { projectId } = req.params;

    // Validate project access before retrieving resources
    await validateProjectAccess(projectId, req.tenantContext);

    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'desc');
    const filters = {};
    if (req.query.search) filters.search = req.query.search;


    // Get project resources by project
    const resources = await projectResourceService.getProjectResourcesByProject(projectId, filters, pagination, sorting, req.tenantContext);

    return successResponse(res, {
      message: 'Project resources retrieved successfully',
      data: resources.projectResources,
      pagination: createPagination(pagination.page, pagination.limit, resources.total)
    });
  } catch (error) {
    next(error);
  }
};

// Module Controllers

/**
 * Create new module
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createModule = async (req, res, next) => {
  try {
    // Create module using the dedicated module service
    const module = await projectService.createModule(req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Module created successfully',
      data: module
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Get project timeline
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getProjectTimeline = async (req, res, next) => {
  try {
    const { projectId } = req.params;

    // Validate project access before retrieving timeline
    await validateProjectAccess(projectId, req.tenantContext);

    // Get project timeline
    const timeline = await projectService.getProjectTimeline(projectId, req.tenantContext);

    return successResponse(res, {
      message: 'Project timeline retrieved successfully',
      data: timeline
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get project budget
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getProjectBudget = async (req, res, next) => {
  try {
    const { projectId } = req.params;

    // Validate project access before retrieving budget
    await validateProjectAccess(projectId, req.tenantContext);

    // Get project budget
    const budget = await projectService.getProjectBudget(projectId, req.tenantContext);

    return successResponse(res, {
      message: 'Project budget retrieved successfully',
      data: budget
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Update project budget
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateProjectBudget = async (req, res, next) => {
  try {
    const { projectId } = req.params;

    // Validate project access before updating budget
    await validateProjectAccess(projectId, req.tenantContext);

    // Update project budget
    const budget = await projectService.updateProjectBudget(
      projectId, 
      req.body, 
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Project budget updated successfully',
      data: budget
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get project Gantt chart data
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 * @returns {Promise<void>}
 */
const getProjectGantt = async (req, res, next) => {
  try {
    const { projectId } = req.params;
    const queryParams = req.query;
    const tenantContext = req.tenantContext;

    const ganttData = await projectService.getProjectGantt(projectId, queryParams, tenantContext);
    
    return successResponse(res, {
      message: 'Gantt chart data retrieved successfully',
      data: ganttData
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get project progress metrics
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 * @returns {Promise<void>}
 */
const getProjectProgress = async (req, res, next) => {
  try {
    const { projectId } = req.params;
    const tenantContext = req.tenantContext;

    const progressData = await projectService.getProjectProgress(projectId, tenantContext);
    
    return successResponse(res, {
      message: 'Project progress retrieved successfully',
      data: progressData
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get project metrics and KPIs
 * @param {Object} req - Request object
 * @param {Object} res - Response object
 * @param {Function} next - Next middleware function
 * @returns {Promise<void>}
 */
const getProjectMetrics = async (req, res, next) => {
  try {
    const { projectId } = req.params;
    const tenantContext = req.tenantContext;

    const metricsData = await projectService.getProjectMetrics(projectId, tenantContext);
    
    return successResponse(res, {
      message: 'Project metrics retrieved successfully',
      data: metricsData
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get project expenses
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getProjectExpenses = async (req, res, next) => {
  try {
    const { projectId } = req.params;

    // Validate project access before retrieving expenses
    await validateProjectAccess(projectId, req.tenantContext);

    // Get pagination and sorting parameters
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'expenseDate', 'desc');

    // Get filter parameters
    const filters = {};
    if (req.query.categoryId) filters.categoryId = req.query.categoryId;
    if (req.query.status) filters.status = req.query.status;
    if (req.query.startDate) filters.startDate = req.query.startDate;
    if (req.query.endDate) filters.endDate = req.query.endDate;
    if (req.query.search) filters.search = req.query.search;

    // Get project expenses
    const result = await projectService.getProjectExpenses(
      projectId,
      filters,
      pagination,
      sorting,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Project expenses retrieved successfully',
      data: result.expenses,
      pagination: createPagination(pagination.page, pagination.limit, result.total)
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create project expense
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createProjectExpense = async (req, res, next) => {
  try {
    const { projectId } = req.params;

    // Validate project access before creating expense
    await validateProjectAccess(projectId, req.tenantContext);

    // Create project expense
    const expense = await projectService.createProjectExpense(
      projectId,
      req.body,
      req.user.id,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Project expense created successfully',
      data: expense
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Approve project expense
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const approveProjectExpense = async (req, res, next) => {
  try {
    const { projectId, expenseId } = req.params;

    // Validate project access before approving expense
    await validateProjectAccess(projectId, req.tenantContext);

    // Approve project expense
    const expense = await projectService.approveProjectExpense(
      projectId,
      expenseId,
      req.user.id,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Project expense approved successfully',
      data: expense
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Reject project expense
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const rejectProjectExpense = async (req, res, next) => {
  try {
    const { projectId, expenseId } = req.params;
    const { reason } = req.body;

    // Validate project access before rejecting expense
    await validateProjectAccess(projectId, req.tenantContext);

    // Reject project expense
    const expense = await projectService.rejectProjectExpense(
      projectId,
      expenseId,
      reason,
      req.user.id,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Project expense rejected successfully',
      data: expense
    });
  } catch (error) {
    next(error);
  }
};

const getProjBillingCosting = async (req, res, next) => {
  try {
    let { page = 1, limit = 10, search, sort_by = 'createdAt', sort_order = 'DESC', status } = req.query;
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'createdAt', 'DESC');
    let projectCondition = {
      companyId: req?.tenantContext?.companyId,
    }
    if (search) {
        projectCondition[Op.or] = [
            { projectName: { [Op.iLike]: `%${search}%` } }
        ];
    }
    if (status) {
      projectCondition.status = status;
    }
    let resourceCondition = {
      projectId: req?.params?.id
    }
    const getProjBillingCycle = await ProjectBillingCycle.findAll({
      where: resourceCondition,
      include: [
        {
          model: Project,
          where: projectCondition
        }
      ],
      limit: pagination?.limit,
      offset: pagination?.offset,
      order: [[sorting?.sortBy, sorting?.sortOrder]]
    });

    return successResponse(res, {
      message: 'Billing Cycles Fetched Successfully',
      data: getProjBillingCycle
    });
  } catch (err) {
    console.log('\n== error in getting rows of billing cycle', err);
    next(err);
  }
}

const getProjBillingCostingV2 = async (req, res, next) => {
  try {
    const projectDetails = await projectService.getProjectById(req?.params?.id, req?.tenantContext);

    let result = projectDetails;
    const startDate = result?.startDate;
    let endDate = result?.endDate;
    if (endDate == null) {
      endDate = moment.utc().endOf('year');
    }
    const billingCycle = result?.billingCycle;
    const customBillingDays = result?.customBillingDays;
    const billingDay = result?.billingDay;
    const dateDayOfTheNextMonth = result?.dateDayOfTheNextMonth;

    let overallEstimatedHours = result?.overallEstmHours; 
    let overallEstimatedCost = result?.overallEstmCost;
    let tillDateEstimatedHours = result?.tillDateEstmHours;
    let tillDateEstimatedCost = result?.tillDateEstmCost;

    let addDays = null, addMonths = null;
    if (billingCycle === 'MONTHLY') {
      addMonths = 1;
    } else if (billingCycle === 'QUATERLY') {
      addMonths = 3;
    } else if (billingCycle === 'HALF-YEARLY') {
      addMonths = 6;
    } else if (billingCycle === 'YEARLY') {
      addMonths = 12;
    } else if (billingCycle === 'CUSTOM') {
      addDays = Number(customBillingDays);
    }

    let resultData = [];
    let tempStart = moment.utc(startDate);
    let tempEnd = null;
    if (addMonths) {
      tempEnd = moment.utc(startDate).add(addMonths - 1, 'months').endOf('month');
    } else if (addDays) {
      tempEnd = moment.utc(startDate).add(addDays, 'days');
    }

    let tempObj = {};
    tempObj.startDate = tempStart;
    tempObj.endDate = tempEnd;
    tempObj.billingCycle = result?.billingCycle;

    if (billingDay === 'FIRST_DAY_OF_NEXT_MONTH') {
      tempObj.billingDate = tempEnd.clone().add(1, 'days');
    } else if (billingDay === 'LAST_DAY_OF_THIS_MONTH') {
      tempObj.billingDate = tempEnd.clone();
    } else if (billingDay === 'N_NUMBER_DAY_OF_THE_NEXT_MONTH') {
      tempObj.billingDate = tempEnd.clone().add(dateDayOfTheNextMonth + 1, 'days');
    }

    let tempActualCost = await sequelize.query(
      `
        SELECT SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0) * pr.billing_cost) AS total_cost,
        SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0)) AS total_hours
        FROM user_timesheets ut
        INNER JOIN project_resources pr ON ut.project_id = pr.project_id 
                                AND ut.employee_id = pr.employee_id
                    AND ut.deleted_at IS NULL
        WHERE ut.project_id = ${Number(req?.params?.id)}
          AND ut.date >= '${String(tempStart?.clone().format('YYYY-MM-DD'))}'
          AND ut.date <= '${String(tempEnd?.clone().format('YYYY-MM-DD'))}' 
      `
    );
    tempObj.allocatedHours = Number( Number( (tempObj.endDate?.diff(tempObj.startDate, 'days') + 1) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * Number(overallEstimatedHours) ).toFixed(2);
    tempObj.allocatedCost = Number( Number( (tempObj.endDate?.diff(tempObj.startDate, 'days') + 1) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * Number(overallEstimatedCost) ).toFixed(2);
    tempObj.actualCost = Number(Number(tempActualCost[0][0]?.total_cost).toFixed(2));
    tempObj.actualHours = Number(Number(tempActualCost[0][0]?.total_hours).toFixed(2));
    tempObj.profitLoss = Number(tempObj?.allocatedCost - tempObj?.actualCost).toFixed(2);
    
    if (moment.utc().format('YYYY-MM-DD') >= tempEnd.clone().format('YYYY-MM-DD')) {
      tempObj.allocatedHoursTillDate = Number( Number( (tempObj.endDate?.diff(tempObj.startDate, 'days') + 1) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * Number(tillDateEstimatedHours) ).toFixed(2);
      tempObj.allocatedCostTillDate = Number( Number( (tempObj.endDate?.diff(tempObj.startDate, 'days') + 1) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * Number(tillDateEstimatedCost) ).toFixed(2);
      tempObj.profitLossTillDate = Number(tempObj?.allocatedCostTillDate - tempObj?.actualCost).toFixed(2);
    } else if (moment.utc().format('YYYY-MM-DD') < tempEnd.clone().format('YYYY-MM-DD')) {
      tempObj.allocatedHoursTillDate = Number( Number( (moment.utc().diff(tempObj.startDate, 'days') + 1) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * Number(tillDateEstimatedHours) ).toFixed(2);
      tempObj.allocatedCostTillDate = Number( Number( (moment.utc().diff(tempObj.startDate, 'days') + 1) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * Number(tillDateEstimatedCost) ).toFixed(2);
      tempObj.profitLossTillDate = Number(tempObj?.allocatedCostTillDate - tempObj?.actualCost).toFixed(2);
    }
    resultData.push(tempObj);

    while(tempEnd.isBefore(moment.utc(endDate))) {
      tempStart = tempEnd.clone().add(1, 'days').startOf('day');
      if (addMonths) {
        tempEnd = tempEnd.clone().add(addMonths, 'months').endOf('month');
      } else if (addDays) {
        tempEnd = tempEnd.clone().add(addDays, 'days');
      }
      let tempObj2 = {};
      tempObj2.startDate = tempStart;
      tempObj2.endDate = tempEnd;
      tempObj2.billingCycle = result?.billingCycle;

      if (billingDay === 'FIRST_DAY_OF_NEXT_MONTH') {
        tempObj2.billingDate = tempEnd.clone().add(1, 'days');
      } else if (billingDay === 'LAST_DAY_OF_THIS_MONTH') {
        tempObj2.billingDate = tempEnd.clone();
      } else if (billingDay === 'N_NUMBER_DAY_OF_THE_NEXT_MONTH') {
        tempObj2.billingDate = tempEnd.clone().add(dateDayOfTheNextMonth + 1, 'days');
      }
      tempObj2.allocatedHours = Number( ( ( tempObj2.endDate.isSameOrBefore(moment.utc(endDate)) ? (tempObj2.endDate?.diff(tempStart, 'days') + 1) : (moment.utc(endDate)?.diff(tempStart, 'days') + 1) ) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * overallEstimatedHours ).toFixed(2);
      tempObj2.allocatedCost = Number( ( ( tempObj2.endDate.isSameOrBefore(moment.utc(endDate)) ? (tempObj2.endDate?.diff(tempStart, 'days') + 1) : (moment.utc(endDate)?.diff(tempStart, 'days') + 1) ) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * overallEstimatedCost ).toFixed(2);

      let tempActualCost2 = await sequelize.query(
        `
          SELECT SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0) * pr.billing_cost) AS total_cost,
          SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0)) AS total_hours
          FROM user_timesheets ut
          INNER JOIN project_resources pr ON ut.project_id = pr.project_id 
                                  AND ut.employee_id = pr.employee_id
                      AND ut.deleted_at IS NULL
          WHERE ut.project_id = ${Number(req?.params?.id)}
            AND ut.date >= '${String(tempStart.clone().format('YYYY-MM-DD'))}'
            AND ut.date <= '${String(tempEnd.clone().format('YYYY-MM-DD'))}' 
            AND ut.status = 'Approved'
        `
      );
      tempObj2.actualCost = Number(Number(tempActualCost2[0][0]?.total_cost).toFixed(2));
      tempObj2.actualHours = Number(Number(tempActualCost2[0][0]?.total_hours).toFixed(2));
      tempObj2.profitLoss = Number(tempObj2?.allocatedCost - tempObj2?.actualCost).toFixed(2);

      if (moment.utc().format('YYYY-MM-DD') >= tempEnd.clone().format('YYYY-MM-DD')) {
        tempObj2.allocatedHoursTillDate = Number( Number( ( tempObj2.endDate.isSameOrBefore(moment.utc(endDate)) ? (tempObj2.endDate?.diff(tempStart, 'days') + 1) : (moment.utc(endDate)?.diff(tempStart, 'days') + 1) ) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * Number(tillDateEstimatedHours) ).toFixed(2);
        tempObj2.allocatedCostTillDate = Number( Number( ( tempObj2.endDate.isSameOrBefore(moment.utc(endDate)) ? (tempObj2.endDate?.diff(tempStart, 'days') + 1) : (moment.utc(endDate)?.diff(tempStart, 'days') + 1) ) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) )  * Number(tillDateEstimatedCost) ).toFixed(2);
        tempObj2.profitLossTillDate = Number(tempObj2?.allocatedCostTillDate - tempObj2?.actualCost).toFixed(2);
      } else if (moment.utc().format('YYYY-MM-DD') < tempEnd.clone().format('YYYY-MM-DD')) {
        tempObj2.allocatedHoursTillDate = Number( Number( ( tempObj2.endDate.isSameOrBefore(moment.utc(endDate)) ? (tempObj2.endDate?.diff(tempStart, 'days') + 1) : (moment.utc(endDate)?.diff(tempStart, 'days') + 1) ) / (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * Number(tillDateEstimatedHours) ).toFixed(2);
        tempObj2.allocatedCostTillDate = Number( Number( ( tempObj2.endDate.isSameOrBefore(moment.utc(endDate)) ? (tempObj2.endDate?.diff(tempStart, 'days') + 1) : (moment.utc(endDate)?.diff(tempStart, 'days') + 1) )/ (moment.utc(endDate)?.diff(moment.utc(startDate), 'days') + 1) ) * Number(tillDateEstimatedCost) ).toFixed(2);
        tempObj2.profitLossTillDate = Number(tempObj2?.allocatedCostTillDate - tempObj2?.actualCost).toFixed(2);
      }

      resultData.push(tempObj2);
    }

    delete result?.projectResources;
    return successResponse(res, {
      message: 'Billing Cycles Fetched Successfully',
      data: { 
        project: result,
        cycleRows: resultData
      }
    });
  } catch (err) {
    console.log('\n== error in getting rows of billing cycle', err);
    next(err);
  }
}

const totalWorkingDaysInCycle = async (beginDate, closeDate, tenantContext) => {
  beginDate = moment.utc(beginDate);
  let beginDateClone = beginDate.clone();
  closeDate = moment.utc(closeDate);
  let totalDays = closeDate.diff(beginDate, 'days');
  let excludedDays = new Set();
  while(!beginDate.isSame(closeDate, 'day')) {
    beginDate = beginDate.add(1, 'days');
    if (beginDate.day() == 0 || beginDate.day() == 6) {
      excludedDays.add(beginDate?.format('YYYY-MM-DD'));
    }
  }
  let holidayCondition = {
    companyId: tenantContext?.companyId
  }
  if (tenantContext?.businessUnitId) {
    holidayCondition.businessUnitId = tenantContext?.businessUnitId
  }
  holidayCondition.date = {
    [Op.gte] : beginDateClone.format('YYYY-MM-DD'),
    [Op.lte] : closeDate.format('YYYY-MM-DD')
  }
  let holidays = await Holiday.findAll({
    where: holidayCondition
  });
  for (let holiday of holidays) {
    holiday = holiday.toJSON();
    excludedDays.add(holiday?.date);
  }
  return totalDays - excludedDays?.size;
}

const oneDayPlannedHoursAndCostCustomer = (projResos) => {
  let hours = 0, cost = 0;
  for (let pr of projResos) {
    if (!pr?.deletedAt && pr.isActive) {
      hours += Number(pr?.allocatedHoursPerDay);
      cost += Number(pr?.customerRatePerHour) * Number(pr?.allocatedHoursPerDay);
    }
  }
  return { hours, cost };
}

const oneDayPlannedHoursAndCostInternal = (projResos) => {
  let hours = 0, cost = 0;
  for (let pr of projResos) {
    if (!pr?.deletedAt && pr.isActive) {
      hours += Number(pr?.allocatedHoursPerDay);
      cost += Number(pr?.billingCost) * Number(pr?.allocatedHoursPerDay);
    }
  }
  return { hours, cost };
}

const billableCalculation = async (projResos, totalWorkingDays, cycleStart, cycleEnd) => {
  let hours = 0, cost = 0;
  for (let pr of projResos) {
    // compare actual hours of the pr with their planned hours
    let tempActualCost = await sequelize.query(
      `
        SELECT SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0) * pr.billing_cost) AS total_cost,
        SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0)) AS total_hours
        FROM user_timesheets ut
        INNER JOIN project_resources pr ON ut.project_id = pr.project_id 
                                AND ut.employee_id = pr.employee_id
                    AND ut.deleted_at IS NULL
        WHERE ut.project_id = ${Number(pr?.projectId)}
          AND ut.employee_id = ${pr?.employeeId}
          AND ut.date >= '${String(cycleStart.format('YYYY-MM-DD'))}'
          AND ut.date <= '${String(cycleEnd.format('YYYY-MM-DD'))}' 
      `
    );

    let prCycleActualHours = Number(Number(tempActualCost[0][0]?.total_hours).toFixed(2));
    let prCyclePlannedHours = totalWorkingDays * oneDayPlannedHoursAndCostCustomer([pr])?.hours;
    let prCycleBillingHours = Math.min(prCycleActualHours, prCyclePlannedHours);
    hours += prCycleBillingHours;
    cost += prCycleBillingHours * pr?.customerRatePerHour;
  }
  return { hours, cost };
}

const getProjBillingCostingV3 = async (req, res, next) => {
  try {
    const projectDetails = await projectService.getProjectById(req?.params?.id, req?.tenantContext);
    let plannedProfitSum = 0, internalPlannedCostSum = 0, netProfitSum = 0, actualCostDSRSum = 0;
    let plannedEmployeeHoursSum = 0, billingPlannedCostSum = 0, billableHoursSum = 0, actualHoursDSRSum = 0, actualBillableCostSum = 0;

    let result = projectDetails;
    const startDate = result?.startDate;
    let endDate = result?.endDate;
    if (endDate == null) {
      endDate = moment.utc().endOf('year');
    }
    const billingCycle = result?.billingCycle;
    const customBillingDays = result?.customBillingDays;
    const billingDay = result?.billingDay;
    const dateDayOfTheNextMonth = result?.dateDayOfTheNextMonth;

    let addDays = null, addMonths = null;
    if (billingCycle === 'MONTHLY') {
      addMonths = 1;
    } else if (billingCycle === 'QUATERLY') {
      addMonths = 3;
    } else if (billingCycle === 'HALF-YEARLY') {
      addMonths = 6;
    } else if (billingCycle === 'YEARLY') {
      addMonths = 12;
    } else if (billingCycle === 'CUSTOM') {
      addDays = Number(customBillingDays);
    }

    let resultData = [];
    let tempStart = moment.utc(startDate);
    let tempEnd = null;
    if (addMonths) {
      tempEnd = moment.utc(startDate).add(addMonths - 1, 'months').endOf('month');
    } else if (addDays) {
      tempEnd = moment.utc(startDate).add(addDays, 'days');
    }

    let tempObj = {};
    tempObj.startDate = tempStart;
    tempObj.endDate = tempEnd;
    tempObj.billingCycle = result?.billingCycle;
    if (tempObj?.endDate.format('YYYY-MM-DD') > projectDetails?.endDate) {
      tempObj.endDate = moment.utc(projectDetails?.endDate);
    }

    if (billingDay === 'FIRST_DAY_OF_NEXT_MONTH') {
      tempObj.billingDate = tempEnd.clone().add(1, 'days');
    } else if (billingDay === 'LAST_DAY_OF_THIS_MONTH') {
      tempObj.billingDate = tempEnd.clone();
    } else if (billingDay === 'N_NUMBER_DAY_OF_THE_NEXT_MONTH') {
      tempObj.billingDate = tempEnd.clone().add(dateDayOfTheNextMonth + 1, 'days');
    }

    let totalWorkingDaysTemp = await totalWorkingDaysInCycle(tempObj?.startDate, tempObj?.endDate, req?.tenantContext);
    let totalWorkingDays = totalWorkingDaysTemp + 1;

    let plannedTempCustomer = oneDayPlannedHoursAndCostCustomer(projectDetails?.projectResources);
    let plannedTempInternal = oneDayPlannedHoursAndCostInternal(projectDetails?.projectResources);

    tempObj.plannedEmployeeHours = Number(Number((plannedTempCustomer?.hours) * totalWorkingDays).toFixed(2));
    tempObj.billingPlannedCost = Number(Number((plannedTempCustomer?.cost) * totalWorkingDays).toFixed(2));
    tempObj.internalPlannedCost = Number(Number((plannedTempInternal?.cost) * totalWorkingDays).toFixed(2));
    tempObj.plannedProfit = Number(Number(tempObj?.billingPlannedCost - tempObj?.internalPlannedCost).toFixed(2));

    let tempActualCost = await sequelize.query(
      `
        SELECT SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0) * pr.billing_cost) AS total_cost,
        SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0)) AS total_hours
        FROM user_timesheets ut
        INNER JOIN project_resources pr ON ut.project_id = pr.project_id 
                                AND ut.employee_id = pr.employee_id
                    AND ut.deleted_at IS NULL
        WHERE ut.project_id = ${Number(req?.params?.id)}
          AND ut.date >= '${String(tempStart?.clone().format('YYYY-MM-DD'))}'
          AND ut.date <= '${String(tempObj?.endDate?.clone().format('YYYY-MM-DD'))}' 
      `
    );
    let tempBill = await billableCalculation(projectDetails?.projectResources, totalWorkingDays, tempObj?.startDate, tempObj?.endDate);
    tempObj.actualHoursDSR = Number(Number(tempActualCost[0][0]?.total_hours).toFixed(2));
    tempObj.billableHours = tempBill?.hours;    
    tempObj.actualCostDSR = Number(Number(tempActualCost[0][0]?.total_cost).toFixed(2));
    tempObj.actualBillableCost = tempBill?.cost;
    tempObj.netProfit = Number(Number(tempObj?.actualBillableCost - tempObj?.actualCostDSR).toFixed(2));
    
    // console.log('\n== cycleRow tempObj', tempObj);
    resultData.push(tempObj);
    plannedProfitSum += tempObj?.plannedProfit;
    internalPlannedCostSum += tempObj?.internalPlannedCost;
    netProfitSum += tempObj?.netProfit;
    actualCostDSRSum += tempObj?.actualCostDSR;

    plannedEmployeeHoursSum += tempObj?.plannedEmployeeHours;
    billingPlannedCostSum += tempObj?.billingPlannedCost;
    billableHoursSum += tempObj?.billableHours;
    actualHoursDSRSum += tempObj?.actualHoursDSR;
    actualBillableCostSum += tempObj?.actualBillableCost;

    while(tempEnd.isBefore(moment.utc(endDate))) {
      tempStart = tempEnd.clone().add(1, 'days').startOf('day');
      if (addMonths) {
        tempEnd = tempEnd.clone().add(addMonths, 'months').endOf('month');
      } else if (addDays) {
        tempEnd = tempEnd.clone().add(addDays, 'days');
      }
      let tempObj2 = {};
      tempObj2.startDate = tempStart;
      tempObj2.endDate = tempEnd;
      tempObj2.billingCycle = result?.billingCycle;

      if (tempObj2?.endDate.format('YYYY-MM-DD') > projectDetails?.endDate) {
        tempObj2.endDate = moment.utc(projectDetails?.endDate);
      }

      if (billingDay === 'FIRST_DAY_OF_NEXT_MONTH') {
        tempObj2.billingDate = tempEnd.clone().add(1, 'days');
      } else if (billingDay === 'LAST_DAY_OF_THIS_MONTH') {
        tempObj2.billingDate = tempEnd.clone();
      } else if (billingDay === 'N_NUMBER_DAY_OF_THE_NEXT_MONTH') {
        tempObj2.billingDate = tempEnd.clone().add(dateDayOfTheNextMonth + 1, 'days');
      }
      
      totalWorkingDaysTemp = await totalWorkingDaysInCycle(tempObj2?.startDate, tempObj2?.endDate, req?.tenantContext);
      totalWorkingDays = totalWorkingDaysTemp + 1;

      tempObj2.plannedEmployeeHours = Number(Number((plannedTempCustomer?.hours) * totalWorkingDays).toFixed(2));
      tempObj2.billingPlannedCost = Number(Number((plannedTempCustomer?.cost) * totalWorkingDays).toFixed(2));
      tempObj2.internalPlannedCost = Number(Number((plannedTempInternal?.cost) * totalWorkingDays).toFixed(2));
      tempObj2.plannedProfit = Number(Number(tempObj2?.billingPlannedCost - tempObj2?.internalPlannedCost).toFixed(2));

      let tempActualCost2 = await sequelize.query(
        `
          SELECT SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0) * pr.billing_cost) AS total_cost,
          SUM((EXTRACT(HOUR FROM ut.duration::TIME) + EXTRACT(MINUTE FROM ut.duration::TIME)/60.0)) AS total_hours
          FROM user_timesheets ut
          INNER JOIN project_resources pr ON ut.project_id = pr.project_id 
                                  AND ut.employee_id = pr.employee_id
                      AND ut.deleted_at IS NULL
          WHERE ut.project_id = ${Number(req?.params?.id)}
            AND ut.date >= '${String(tempStart.clone().format('YYYY-MM-DD'))}'
            AND ut.date <= '${String(tempObj2?.endDate?.clone().format('YYYY-MM-DD'))}' 
            AND ut.status = 'Approved'
        `
      );

      tempBill = await billableCalculation(projectDetails?.projectResources, totalWorkingDays, tempObj2?.startDate, tempObj2?.endDate);
      tempObj2.actualHoursDSR = Number(Number(tempActualCost2[0][0]?.total_hours).toFixed(2));
      tempObj2.billableHours = tempBill?.hours;    
      tempObj2.actualCostDSR = Number(Number(tempActualCost2[0][0]?.total_cost).toFixed(2));
      tempObj2.actualBillableCost = tempBill?.cost;
      tempObj2.netProfit = Number(Number(tempObj2?.actualBillableCost - tempObj2?.actualCostDSR).toFixed(2));
      // console.log('\n== cycleRow tempObj2', tempObj2);
      resultData.push(tempObj2);

      plannedProfitSum += tempObj2?.plannedProfit;
      internalPlannedCostSum += tempObj2?.internalPlannedCost;
      netProfitSum += tempObj2?.netProfit;
      actualCostDSRSum += tempObj2?.actualCostDSR

      plannedEmployeeHoursSum += tempObj2?.plannedEmployeeHours;
      billingPlannedCostSum += tempObj2?.billingPlannedCost;
      billableHoursSum += tempObj2?.billableHours;
      actualHoursDSRSum += tempObj2?.actualHoursDSR;
      actualBillableCostSum += tempObj2?.actualBillableCost;
    }

    result.plannedProfitPercentage = Number(( (plannedProfitSum / internalPlannedCostSum) * 100 ).toFixed(2));
    result.netProfitPercentage = Number( ((netProfitSum / actualCostDSRSum) * 100).toFixed(2));
    result.plannedEmployeeHoursSum = plannedEmployeeHoursSum;
    result.internalPlannedCostSum = internalPlannedCostSum;
    result.billingPlannedCostSum = billingPlannedCostSum;
    result.plannedProfitSum = plannedProfitSum;
    result.actualHoursDSRSum = actualHoursDSRSum;
    result.billableHoursSum = billableHoursSum;
    result.actualCostDSRSum = Number(actualCostDSRSum.toFixed(2));
    result.actualBillableCostSum = Number(actualBillableCostSum.toFixed(2));

    delete result?.projectResources;
    
    return successResponse(res, {
      message: 'Billing Cycles Fetched Successfully',
      data: { 
        project: result,
        cycleRows: resultData
      }
    });
  } catch (err) {
    console.log('\n== error in getting rows of billing cycle', err);
    next(err);
  }
}
/**
 * Download projects as Excel file with filters
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const downloadProjects = async (req, res, next) => {
  try {
    // Get filter parameters (same as getProjects)
    const filters = {};
    if (req.query.status) filters.status = req.query.status;
    if (req.query.projectStatus) filters.projectStatus = req.query.projectStatus;
    if (req.query.clientId) filters.clientId = req.query.clientId;
    if (req.query.groupId) filters.groupId = req.query.groupId;
    if (req.query.projectType) filters.projectType = req.query.projectType;
    if (req.query.projectSource) filters.projectSource = req.query.projectSource;
    if (req.query.billingType) filters.billingType = req.query.billingType;
    if (req.query.startDate) filters.startDate = req.query.startDate;
    if (req.query.endDate) filters.endDate = req.query.endDate;
    if (req.query.search) filters.search = req.query.search;

    // Get sorting parameters
    const sorting = getSortingParams(req.query, 'projectName', 'asc');

    // Get all projects without pagination for download
    const result = await projectService.getProjects(
      filters,
      { page: 1, limit: 10000 }, // Large limit to get all projects
      sorting,
      req.tenantContext
    );

    // Generate Excel file
    const { generateProjectExcel } = require('../../common/utils/excelGenerator');
    const excelBuffer = await generateProjectExcel(result);

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `projects-report-${timestamp}.xlsx`;

    // Set response headers for file download
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', excelBuffer.length);

    // Send the Excel file
    res.send(excelBuffer);
  } catch (error) {
    next(error);
  }
};

/**
 * Get all service types
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getServiceTypes = async (req, res, next) => {
  try {
    // Get pagination and sorting parameters
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'name', 'asc');

    // Get filter parameters
    const filters = {};
    if (req.query.search) filters.search = req.query.search;

    // Get all service types
    const result = await projectService.getServiceTypes(
      filters,
      pagination,
      sorting,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Service types retrieved successfully',
      data: result.serviceTypes,
      pagination: createPagination(pagination.page, pagination.limit, result.total)
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get service type by ID
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getServiceTypeById = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Get service type by ID
    const serviceType = await projectService.getServiceTypeById(id, req.tenantContext);

    return successResponse(res, {
      message: 'Service type retrieved successfully',
      data: serviceType
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Create service type
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const createServiceType = async (req, res, next) => {
  try {
    // Create service type
    const serviceType = await projectService.createServiceType(req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Service type created successfully',
      data: serviceType
    }, 201);
  } catch (error) {
    next(error);
  }
};

/**
 * Update service type
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const updateServiceType = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Update service type
    const serviceType = await projectService.updateServiceType(id, req.body, req.tenantContext);

    return successResponse(res, {
      message: 'Service type updated successfully'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Delete service type
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const deleteServiceType = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Delete service type
    const result = await projectService.deleteServiceType(id, req.tenantContext);

    return successResponse(res, {
      message: result ? 'Service type deleted successfully' : 'Failed to delete service type'
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Get service types by project
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const getServiceTypesByProject = async (req, res, next) => {
  try {
    const { projectId } = req.params;
    
    // Get pagination and sorting parameters
    const pagination = getPaginationParams(req.query);
    const sorting = getSortingParams(req.query, 'name', 'asc');

    // Get service types by project
    const result = await projectService.getServiceTypesByProject(
      projectId,
      pagination,
      sorting,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Service types retrieved successfully',
      data: result.serviceTypes,
      pagination: createPagination(pagination.page, pagination.limit, result.total)
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Associate service types with a project
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const associateServiceTypes = async (req, res, next) => {
  try {
    const { projectId } = req.params;
    const { serviceTypeIds } = req.body;
    
    await projectService.associateServiceTypes(
      parseInt(projectId, 10),
      serviceTypeIds,
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Service types associated with project successfully',
      data: { success: true }
    });
  } catch (error) {
    next(error);
  }
};

/**
 * Remove service type association from a project
 * @param {Object} req - Express request
 * @param {Object} res - Express response
 * @param {Function} next - Express next function
 */
const removeServiceTypeAssociation = async (req, res, next) => {
  try {
    const { projectId, serviceTypeId } = req.params;
    
    await projectService.removeServiceTypeAssociation(
      parseInt(projectId, 10),
      parseInt(serviceTypeId, 10),
      req.tenantContext
    );

    return successResponse(res, {
      message: 'Service type removed from project successfully',
      data: { success: true }
    });
  } catch (error) {
    next(error);
  }
};

module.exports = {
  // Project controllers
  getProjects,
  getProjectById,
  createProject,
  updateProject,
  deleteProject,
  getProjectStatistics,
  getProjectTimeline,
  getProjectBudget,
  updateProjectBudget,
  getProjectGantt,
  getProjectProgress,
  getProjectMetrics,
  getProjectExpenses,
  createProjectExpense,
  approveProjectExpense,
  rejectProjectExpense,
  downloadProjects,
  getProjBillingCosting,
  getProjBillingCostingV2,
  getProjBillingCostingV3,

  // Client controllers
  getClients,
  getClientById,
  createClient,
  updateClient,
  deleteClient,

  // Task controllers
  getTasks,
  getTaskById,
  createTask,
  updateTask,
  deleteTask,
  getTasksByProject,

  // Milestone controllers
  getMilestones,
  createMilestone,
  updateMilestone,
  deleteMilestone,
  getMilestoneDetails,
  getCurrentMilestoneDetails,

  // Project Resource controllers
  addProjectResources,
  editProjectResource,
  removeProjectResource,
  getProjectResourcesByProject,

  // Module controllers
  createModule,

  // Service type controllers
  getServiceTypes,
  getServiceTypeById,
  createServiceType,
  updateServiceType,
  deleteServiceType,
  getServiceTypesByProject,
  associateServiceTypes,
  removeServiceTypeAssociation
};
