'use strict';

/**
 * Rota Schedule Service - PRD Implementation
 * 
 * Handles schedule business logic according to PRD specifications:
 * - Schedule lifecycle management (draft → published → archived)
 * - Status-based permissions and workflow controls
 * - Instance generation and management
 * - Schedule validation and conflict detection
 * - Integration with auto-schedule service
 */

const {
  RotaSchedule,
  RotaShiftInstance,
  RotaShiftInstanceDesignationRequirement,
  ShiftAssignment,
  RotaShift,
  ShiftTemplate,
  ShiftTemplateDayConfig,
  ShiftTemplateDayShift,
  ShiftTemplateDayShiftDesignation,
  RotaShiftDesignationRequirement,
  Department,
  BusinessUnit,
  Employee,
  User,
  Company,
  Designation,
  Holiday
} = require('../../data/models');
const { NotFoundError, ValidationError, ConflictError } = require('../../common/errors');
const { Op } = require('sequelize');
const sequelize = require('../../data/models').sequelize;
const moment = require('moment');

class RotaScheduleService {

  /**
   * Get all schedules with advanced filtering
   * @param {Object} filters - Filter options
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Schedules with pagination
   */
  async getAllSchedules(filters, tenantContext) {
    const {
      page = 1,
      limit = 10,
      status,
      departmentId,
      startDate,
      endDate,
      search,
      sortBy = 'createdAt',
      sortOrder = 'DESC',
      // Enhanced: Hybrid filtering options
      type, // Filter by schedule type (manual, template_based, hybrid, etc.)
      sourceType, // Filter by source type (template, rotaShift)
      sourceId, // Filter by specific source ID
      includeSourceStats = true, // Include source statistics in response
      includeHybridMetadata = true, // Include hybrid metadata
      // Include options for instances, assignments, and statistics
      includeInstances = false,
      includeAssignments = false,
      includeStatistics = false
    } = filters;

    const offset = (page - 1) * limit;
    const whereClause = {
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId
    };

    // Enhanced: Apply filters including hybrid options
    if (status) whereClause.status = status;
    if (startDate) whereClause.startDate = { [Op.gte]: startDate };
    if (endDate) whereClause.endDate = { [Op.lte]: endDate };

    // Enhanced: Filter by schedule type (including hybrid)
    if (type) whereClause.type = type;

    if (search) {
      whereClause[Op.or] = [
        { name: { [Op.iLike]: `%${search}%` } },
        { description: { [Op.iLike]: `%${search}%` } }
      ];
    }

    const includeClause = [
      {
        model: BusinessUnit,
        as: 'businessUnit',
        attributes: ['id', 'name']
      }
    ];

    // Enhanced: Add hybrid filtering and metadata includes
    const shouldIncludeInstances = departmentId || sourceType || sourceId || includeSourceStats || includeHybridMetadata || includeInstances || includeAssignments;

    if (shouldIncludeInstances) {
      const instanceInclude = {
        model: RotaShiftInstance,
        as: 'shiftInstances',
        // Enhanced: Include hybrid-related fields for metadata (removed JSON fields)
        attributes: includeSourceStats || includeHybridMetadata || includeInstances ? [
          'id', 'date', 'status',
          'sourceType', 'sourceId', // Hybrid source tracking
          'totalRequired', 'totalAssigned' // Performance counters
        ] : [],
        include: [
          {
            model: RotaShift,
            as: 'rotaShift',
            where: departmentId ? { departmentId } : undefined,
            attributes: includeSourceStats || includeHybridMetadata || includeInstances ? ['id', 'name', 'departmentId'] : [],
            required: false // Left join since rotaShiftId can be null for template instances
          },
          {
            model: RotaShiftInstanceDesignationRequirement,
            as: 'designationRequirementsTable',
            attributes: ['designationId', 'requiredCount', 'assignedCount', 'priority', 'requirementType', 'sourceType'],
            required: false,
            include: [
              {
                model: Designation,
                as: 'designation',
                attributes: ['id', 'name', 'code']
              }
            ]
          }
        ],
        required: departmentId ? true : false // INNER JOIN only if filtering by department
      };

      // Add assignments if requested
      if (includeAssignments) {
        instanceInclude.include.push({
          model: ShiftAssignment,
          as: 'assignments',
          attributes: [
            'id', 'employeeId', 'designationId', 'assignedAt', 'status', 'assignmentType',
            'actualStartTime', 'actualEndTime', 'notes'
          ],
          include: [
            {
              model: Employee,
              as: 'employee',
              attributes: ['id', 'firstName', 'lastName', 'contactEmail'],
              required: false
            },
            {
              model: Designation,
              as: 'designation',
              attributes: ['id', 'name', 'code'],
              required: false
            }
          ],
          required: false
        });
      }

      // Enhanced: Add source filtering
      if (sourceType || sourceId) {
        const instanceWhere = {};
        if (sourceType) instanceWhere.sourceType = sourceType;
        if (sourceId) instanceWhere.sourceId = sourceId;
        instanceInclude.where = instanceWhere;
        instanceInclude.required = true; // INNER JOIN when filtering by source
      }

      includeClause.push(instanceInclude);
    }

    const { count, rows: schedules } = await RotaSchedule.findAndCountAll({
      where: whereClause,
      include: includeClause,
      order: [[sortBy, sortOrder]],
      limit,
      offset,
      distinct: true
    });

    // Enhanced: Add hybrid metadata to each schedule if requested
    const schedulesWithMetadata = await Promise.all(schedules.map(async schedule => {
      // Format shift instances with backward compatible designationRequirements
      if (schedule.shiftInstances) {
        schedule.shiftInstances = await Promise.all(schedule.shiftInstances.map(async instance => {
          // Convert junction table data to JSON format for backward compatibility
          if (instance.designationRequirementsTable) {
            const resolvedRequirements = {};
            const typePriority = { 'override': 3, 'custom': 2, 'base': 1 };

            // Resolve conflicts: Override > Custom > Base
            instance.designationRequirementsTable.forEach(req => {
              const key = req.designationId;
              const currentPriority = typePriority[req.requirementType] || 1;

              if (!resolvedRequirements[key] ||
                  currentPriority > (typePriority[resolvedRequirements[key].requirementType] || 1)) {
                resolvedRequirements[key] = req;
              }
            });

            // Format as JSON array (backward compatible) with designation details
            instance.dataValues.designationRequirements = Object.values(resolvedRequirements).map(req => ({
              designationId: req.designationId,
              requiredCount: req.requiredCount,
              assignedCount: req.assignedCount,
              priority: req.priority,
              designation: req.designation ? {
                id: req.designation.id,
                name: req.designation.name,
                code: req.designation.code
              } : null
            }));

            // Remove junction table data from response
            delete instance.dataValues.designationRequirementsTable;
          }
          return instance;
        }));
      }

      // Enhanced: Add hybrid metadata if requested
      if (includeHybridMetadata && schedule.shiftInstances) {
        schedule.dataValues.hybridMetadata = {
          isHybridSchedule: schedule.type === 'hybrid' || this.hasMultipleSourceTypes(schedule.shiftInstances),
          sourceTypes: this.getUniqueSourceTypes(schedule.shiftInstances),
          totalSources: this.countUniqueSources(schedule.shiftInstances),
          hasCustomModifications: this.hasCustomModifications(schedule.shiftInstances)
        };
      }

      // Enhanced: Add source statistics if requested
      if (includeSourceStats && schedule.shiftInstances) {
        schedule.dataValues.sourceStats = this.analyzeScheduleSources(schedule.shiftInstances);
      }

      // Add statistics if requested
      if (includeStatistics && schedule.shiftInstances) {
        schedule.dataValues.statistics = await this.calculateScheduleStats(schedule.id);
      }

      return schedule;
    }));

    // Enhanced: Calculate hybrid statistics for pagination metadata
    const hybridStats = includeSourceStats ? {
      totalHybridSchedules: schedulesWithMetadata.filter(s => s.dataValues.hybridMetadata?.isHybridSchedule).length,
      sourceTypeBreakdown: this.calculateSourceTypeBreakdown(schedulesWithMetadata),
      averageFillRate: this.calculateAverageFillRate(schedulesWithMetadata)
    } : {};

    return {
      schedules: schedulesWithMetadata,
      pagination: {
        page,
        limit,
        total: count,
        totalPages: Math.ceil(count / limit),
        hasNext: page < Math.ceil(count / limit),
        hasPrev: page > 1,
        // Enhanced: Include hybrid statistics in pagination
        // ...hybridStats
      }
    };
  }

  /**
   * Get schedule by ID with optional detailed information
   * @param {number} id - Schedule ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Additional options
   * @returns {Object} Schedule details
   */
  async getScheduleById(id, tenantContext, options = {}) {
    const includeClause = [
      {
        model: BusinessUnit,
        as: 'businessUnit',
        attributes: ['id', 'name', 'timezone']
      }
    ];

    // Enhanced: Include instances with hybrid source information
    if (options.includeInstances) {
      includeClause.push({
        model: RotaShiftInstance,
        as: 'shiftInstances',
        // Enhanced: Include all hybrid-related fields (removed JSON fields)
        attributes: [
          'id', 'date', 'status', 'notes',
          'sourceType', 'sourceId', // Hybrid source tracking
          'totalRequired', 'totalAssigned', // Performance counters
          'actualRequiredCount', 'assignmentMetadata', 'conflictFlags' // Legacy + metadata
        ],
        include: [
          {
            model: RotaShift,
            as: 'rotaShift',
            attributes: ['id', 'name', 'startTime', 'endTime', 'departmentId'],
            required: false, // Left join since rotaShiftId can be null for template instances
            include: [
              {
                model: Department,
                as: 'department',
                attributes: ['id', 'name']
              }
            ]
          },
          {
            model: RotaShiftInstanceDesignationRequirement,
            as: 'designationRequirementsTable',
            attributes: ['designationId', 'requiredCount', 'assignedCount', 'priority', 'requirementType', 'sourceType'],
            required: false,
            include: [
              {
                model: Designation,
                as: 'designation',
                attributes: ['id', 'name', 'code']
              }
            ]
          }
        ]
      });
    }

    // Include assignments if requested
    if (options.includeAssignments && options.includeInstances) {
      // Add ShiftAssignment to RotaShiftInstance (not RotaShift)
      includeClause[includeClause.length - 1].include.push({
        model: ShiftAssignment,
        as: 'assignments',
        include: [
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'firstName', 'lastName', 'contactEmail']
          },
          {
            model: Designation,
            as: 'designation',
            attributes: ['id', 'name', 'code'],
            required: false
          }
        ]
      });
    }

    const schedule = await RotaSchedule.findOne({
      where: {
        id,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      },
      include: includeClause
    });

    if (!schedule) {
      throw new NotFoundError('Schedule not found');
    }

    // Format shift instances with backward compatible designationRequirements
    if (schedule.shiftInstances) {
      schedule.shiftInstances = await Promise.all(schedule.shiftInstances.map(async instance => {
        // Convert junction table data to JSON format for backward compatibility
        if (instance.designationRequirementsTable) {
          const resolvedRequirements = {};
          const typePriority = { 'override': 3, 'custom': 2, 'base': 1 };

          // Resolve conflicts: Override > Custom > Base
          instance.designationRequirementsTable.forEach(req => {
            const key = req.designationId;
            const currentPriority = typePriority[req.requirementType] || 1;

            if (!resolvedRequirements[key] ||
                currentPriority > (typePriority[resolvedRequirements[key].requirementType] || 1)) {
              resolvedRequirements[key] = req;
            }
          });

          // Format as JSON array (backward compatible) with designation details
          instance.dataValues.designationRequirements = Object.values(resolvedRequirements).map(req => ({
            designationId: req.designationId,
            requiredCount: req.requiredCount,
            assignedCount: req.assignedCount,
            priority: req.priority,
            designation: req.designation ? {
              id: req.designation.id,
              name: req.designation.name,
              code: req.designation.code
            } : null
          }));

          // Remove junction table data from response
          delete instance.dataValues.designationRequirementsTable;
        }

        // ✅ GROUP ASSIGNMENTS BY DESIGNATION
        if (instance.assignments && instance.dataValues.designationRequirements) {
          // Create a map of assignments by designationId
          const assignmentsByDesignation = {};

          instance.assignments.forEach(assignment => {
            const assignmentData = assignment.toJSON ? assignment.toJSON() : assignment;
            const designationId = assignmentData.designationId;

            if (designationId) {
              if (!assignmentsByDesignation[designationId]) {
                assignmentsByDesignation[designationId] = [];
              }
              assignmentsByDesignation[designationId].push(assignmentData);
            }
          });

          // Add assignments to each designation requirement
          instance.dataValues.designationRequirements = instance.dataValues.designationRequirements.map(req => {
            req.assignments = assignmentsByDesignation[req.designationId] || [];
            req.actualAssignedCount = req.assignments.length;
            return req;
          });

          // Remove the flat assignments array since we now have designation-wise assignments
          delete instance.dataValues.assignments;
        }

        return instance;
      }));
    }

    // Enhanced: Add hybrid-specific metadata
    if (options.includeInstances && schedule.shiftInstances) {
      // Add source analysis
      schedule.dataValues.sourceAnalysis = this.analyzeScheduleSources(schedule.shiftInstances);

      // Add hybrid metadata
      schedule.dataValues.hybridMetadata = {
        isHybridSchedule: schedule.type === 'hybrid' || this.hasMultipleSourceTypes(schedule.shiftInstances),
        sourceTypes: this.getUniqueSourceTypes(schedule.shiftInstances),
        totalSources: this.countUniqueSources(schedule.shiftInstances),
        lastModified: schedule.updatedAt,
        hasCustomModifications: this.hasCustomModifications(schedule.shiftInstances)
      };
    }

    // Add statistics if requested (enhanced with hybrid metrics)
    if (options.includeStatistics) {
      schedule.dataValues.statistics = await this.getScheduleStatistics(id, tenantContext);
    }


    // delete schedule?.templateId //no need for this time due to multiple templates used by schedule
    return schedule;
  }

  /**
   * Create new schedule with hybrid approach (Enhanced for Template + RotaShift support)
   * @param {Object} scheduleData - Schedule data with shift sources
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Created schedule
   */
  async createSchedule(scheduleData, tenantContext) {
    const transaction = await sequelize.transaction();
    let scheduleId = null;

    try {
      // Validate schedule data
      await this.validateScheduleData(scheduleData, tenantContext);

      const { shiftSources = [], ...basicScheduleData } = scheduleData;

      // Create the main schedule
      const schedule = await RotaSchedule.create({
        ...basicScheduleData,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
      }, { transaction });

      scheduleId = schedule.id;

      // Process shift sources if provided (Templates + Individual Shifts)
      if (shiftSources.length > 0) {
        await this.processShiftSources(schedule.id, shiftSources, tenantContext, transaction);
      }

      // Process employee assignments if provided
      console.log('🔍 Checking employee assignments:', {
        hasAssignments: !!scheduleData.employeeAssignments,
        assignmentCount: scheduleData.employeeAssignments?.length || 0,
        assignments: scheduleData.employeeAssignments
      });

      if (scheduleData.employeeAssignments && scheduleData.employeeAssignments.length > 0) {
        console.log('📋 Processing employee assignments during schedule creation...');

        // Enhanced: Check if there are template-based assignments
        const templateAssignments = scheduleData.employeeAssignments.filter(a => a.sourceType === 'template');
        const directAssignments = scheduleData.employeeAssignments.filter(a => a.sourceType !== 'template');

        console.log(`📊 Assignment breakdown: ${templateAssignments.length} template, ${directAssignments.length} direct`);

        let assignmentResults = { successful: 0, failed: 0, assignments: [], errors: [] };

        // Process template assignments
        if (templateAssignments.length > 0) {
          console.log('🎯 Processing template-based assignments...');
          const templateResults = await this.processTemplateAssignments(schedule.id, templateAssignments, tenantContext, transaction);
          assignmentResults.successful += templateResults.successful;
          assignmentResults.failed += templateResults.failed;
          assignmentResults.assignments.push(...(templateResults.assignments || []));
          assignmentResults.errors.push(...(templateResults.errors || []));
        }

        // Process direct assignments (existing logic)
        if (directAssignments.length > 0) {
          console.log('📋 Processing direct assignments...');
          const shiftAssignmentService = require('./shiftAssignmentService');
          const directResults = await shiftAssignmentService._assignEmployeesToScheduleWithTransaction(
            schedule.id,
            directAssignments,
            tenantContext,
            transaction
          );
          assignmentResults.successful += directResults.successful || 0;
          assignmentResults.failed += directResults.failed || 0;
          assignmentResults.assignments.push(...(directResults.assignments || []));
          assignmentResults.errors.push(...(directResults.errors || []));
        }

        console.log('✅ Employee assignments processed:', assignmentResults);
      }

      await transaction.commit();

      // Return schedule with associations (outside transaction)
      try {
        return await this.getScheduleById(scheduleId, tenantContext, {
          includeInstances: true,
          includeStatistics: true
        });
      } catch (getError) {
        // If getting the schedule fails, return basic schedule data
        console.error('Error fetching created schedule:', getError);
        return {
          id: scheduleId,
          ...basicScheduleData,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId,
          status: 'draft',
          message: 'Schedule created successfully but could not fetch full details'
        };
      }

    } catch (error) {
      // Only rollback if transaction is still active
      if (!transaction.finished) {
        await transaction.rollback();
      }
      throw error;
    }
  }



  /**
   * Process shift sources (Templates + Individual RotaShifts) for hybrid approach
   * @param {number} scheduleId - Schedule ID
   * @param {Array} shiftSources - Array of shift sources
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async processShiftSources(scheduleId, shiftSources, tenantContext, transaction) {
    const shiftInstances = [];

    for (const source of shiftSources) {
      if (source.type === 'template') {
        const templateInstances = await this._generateFromTemplate(
          scheduleId,
          source,
          tenantContext,
          transaction
        );
        shiftInstances.push(...templateInstances);

      } else if (source.type === 'rotaShift') {
        const rotaShiftInstances = await this._generateFromRotaShift(
          scheduleId,
          source,
          tenantContext,
          transaction
        );
        shiftInstances.push(...rotaShiftInstances);
      }
    }

    // Bulk create shift instances for performance
    if (shiftInstances.length > 0) {
      // Create instances without JSON fields
      const instancesData = shiftInstances.map(instance => {
        const { designationRequirements, customRequirements, ...instanceData } = instance;
        return instanceData;
      });

      const createdInstances = await RotaShiftInstance.bulkCreate(instancesData, {
        transaction,
        returning: true,
        updateOnDuplicate: [
          'totalRequired',
          'status',
          'sourceType',
          'sourceId'
        ]
      });

      // Create designation requirements in junction table
      for (let i = 0; i < shiftInstances.length; i++) {
        const originalInstance = shiftInstances[i];
        const createdInstance = createdInstances[i];

        // Create base requirements
        if (originalInstance.designationRequirements) {
          await this.createInstanceDesignationRequirements(
            createdInstance.id,
            originalInstance.designationRequirements,
            'base',
            originalInstance.sourceType || 'template',
            originalInstance.createdById,
            transaction
          );
        }

        // Custom requirements are already merged in designationRequirements
        // No need to create separate custom requirements
      }
    }

    return shiftInstances.length;
  }

  /**
   * Generate shift instances from template (Enhanced for scale)
   * @param {number} scheduleId - Schedule ID
   * @param {Object} templateSource - Template source configuration
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Array} Array of shift instances to create
   */
  async _generateFromTemplate(scheduleId, templateSource, tenantContext, transaction) {
    const { templateId, dates = [], customRequirements = [], autoCalculateDates = true } = templateSource;

    // Get ShiftTemplate with normalized day configurations
    const template = await ShiftTemplate.findByPk(templateId, {
      include: [
        {
          model: ShiftTemplateDayConfig,
          as: 'dayConfigs',
          where: { isActive: true },
          required: false,
          include: [
            {
              model: ShiftTemplateDayShift,
              as: 'dayShifts',
              where: { isActive: true },
              required: false,
              include: [
                {
                  model: RotaShift,
                  as: 'rotaShift'
                },
                {
                  model: ShiftTemplateDayShiftDesignation,
                  as: 'designationRequirements',
                  where: { isActive: true },
                  required: false,
                  include: [
                    {
                      model: Designation,
                      as: 'designation'
                    }
                  ]
                }
              ]
            }
          ]
        }
      ],
      transaction
    });

    if (!template) {
      throw new ValidationError(`ShiftTemplate ${templateId} not found`);
    }

    console.log(`🔍 Template ${templateId} found:`, {
      id: template.id,
      name: template.name,
      dayConfigsCount: template.dayConfigs?.length || 0
    });

   

    // Get schedule details for auto-calculating dates
    const schedule = await RotaSchedule.findByPk(scheduleId, { transaction });
    if (!schedule) {
      throw new ValidationError(`Schedule ${scheduleId} not found`);
    }

    let finalDates = dates;

    // ENHANCED: Auto-calculate dates if not provided or autoCalculateDates is true
    if (autoCalculateDates && (!dates || dates.length === 0)) {
      console.log(`🎯 Auto-calculating dates for template ${templateId} from schedule date range`);
      finalDates = await this._calculateDatesFromTemplate(template, schedule.startDate, schedule.endDate, tenantContext, transaction);
      console.log(`✅ Auto-calculated ${finalDates.length} dates for template: ${finalDates.join(', ')}`);
    }

    const instances = [];
    const invalidDates = [];
    const nonWorkingDates = []; 

    // Validate template has day configurations
    if (!template.dayConfigs || template.dayConfigs.length === 0) {
      throw new ValidationError(`ShiftTemplate ${templateId} has no day configurations defined`);
    }

    console.log(`📋 Template has ${template.dayConfigs.length} day configurations`);

    // ✅ ENHANCED: Convert normalized structure to lookup map with consistent shift ordering
    const dayConfigsMap = {};
    template.dayConfigs.forEach(dayConfig => {
      if (dayConfig.isWorkingDay && dayConfig.dayShifts && dayConfig.dayShifts.length > 0) {
        // ✅ CRITICAL: Sort dayShifts by start time before mapping
        const sortedDayShifts = [...dayConfig.dayShifts].sort((a, b) => {
          const getMinutes = (timeStr) => {
            if (!timeStr) return 0;
            const [hours, minutes] = timeStr.split(':').map(Number);
            return hours * 60 + minutes;
          };

          const timeA = a.rotaShift?.startTime || '00:00';
          const timeB = b.rotaShift?.startTime || '00:00';
          return getMinutes(timeA) - getMinutes(timeB);
        });

        dayConfigsMap[dayConfig.dayOfWeek] = {
          isWorkingDay: dayConfig.isWorkingDay,
          dayType: dayConfig.dayType,
          priority: dayConfig.priority,
          shifts: sortedDayShifts.map(dayShift => ({
            rotaShiftId: dayShift.rotaShiftId,
            priority: dayShift.priority,
            isFlexible: dayShift.isFlexible,
            useDesignationOverride: dayShift.useDesignationOverride,
            designationOverrides: dayShift.designationRequirements?.map(req => ({
              designationId: req.designationId,
              requiredCount: req.requiredCount
            })) || [],
            rotaShift: dayShift.rotaShift
          }))
        };

        console.log(`📋 Day config for ${dayConfig.dayOfWeek}: ${sortedDayShifts.length} shifts sorted by start time: ${sortedDayShifts.map(s => `${s.rotaShift?.name}(${s.rotaShift?.startTime})`).join(' → ')}`);
      }
    });

    console.log(`📊 Processed day configs for: ${Object.keys(dayConfigsMap).join(', ')}`);

    if (Object.keys(dayConfigsMap).length === 0) {
      throw new ValidationError(`ShiftTemplate ${templateId} has no working day configurations with shifts`);
    }

    for (const date of finalDates) {
      const dayOfWeek = new Date(date).toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
      const dayConfig = dayConfigsMap[dayOfWeek];

      console.log(`📅 Processing manual schedule date: ${date} (${dayOfWeek})`);

      // Step 1: Check if it's weekend (Saturday/Sunday)
      if (dayOfWeek === 'saturday' || dayOfWeek === 'sunday') {
        console.log(`⏭️ Skipping ${date} (${dayOfWeek}) - Weekend`);
        nonWorkingDates.push(`${date} (${dayOfWeek} - Weekend)`);
        continue;
      }

      // Step 2: Check if date is holiday
      const isHoliday = await this.checkIfHoliday(date, tenantContext, transaction);
      if (isHoliday.isHoliday) {
        console.log(`🏖️ Skipping ${date} (${dayOfWeek}) - Holiday: ${isHoliday.holidayName}`);
        nonWorkingDates.push(`${date} (${dayOfWeek} - Holiday: ${isHoliday.holidayName})`);
        continue;
      }

      // Step 3: Check if day configuration exists
      if (!dayConfig) {
        invalidDates.push(`${date} (${dayOfWeek} - No template configuration)`);
        continue;
      }

      // Step 4: Check if it's a working day (skip non-working days)
      if (dayConfig.isWorkingDay === false) {
        console.log(`⏭️ Skipping ${date} (${dayOfWeek}) - Non-working day in template`);
        nonWorkingDates.push(`${date} (${dayOfWeek} - Non-working day)`);
        continue;
      }

      // Step 5: Check if day has shifts configured
      if (!dayConfig.shifts || dayConfig.shifts.length === 0) {
        invalidDates.push(`${date} (${dayOfWeek} - No shifts configured)`);
        continue;
      }

      // ✅ CRITICAL FIX: Sort shifts by start time to ensure consistent sequence (Morning → Afternoon → Night)
      const sortedShifts = [...(dayConfig.shifts || [])].sort((a, b) => {
        // Convert time to minutes for proper sorting
        const getMinutes = (timeStr) => {
          if (!timeStr) return 0;
          const [hours, minutes] = timeStr.split(':').map(Number);
          return hours * 60 + minutes;
        };

        const timeA = a.rotaShift?.startTime || '00:00';
        const timeB = b.rotaShift?.startTime || '00:00';
        return getMinutes(timeA) - getMinutes(timeB);
      });

      console.log(`📊 Processing ${sortedShifts.length} shifts for ${date} in chronological order: ${sortedShifts.map(s => `${s.rotaShift?.name}(${s.rotaShift?.startTime})`).join(' → ')}`);

      // Process each shift in CONSISTENT chronological order (Morning → Afternoon → Night)
      for (const shiftConfig of sortedShifts) {
        // Get the RotaShift referenced in the template
        const rotaShift = await RotaShift.findByPk(shiftConfig.rotaShiftId, {
          include: [{
            model: RotaShiftDesignationRequirement,
            as: 'designationRequirements'
          }],
          transaction
        });
        if (!rotaShift) {
          invalidDates.push(`${date} (${dayOfWeek} - RotaShift ${shiftConfig.rotaShiftId} not found)`);
          continue;
        }

        // Build designation requirements (template + custom overrides)
        const designationReqs = await this._buildDesignationRequirements(
          shiftConfig,
          customRequirements, // Pass array format
          rotaShift,
          transaction
        );

        instances.push({
          scheduleId,
          rotaShiftId: shiftConfig.rotaShiftId, // Template-based instances don't have rotaShiftId
          date,
          sourceType: 'template',
          sourceId: templateId,
          designationRequirements: designationReqs, // Already includes custom requirements merged
          // customRequirements: customRequirements, // Don't store separately - already merged in designationReqs
          totalRequired: designationReqs.reduce((sum, req) => sum + req.requiredCount, 0),
          totalAssigned: 0,
          status: 'open',
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId,
          createdById: tenantContext.userId
        });
      }
    }

    // Throw errors if any dates are invalid
    if (invalidDates.length > 0) {
      throw new ValidationError(`Template ${templateId} - Invalid dates: ${invalidDates.join(', ')}. These dates have no valid configuration or shifts.`);
    }

    if (nonWorkingDates.length > 0) {
      throw new ValidationError(`Template ${templateId} - Non-working dates: ${nonWorkingDates.join(', ')}. These dates are configured as non-working days.`);
    }

    // If no instances were created, throw error
    if (instances.length === 0) {
      throw new ValidationError(`Template ${templateId} - No valid shift instances could be generated for the specified dates: ${dates.join(', ')}`);
    }

    return instances;
  }

  /**
   * Generate shift instances from individual RotaShift (Enhanced for scale)
   * @param {number} scheduleId - Schedule ID
   * @param {Object} shiftSource - RotaShift source configuration
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Array} Array of shift instances to create
   */
  async _generateFromRotaShift(scheduleId, shiftSource, tenantContext, transaction) {
    const { rotaShiftId, dates = [], customRequirements = [] } = shiftSource;

    const rotaShift = await RotaShift.findByPk(rotaShiftId, {
      include: [{
        model: RotaShiftDesignationRequirement,
        as: 'designationRequirements'
      }],
      transaction
    });

    if (!rotaShift) {
      throw new ValidationError(`RotaShift ${rotaShiftId} not found`);
    }

    // Validate RotaShift has designation requirements
    if (!rotaShift.designationRequirements || rotaShift.designationRequirements.length === 0) {
      throw new ValidationError(`RotaShift ${rotaShiftId} has no designation requirements configured`);
    }

    // Validate dates array is not empty
    if (!dates || dates.length === 0) {
      throw new ValidationError(`RotaShift ${rotaShiftId} - No dates specified for schedule generation`);
    }

    const instances = [];

    for (const date of dates) {
      // Start with base requirements from RotaShift
      const baseRequirements = {};
      rotaShift.designationRequirements.forEach(req => {
        baseRequirements[req.designationId] = req.requiredCount;
      });

      // Override with custom requirements (array format)
      customRequirements.forEach(customReq => {
        baseRequirements[customReq.designationId] = customReq.requiredCount;
      });

      // Convert to array format for storage
      const designationReqs = Object.entries(baseRequirements).map(([designationId, requiredCount]) => ({
        designationId: parseInt(designationId),
        requiredCount,
        assignedCount: 0,
        priority: 0
      }));

      // Validate that we have at least one requirement
      if (designationReqs.length === 0) {
        throw new ValidationError(`RotaShift ${rotaShiftId} - No valid designation requirements found for date ${date}`);
      }

      instances.push({
        scheduleId,
        rotaShiftId: rotaShift.id, // RotaShift-based instances have rotaShiftId
        date,
        sourceType: 'rotaShift',
        sourceId: rotaShiftId,
        designationRequirements: designationReqs, // Already includes custom requirements merged
        // customRequirements: customRequirements, // Don't store separately - already merged in designationReqs
        totalRequired: designationReqs.reduce((sum, req) => sum + req.requiredCount, 0),
        totalAssigned: 0,
        status: 'open',
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        createdById: tenantContext.userId
      });
    }

    return instances;
  }

  /**
   * Build designation requirements with template/custom overrides
   * @param {Object} shiftConfig - Shift configuration from template
   * @param {Object} customReqs - Custom requirement overrides
   * @param {Object} rotaShift - RotaShift object
   * @param {Object} transaction - Database transaction
   * @returns {Array} Array of designation requirements
   */
  async _buildDesignationRequirements(shiftConfig, customReqs, rotaShift, transaction) {
    let requirements = [];

    // Convert customReqs array to object for easy lookup
    const customReqsMap = {};
    customReqs.forEach(req => {
      customReqsMap[req.designationId] = req.requiredCount;
    });

    // Use template overrides if available
    if (shiftConfig.useDesignationOverride && shiftConfig.designationOverrides) {
      requirements = shiftConfig.designationOverrides.map(override => ({
        designationId: override.designationId,
        requiredCount: customReqsMap[override.designationId] || override.requiredCount,
        assignedCount: 0,
        priority: override.priority || 0
      }));
    } else {
      // Use RotaShift default requirements
      const shiftReqs = await RotaShiftDesignationRequirement.findAll({
        where: { rotaShiftId: rotaShift.id },
        transaction
      });

      requirements = shiftReqs.map(req => ({
        designationId: req.designationId,
        requiredCount: customReqsMap[req.designationId] || req.requiredCount,
        assignedCount: 0,
        priority: req.priority || 0
      }));
    }

    return requirements;
  }

  /**
   * Update schedule
   * @param {number} id - Schedule ID
   * @param {Object} updateData - Update data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated schedule
   */
  async updateSchedule(id, updateData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const schedule = await RotaSchedule.findOne({
        where: {
          id,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (!schedule) {
        throw new NotFoundError('Schedule not found');
      }

      // 🔒 RESTRICTION: Prevent updates to published or cancelled schedules
      if (schedule.status === 'published') {
        throw new ValidationError('Cannot update schedule: Schedule is already published. Only archiving is allowed for published schedules.');
      }

      if (schedule.status === 'cancelled') {
        throw new ValidationError('Cannot update schedule: Schedule is cancelled. Cancelled schedules cannot be modified.');
      }

      // Validate update data
      await this.validateScheduleData(updateData, tenantContext, id);

      const { shiftSources, addShiftSources, removeShiftSources, employeeAssignments, ...basicUpdateData } = updateData;
      // console.log(basicUpdateData,'-------------')
      // Update basic schedule data
      await schedule.update({
        ...basicUpdateData,
        updatedById: tenantContext.userId
      }, { transaction });

      // Handle shift source modifications
      let shiftInstancesModified = 0;

      // 1. Remove shift sources if specified
      if (removeShiftSources && removeShiftSources.length > 0) {
        shiftInstancesModified += await this.removeShiftSources(id, removeShiftSources, tenantContext, transaction);
      }

      // 2. Add new shift sources if specified
      if (addShiftSources && addShiftSources.length > 0) {
        shiftInstancesModified += await this.processShiftSources(id, addShiftSources, tenantContext, transaction);
      }

      // 3. Replace all shift sources if specified (complete replacement)
      if (shiftSources && shiftSources.length > 0) {
        // Remove all existing instances
        // await RotaShiftInstance.destroy({
        //   where: { scheduleId: id },
        //   transaction
        // });                     abhi ke liye bas add kr rhe hai bad me remove wala funcatnality denge mere me implimented hai

        // Add new instances
        shiftInstancesModified += await this.processShiftSources(id, shiftSources, tenantContext, transaction);
      }

      // 4. Process employee assignments if provided (REPLACEMENT STRATEGY)
      if (employeeAssignments && employeeAssignments.length > 0) {
        console.log('📋 Processing employee assignments during schedule update (REPLACEMENT MODE)...');
        const shiftAssignmentService = require('./shiftAssignmentService');

        // STEP 1: Remove existing assignments for the dates being updated
        const updatedDates = [...new Set(employeeAssignments.map(a => a.date))];
        console.log('🗑️ Removing existing assignments for dates:', updatedDates);

        await shiftAssignmentService._removeAssignmentsForDates(
          id,
          updatedDates,
          tenantContext,
          transaction
        );

        // STEP 2: Add new assignments
        const assignmentResults = await shiftAssignmentService._assignEmployeesToScheduleWithTransaction(
          id,
          employeeAssignments,
          tenantContext,
          transaction
        );
        console.log('✅ Employee assignments replaced during update:', assignmentResults);
      }

      await transaction.commit();

      // Return updated schedule with basic information to avoid circular references
      const updatedSchedule = await RotaSchedule.findByPk(id, {
        include: [
          {
            model: BusinessUnit,
            as: 'businessUnit',
            attributes: ['id', 'name', 'timezone']
          }
        ]
      });

      // Get basic statistics without deep nesting
      const instanceCount = await RotaShiftInstance.count({
        where: { scheduleId: id }
      });

      const assignmentCount = await ShiftAssignment.count({
        include: [{
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: { scheduleId: id },
          attributes: []
        }]
      });

      return {
        ...updatedSchedule.toJSON(),
        metrics: {
          totalShiftInstances: instanceCount,
          totalAssignments: assignmentCount,
          fillRate: instanceCount > 0 ? `${Math.round((assignmentCount / instanceCount) * 100)}%` : '0%'
        },
        updateSummary: {
          shiftInstancesModified,
          lastModified: new Date(),
          modifiedBy: tenantContext.userId
        }
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Remove shift sources from schedule
   * @param {number} scheduleId - Schedule ID
   * @param {Array} removeShiftSources - Array of shift sources to remove
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {number} Number of instances removed
   */
  async removeShiftSources(scheduleId, removeShiftSources, tenantContext, transaction) {
    let removedCount = 0;

    for (const source of removeShiftSources) {
      const whereClause = {
        scheduleId,
        sourceType: source.type
      };

      if (source.type === 'template') {
        whereClause.sourceId = source.templateId;
        if (source.dates && source.dates.length > 0) {
          whereClause.date = { [Op.in]: source.dates };
        }
      } else if (source.type === 'rotaShift') {
        whereClause.sourceId = source.rotaShiftId;
        if (source.dates && source.dates.length > 0) {
          whereClause.date = { [Op.in]: source.dates };
        }
      }

      // Remove associated assignments first
      const instancesToRemove = await RotaShiftInstance.findAll({
        where: whereClause,
        attributes: ['id'],
        transaction
      });

      if (instancesToRemove.length > 0) {
        const instanceIds = instancesToRemove.map(instance => instance.id);

        // Remove assignments
        await ShiftAssignment.destroy({
          where: { shiftInstanceId: { [Op.in]: instanceIds } },
          transaction
        });

        // Remove instances
        const deletedCount = await RotaShiftInstance.destroy({
          where: whereClause,
          transaction
        });

        removedCount += deletedCount;
      }
    }

    return removedCount;
  }

  /**
   * Delete schedule
   * @param {number} id - Schedule ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} options - Delete options
   */
  async deleteSchedule(id, tenantContext, options = {}) {
    const transaction = await sequelize.transaction();

    try {
      // Get schedule with hybrid source information
      const schedule = await RotaSchedule.findOne({
        where: {
          id,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        include: [{
          model: RotaShiftInstance,
          as: 'shiftInstances',
          attributes: ['id', 'sourceType', 'sourceId', 'date'],
          required: false
        }],
        transaction
      });

      if (!schedule) {
        throw new NotFoundError('Schedule not found');
      }



      // Collect deletion statistics for audit
      const deletionStats = {
        totalInstances: schedule.shiftInstances?.length || 0,
        sourceBreakdown: {},
        assignmentCount: 0
      };

      // Count instances by source type
      if (schedule.shiftInstances) {
        for (const instance of schedule.shiftInstances) {
          const sourceKey = `${instance.sourceType}_${instance.sourceId}`;
          deletionStats.sourceBreakdown[sourceKey] = (deletionStats.sourceBreakdown[sourceKey] || 0) + 1;
        }
      }

      // Count total assignments
      if (schedule.shiftInstances?.length > 0) {
        const instanceIds = schedule.shiftInstances.map(i => i.id);
        deletionStats.assignmentCount = await ShiftAssignment.count({
          where: { shiftInstanceId: { [Op.in]: instanceIds } },
          transaction
        });
      }

      if (options.force) {
        // Hard delete - remove all related data with proper cascade
        console.log(`🗑️ Hard deleting schedule ${schedule.name} with ${deletionStats.totalInstances} instances`);

        // Delete assignments first (foreign key constraint)
        if (schedule.shiftInstances?.length > 0) {
          const instanceIds = schedule.shiftInstances.map(i => i.id);
          await ShiftAssignment.destroy({
            where: { shiftInstanceId: { [Op.in]: instanceIds } },
            transaction
          });
        }

        // Delete shift instances
        await RotaShiftInstance.destroy({
          where: { scheduleId: id },
          transaction
        });

        // Delete the schedule
        await schedule.destroy({ transaction });

        console.log(`✅ Hard deleted schedule with ${deletionStats.assignmentCount} assignments`);
      } else {
        // Soft delete - mark as archived with hybrid source tracking
        console.log(`📦 Archiving schedule ${schedule.name} with hybrid sources`);

        await schedule.update({
          status: 'archived',
          archivedAt: new Date(),
          archivedBy: tenantContext.userId,
          archiveMetadata: {
            deletionStats,
            archivedSources: deletionStats.sourceBreakdown,
            originalType: schedule.type
          }
        }, { transaction });

        // Update shift instances status to archived
        await RotaShiftInstance.update({
          status: 'cancelled'
        }, {
          where: { scheduleId: id },
          transaction
        });

        console.log(`✅ Archived schedule with ${deletionStats.totalInstances} instances`);
      }

      await transaction.commit();

      return {
        message: options.force ? 'Schedule permanently deleted' : 'Schedule archived',
        deletionStats
      };

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Publish schedule with comprehensive validation and notifications
   * @param {number} id - Schedule ID
   * @param {Object} publishData - Publish data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Published schedule with validation results
   */
  async publishSchedule(id, publishData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      // Phase 1: Get and validate schedule exists with hybrid source information
      const schedule = await RotaSchedule.findOne({
        where: {
          id,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        include: [{
          model: RotaShiftInstance,
          as: 'shiftInstances',
          attributes: ['id', 'sourceType', 'sourceId', 'date', 'status', 'totalRequired', 'totalAssigned'],
          required: false
        }],
        transaction
      });

      if (!schedule) {
        throw new NotFoundError('Schedule not found');
      }

      if (schedule.status === 'published') {
        throw new ValidationError('Schedule is already published');
      }

      if (schedule.status === 'archived') {
        throw new ValidationError('Cannot publish archived schedule');
      }

      // Enhanced: Validate hybrid schedule sources
      console.log(`📋 Publishing ${schedule.type} schedule with ${schedule.shiftInstances?.length || 0} instances`);

      // Analyze hybrid sources for validation
      const sourceAnalysis = this.analyzeScheduleSources(schedule.shiftInstances || []);
      console.log('📊 Source analysis:', sourceAnalysis);

      // Phase 2: Comprehensive validation
      console.log('🔍 Starting comprehensive schedule validation...');
      const validationResults = await this.validateScheduleForPublishing(schedule, tenantContext, transaction);

      // Phase 3: Lock assignments (if requested)
      if (publishData.lockAssignments !== false) {
        console.log('🔒 Locking shift assignments...');
        await this.lockScheduleAssignments(id, transaction);
      }

      // Phase 4: Update schedule status and metadata
      console.log('📝 Updating schedule status to published...');
      await schedule.update({
        status: 'published',
        publishedAt: publishData.publishDate || new Date(),
        publishedBy: tenantContext.userId,
        updatedById: tenantContext.userId
      }, { transaction });

      // Phase 5: Update schedule statistics
      console.log('📊 Updating schedule statistics...');
      await this.updateScheduleStatistics(id, transaction);

      // Phase 6: Create audit log
      await this.createPublishAuditLog(schedule, validationResults, tenantContext, transaction);

      await transaction.commit();

      // Phase 7: Send notifications (async - don't wait)
      if (publishData.notifyEmployees !== false) {
        console.log('📧 Triggering notification process...');
        setImmediate(() => {
          this.sendEnhancedPublishNotifications(schedule, tenantContext);
        });
      }

      // Phase 8: Background tasks (async)
      setImmediate(() => {
        this.triggerPostPublishTasks(schedule, tenantContext);
      });

      // Return published schedule with validation results
      const publishedSchedule = await this.getScheduleById(id, tenantContext);
      publishedSchedule.dataValues.publishValidation = validationResults;

      return publishedSchedule;

    } catch (error) {
      await transaction.rollback();
      console.error('❌ Schedule publish failed:', error.message);
      throw error;
    }
  }

  /**
   * Archive schedule
   * @param {number} id - Schedule ID
   * @param {Object} archiveData - Archive data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Archived schedule
   */
  async archiveSchedule(id, archiveData, tenantContext) {
    const schedule = await RotaSchedule.findOne({
      where: {
        id,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      }
    });

    if (!schedule) {
      throw new NotFoundError('Schedule not found');
    }

    if (schedule.status === 'archived') {
      throw new ValidationError('Schedule is already archived');
    }

    await schedule.update({
      status: 'archived',
      archivedAt: archiveData.archivedAt || new Date(),
      archivedBy: archiveData.archivedBy || tenantContext.userId,
      archiveReason: archiveData.reason,
      updatedById: tenantContext.userId
    });

    return await this.getScheduleById(id, tenantContext);
  }

  /**
   * Check if schedule has multiple source types (hybrid indicator)
   * @param {Array} instances - Array of shift instances
   * @returns {boolean} True if multiple source types exist
   */
  hasMultipleSourceTypes(instances) {
    const sourceTypes = new Set(instances.map(i => i.sourceType || 'rotaShift'));
    return sourceTypes.size > 1;
  }

  /**
   * Get unique source types in schedule
   * @param {Array} instances - Array of shift instances
   * @returns {Array} Array of unique source types
   */
  getUniqueSourceTypes(instances) {
    return [...new Set(instances.map(i => i.sourceType || 'rotaShift'))];
  }

  /**
   * Count unique sources (templates + RotaShifts)
   * @param {Array} instances - Array of shift instances
   * @returns {number} Number of unique sources
   */
  countUniqueSources(instances) {
    const sources = new Set(instances.map(i => `${i.sourceType || 'rotaShift'}_${i.sourceId || i.rotaShiftId}`));
    return sources.size;
  }

  /**
   * Check if schedule has custom modifications
   * @param {Array} instances - Array of shift instances
   * @returns {boolean} True if custom modifications exist
   */
  hasCustomModifications(instances) {
    return instances.some(i => i.customRequirements && Object.keys(i.customRequirements).length > 0);
  }

  /**
   * Calculate source type breakdown for multiple schedules
   * @param {Array} schedules - Array of schedules with metadata
   * @returns {Object} Source type breakdown
   */
  calculateSourceTypeBreakdown(schedules) {
    const breakdown = {
      hybrid: 0,
      template_based: 0,
      manual: 0,
      auto_generated: 0
    };

    schedules.forEach(schedule => {
      if (schedule.dataValues.hybridMetadata?.isHybridSchedule) {
        breakdown.hybrid++;
      } else {
        breakdown[schedule.type] = (breakdown[schedule.type] || 0) + 1;
      }
    });

    return breakdown;
  }

  /**
   * Calculate average fill rate across schedules
   * @param {Array} schedules - Array of schedules with source stats
   * @returns {string} Average fill rate percentage
   */
  calculateAverageFillRate(schedules) {
    const schedulesWithStats = schedules.filter(s => s.dataValues.sourceStats);

    if (schedulesWithStats.length === 0) return '0.00';

    const totalFillRate = schedulesWithStats.reduce((sum, schedule) => {
      return sum + parseFloat(schedule.dataValues.sourceStats.fillRate || 0);
    }, 0);

    return (totalFillRate / schedulesWithStats.length).toFixed(2);
  }

  /**
   * Generate date range array from start to end date
   * @param {string} startDate - Start date (YYYY-MM-DD)
   * @param {string} endDate - End date (YYYY-MM-DD)
   * @returns {Array} Array of date strings
   */
  generateDateRange(startDate, endDate) {
    const dates = [];
    const start = moment(startDate);
    const end = moment(endDate);

    while (start.isSameOrBefore(end)) {
      dates.push(start.format('YYYY-MM-DD'));
      start.add(1, 'day');
    }

    return dates;
  }

  /**
   * Analyze schedule sources for hybrid validation
   * @param {Array} instances - Array of shift instances
   * @returns {Object} Source analysis
   */
  analyzeScheduleSources(instances) {
    const analysis = {
      totalInstances: instances.length,
      sourceTypes: {
        template: 0,
        rotaShift: 0,
        manual: 0
      },
      sourceBreakdown: {
        template: { instances: 0, required: 0, assigned: 0, dates: [] },
        rotaShift: { instances: 0, required: 0, assigned: 0, dates: [] },
        manual: { instances: 0, required: 0, assigned: 0, dates: [] }
      },
      sourceDetails: {
        template: [],
        rotaShift: [],
        manual: []
      },
      sourceGroups: {}, // Temporary for grouping
      fillRate: 0,
      readinessStatus: 'draft'
    };

    let totalRequired = 0;
    let totalAssigned = 0;

    for (const instance of instances) {
      const sourceType = instance.sourceType || 'rotaShift';
      const required = instance.totalRequired || 0;
      const assigned = instance.totalAssigned || 0;

      // Count by source type
      analysis.sourceTypes[sourceType] = (analysis.sourceTypes[sourceType] || 0) + 1;

      // Type-based breakdown
      if (analysis.sourceBreakdown[sourceType]) {
        analysis.sourceBreakdown[sourceType].instances += 1;
        analysis.sourceBreakdown[sourceType].required += required;
        analysis.sourceBreakdown[sourceType].assigned += assigned;
        analysis.sourceBreakdown[sourceType].dates.push(instance.date);
      }

      // Group by sourceId for detailed tracking (temporary)
      const sourceKey = `${sourceType}_${instance.sourceId}`;
      if (!analysis.sourceGroups[sourceKey]) {
        analysis.sourceGroups[sourceKey] = {
          type: sourceType,
          sourceId: instance.sourceId,
          instanceCount: 0,
          required: 0,
          assigned: 0,
          dates: []
        };
      }
      analysis.sourceGroups[sourceKey].instanceCount++;
      analysis.sourceGroups[sourceKey].required += required;
      analysis.sourceGroups[sourceKey].assigned += assigned;
      analysis.sourceGroups[sourceKey].dates.push(instance.date);

      // Calculate fill rate
      totalRequired += required;
      totalAssigned += assigned;
    }

    analysis.fillRate = totalRequired > 0 ? ((totalAssigned / totalRequired) * 100).toFixed(2) : 0;

    // Determine readiness status
    if (totalAssigned === 0) {
      analysis.readinessStatus = 'empty';
    } else if (totalAssigned < totalRequired) {
      analysis.readinessStatus = 'partial';
    } else {
      analysis.readinessStatus = 'full';
    }

    // Convert sourceGroups to type-based arrays for frontend
    for (const [, details] of Object.entries(analysis.sourceGroups)) {
      analysis.sourceDetails[details.type].push({
        sourceId: details.sourceId,
        instances: details.instanceCount,
        required: details.required,
        assigned: details.assigned,
        dates: details.dates
      });
    }

    // Remove temporary sourceGroups
    delete analysis.sourceGroups;

    return analysis;
  }

  /**
   * Bulk assign employees to schedule shifts (Enhanced for scale)
   * @param {number} scheduleId - Schedule ID
   * @param {Array} assignments - Array of assignment objects
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Assignment results
   */
  async bulkAssignEmployees(scheduleId, assignments, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const schedule = await RotaSchedule.findOne({
        where: {
          id: scheduleId,
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId
        },
        transaction
      });

      if (!schedule) {
        throw new NotFoundError('Schedule not found');
      }



      const batchId = require('uuid').v4();
      const results = {
        successful: [],
        failed: [],
        conflicts: [],
        totalProcessed: assignments.length
      };

      // Process assignments in batches for performance
      const batchSize = 100;
      for (let i = 0; i < assignments.length; i += batchSize) {
        const batch = assignments.slice(i, i + batchSize);

        for (const assignment of batch) {
          try {
            // Validate assignment
            const validation = await this.validateEmployeeAssignment(
              assignment,
              scheduleId,
              tenantContext,
              transaction
            );

            if (validation.hasConflicts) {
              results.conflicts.push({
                assignment,
                conflicts: validation.conflicts
              });
              continue;
            }

            // Create assignment
            const shiftAssignment = await ShiftAssignment.create({
              shiftInstanceId: assignment.shiftInstanceId,
              employeeId: assignment.employeeId,
              status: 'assigned',
              assignmentType: 'manual',
              batchId,
              priority: assignment.priority || 0,
              assignedBy: tenantContext.userId,
              assignedAt: new Date(),
              notes: assignment.notes || `Bulk assigned via schedule ${scheduleId}`
            }, { transaction });

            // Update shift instance counters
            await this.updateShiftInstanceCounters(
              assignment.shiftInstanceId,
              transaction
            );

            results.successful.push({
              assignmentId: shiftAssignment.id,
              employeeId: assignment.employeeId,
              shiftInstanceId: assignment.shiftInstanceId
            });

          } catch (error) {
            results.failed.push({
              assignment,
              error: error.message
            });
          }
        }
      }

      await transaction.commit();

      return results;

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Validate employee assignment for conflicts
   * @param {Object} assignment - Assignment object
   * @param {number} scheduleId - Schedule ID
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Validation results
   */
  async validateEmployeeAssignment(assignment, scheduleId, tenantContext, transaction) {
    const validation = {
      hasConflicts: false,
      conflicts: []
    };

    try {
      // Get shift instance details
      const shiftInstance = await RotaShiftInstance.findByPk(assignment.shiftInstanceId, {
        include: [{
          model: RotaShift,
          as: 'rotaShift',
          attributes: ['name', 'startTime', 'endTime']
        }],
        transaction
      });

      if (!shiftInstance) {
        validation.hasConflicts = true;
        validation.conflicts.push('Shift instance not found');
        return validation;
      }

      // Check for overlapping assignments on the same date
      const existingAssignments = await ShiftAssignment.findAll({
        include: [{
          model: RotaShiftInstance,
          as: 'shiftInstance',
          where: {
            date: shiftInstance.date
          },
          include: [{
            model: RotaShift,
            as: 'rotaShift',
            attributes: ['startTime', 'endTime']
          }]
        }],
        where: {
          employeeId: assignment.employeeId,
          status: { [Op.in]: ['assigned', 'confirmed'] }
        },
        transaction
      });

      // Check for time overlaps
      for (const existing of existingAssignments) {
        if (this.checkTimeOverlap(
          shiftInstance.rotaShift.startTime,
          shiftInstance.rotaShift.endTime,
          existing.shiftInstance.rotaShift.startTime,
          existing.shiftInstance.rotaShift.endTime
        )) {
          validation.hasConflicts = true;
          validation.conflicts.push(
            `Employee has overlapping shift on ${shiftInstance.date}`
          );
        }
      }

      // Check capacity limits
      const currentAssignments = await ShiftAssignment.count({
        where: {
          shiftInstanceId: assignment.shiftInstanceId,
          status: { [Op.in]: ['assigned', 'confirmed'] }
        },
        transaction
      });

      if (currentAssignments >= shiftInstance.totalRequired) {
        validation.hasConflicts = true;
        validation.conflicts.push('Shift is already fully staffed');
      }

    } catch (error) {
      validation.hasConflicts = true;
      validation.conflicts.push(`Validation error: ${error.message}`);
    }

    return validation;
  }

  /**
   * Update shift instance assignment counters
   * @param {number} shiftInstanceId - Shift instance ID
   * @param {Object} transaction - Database transaction
   */
  async updateShiftInstanceCounters(shiftInstanceId, transaction) {
    const assignmentCount = await ShiftAssignment.count({
      where: {
        shiftInstanceId,
        status: { [Op.in]: ['assigned', 'confirmed'] }
      },
      transaction
    });

    await RotaShiftInstance.update({
      totalAssigned: assignmentCount
    }, {
      where: { id: shiftInstanceId },
      transaction
    });
  }

  /**
   * Update shift instance requirements (Schedule-specific modifications)
   * @param {number} shiftInstanceId - Shift instance ID
   * @param {Object} requirements - New requirements
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Updated shift instance
   */
  async updateShiftInstanceRequirements(shiftInstanceId, requirements, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      const shiftInstance = await RotaShiftInstance.findByPk(shiftInstanceId, {
        include: [{
          model: RotaSchedule,
          as: 'schedule',
          where: {
            companyId: tenantContext.companyId,
            businessUnitId: tenantContext.businessUnitId
          }
        }],
        transaction
      });

      if (!shiftInstance) {
        throw new NotFoundError('Shift instance not found');
      }



      // Update requirements in junction table (schedule-specific, doesn't affect original RotaShift)
      const updatedRequirements = requirements.designationRequirements || [];
      const customRequirements = requirements.customRequirements || [];
      const totalRequired = updatedRequirements.reduce((sum, req) => sum + req.requiredCount, 0);

      // Remove existing requirements
      await RotaShiftInstanceDesignationRequirement.destroy({
        where: { shiftInstanceId },
        transaction
      });

      // Create new base requirements
      if (updatedRequirements.length > 0) {
        await this.createInstanceDesignationRequirements(
          shiftInstanceId,
          updatedRequirements,
          'base',
          shiftInstance.sourceType || 'manual',
          tenantContext.userId,
          transaction
        );
      }

      // Create custom requirements as overrides (only if they exist)
      if (customRequirements.length > 0) {
        await this.createInstanceDesignationRequirements(
          shiftInstanceId,
          customRequirements,
          'override', // Override type has highest priority
          'manual',
          tenantContext.userId,
          transaction
        );
      }

      // Update instance totals
      await shiftInstance.update({
        totalRequired,
        status: requirements.status || shiftInstance.status
      }, { transaction });

      await transaction.commit();

      return await RotaShiftInstance.findByPk(shiftInstanceId, {
        include: [{
          model: RotaShift,
          as: 'rotaShift'
        }, {
          model: ShiftAssignment,
          as: 'assignments',
          include: [{
            model: Employee,
            as: 'employee',
            attributes: ['id', 'firstName', 'lastName', 'employeeId']
          }]
        }]
      });

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Clone schedule with optional assignments
   * @param {number} id - Schedule ID to clone
   * @param {Object} cloneData - Clone configuration
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Cloned schedule
   */
  async cloneSchedule(id, cloneData, tenantContext) {
    const transaction = await sequelize.transaction();

    try {
      // Get original schedule with hybrid source information
      const originalSchedule = await this.getScheduleById(id, tenantContext, {
        includeInstances: true,
        includeAssignments: cloneData.includeAssignments
      });

      console.log(`🔄 Cloning ${originalSchedule.type} schedule: ${originalSchedule.name}`);

      // Analyze original schedule sources
      const sourceAnalysis = this.analyzeScheduleSources(originalSchedule.shiftInstances || []);
      console.log('📊 Original schedule analysis:', sourceAnalysis);

      // Create new schedule with hybrid support
      const newScheduleData = {
        name: cloneData.name,
        description: cloneData.description || `Cloned from: ${originalSchedule.name}`,
        startDate: cloneData.startDate,
        endDate: cloneData.endDate,
        type: originalSchedule.type, // Preserve hybrid type
        ...cloneData.modifications,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        createdById: tenantContext.userId,
        // Enhanced: Clone metadata
        cloneMetadata: {
          originalScheduleId: originalSchedule.id,
          originalScheduleName: originalSchedule.name,
          clonedAt: new Date(),
          clonedBy: tenantContext.userId,
          sourceAnalysis: sourceAnalysis,
          includeAssignments: cloneData.includeAssignments || false
        }
      };

      const newSchedule = await RotaSchedule.create(newScheduleData, { transaction });

      // Clone instances
      if (originalSchedule.shiftInstances && originalSchedule.shiftInstances.length > 0) {
        await this.cloneScheduleInstances(
          originalSchedule.shiftInstances,
          newSchedule.id,
          cloneData,
          tenantContext,
          transaction
        );
      }

      await transaction.commit();
      return await this.getScheduleById(newSchedule.id, tenantContext);

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }



  /**
   * Get schedule statistics
   * @param {number} id - Schedule ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Schedule statistics
   */
  async getScheduleStatistics(id, tenantContext) {
    const instances = await RotaShiftInstance.findAll({
      where: { scheduleId: id },
      include: [
        {
          model: ShiftAssignment,
          as: 'assignments',
          required: false
        },
        {
          model: RotaShift,
          as: 'rotaShift',
          attributes: ['id']
        }
      ]
    });

    const totalInstances = instances.length;
    const totalAssignments = instances.reduce((sum, instance) =>
      sum + (instance.assignments?.length || 0), 0
    );
    const totalRequiredStaff = instances.reduce((sum, instance) =>
      sum + (instance.totalRequired || instance.actualRequiredCount || 0), 0
    );

    const fullyStaffedInstances = instances.filter(instance => {
      const required = instance.totalRequired || instance.actualRequiredCount || 0;
      const assigned = instance.totalAssigned || instance.assignments?.length || 0;
      return assigned >= required;
    }).length;

    const understaffedInstances = totalInstances - fullyStaffedInstances;
    const staffingPercentage = totalRequiredStaff > 0 ?
      Math.round((totalAssignments / totalRequiredStaff) * 100) : 0;

    // Enhanced: Hybrid schedule source breakdown
    const sourceBreakdown = {
      template: { instances: 0, required: 0, assigned: 0 },
      rotaShift: { instances: 0, required: 0, assigned: 0 },
      manual: { instances: 0, required: 0, assigned: 0 }
    };

    // Type-based sourceDetails (frontend-friendly)
    const sourceDetails = {
      template: [],
      rotaShift: [],
      manual: []
    };

    // Temporary grouping by sourceId
    const sourceGroups = {};

    instances.forEach(instance => {
      const sourceType = instance.sourceType || 'rotaShift';
      const required = instance.totalRequired || instance.actualRequiredCount || 0;
      const assigned = instance.totalAssigned || instance.assignments?.length || 0;

      // Source breakdown
      if (sourceBreakdown[sourceType]) {
        sourceBreakdown[sourceType].instances += 1;
        sourceBreakdown[sourceType].required += required;
        sourceBreakdown[sourceType].assigned += assigned;
      }

      // Group by sourceId for detailed tracking
      const sourceKey = `${sourceType}_${instance.sourceId}`;
      if (!sourceGroups[sourceKey]) {
        sourceGroups[sourceKey] = {
          type: sourceType,
          sourceId: instance.sourceId,
          instances: 0,
          required: 0,
          assigned: 0,
          dates: []
        };
      }
      sourceGroups[sourceKey].instances += 1;
      sourceGroups[sourceKey].required += required;
      sourceGroups[sourceKey].assigned += assigned;
      sourceGroups[sourceKey].dates.push(instance.date);
    });

    // Convert to type-based arrays for frontend
    for (const [key, details] of Object.entries(sourceGroups)) {
      sourceDetails[details.type].push({
        sourceId: details.sourceId,
        instances: details.instances,
        required: details.required,
        assigned: details.assigned,
        dates: details.dates
      });
    }

    return {
      totalInstances,
      totalAssignments,
      totalRequiredStaff,
      fullyStaffedInstances,
      understaffedInstances,
      staffingPercentage,
      averageAssignmentsPerInstance: totalInstances > 0 ?
        Math.round((totalAssignments / totalInstances) * 100) / 100 : 0,

      // Enhanced: Hybrid schedule metrics
      sourceBreakdown,
      sourceDetails,
      hybridMetrics: {
        templateCoverage: totalInstances > 0 ? ((sourceBreakdown.template.instances / totalInstances) * 100).toFixed(2) : 0,
        rotaShiftCoverage: totalInstances > 0 ? ((sourceBreakdown.rotaShift.instances / totalInstances) * 100).toFixed(2) : 0,
        customModifications: instances.filter(i => i.customRequirements && Object.keys(i.customRequirements).length > 0).length
      }
    };
  }

  /**
   * Validate schedule data
   * @param {Object} scheduleData - Schedule data to validate
   * @param {Object} tenantContext - Tenant context
   * @param {number} excludeId - ID to exclude from validation (for updates)
   */
  async validateScheduleData(scheduleData, tenantContext, excludeId = null) {
    const { name, startDate, endDate } = scheduleData;

    // Required fields validation
    if (!name || !startDate || !endDate) {
      throw new ValidationError('Name, start date, and end date are required');
    }

    // Date validation
    const start = moment(startDate);
    const end = moment(endDate);

    if (!start.isValid() || !end.isValid()) {
      throw new ValidationError('Invalid date format');
    }

    if (end.isBefore(start)) {
      throw new ValidationError('End date must be after start date');
    }

    if (end.diff(start, 'days') > 365) {
      throw new ValidationError('Schedule duration cannot exceed 365 days');
    }

    // Check for duplicate names
    const whereClause = {
      name,
      companyId: tenantContext.companyId,
      businessUnitId: tenantContext.businessUnitId,
      status: {
        [Op.notIn]: ['archived', 'cancelled']
      }

    };

    if (excludeId) {
      whereClause.id = { [Op.ne]: excludeId };
    }

    const existingSchedule = await RotaSchedule.findOne({ where: whereClause });
    if (existingSchedule) {
      throw new ValidationError('Schedule with this name already exists');
    }
  }

  /**
   * Validate schedule for publishing with comprehensive checks
   * @param {Object} schedule - Schedule object
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async validateScheduleForPublishing(schedule, tenantContext, transaction) {
    const validationResults = {
      passed: true,
      errors: [],
      warnings: [],
      stats: {}
    };

    // 1. Check if schedule has instances
    const instanceCount = await RotaShiftInstance.count({
      where: { scheduleId: schedule.id },
      transaction
    });

    if (instanceCount === 0) {
      validationResults.errors.push('Cannot publish schedule without shift instances');
      validationResults.passed = false;
    }

    validationResults.stats.totalInstances = instanceCount;

    // 2. Check for minimum staffing requirements
    const understaffedCount = await this.getUnderstaffedInstancesCount(schedule.id, transaction);
    validationResults.stats.understaffedInstances = understaffedCount;

    if (understaffedCount > 0) {
      validationResults.warnings.push(`${understaffedCount} shift instances are understaffed`);
    }

    // 3. Check for assignment conflicts
    const conflicts = await this.detectScheduleConflicts(schedule.id, transaction);
    validationResults.stats.conflicts = conflicts;

    if (conflicts.critical && conflicts.critical.length > 0) {
      validationResults.errors.push(`Critical conflicts found: ${conflicts.critical.join(', ')}`);
      validationResults.passed = false;
    }

    if (conflicts.warnings && conflicts.warnings.length > 0) {
      validationResults.warnings.push(...conflicts.warnings);
    }

    // 4. Check coverage analysis
    const coverage = await this.analyzeCoverage(schedule.id, transaction);
    validationResults.stats.coverage = coverage;

    if (coverage.criticalGaps && coverage.criticalGaps.length > 0) {
      validationResults.errors.push(`Critical coverage gaps: ${coverage.criticalGaps.join(', ')}`);
      validationResults.passed = false;
    }

    // 5. Validate business rules
    const businessRuleViolations = await this.validateBusinessRules(schedule, transaction);
    if (businessRuleViolations.length > 0) {
      validationResults.warnings.push(...businessRuleViolations);
    }

    // Throw error if validation failed
    if (!validationResults.passed) {
      const errorMessage = `Schedule validation failed: ${validationResults.errors.join('; ')}`;
      throw new ValidationError(errorMessage);
    }

    // Log warnings
    if (validationResults.warnings.length > 0) {
      console.warn(`⚠️ Schedule publish warnings: ${validationResults.warnings.join('; ')}`);
    }

    return validationResults;
  }

  /**
   * Get count of understaffed instances
   * @param {number} scheduleId - Schedule ID
   * @param {Object} transaction - Database transaction
   * @returns {number} Count of understaffed instances
   */
  async getUnderstaffedInstancesCount(scheduleId, transaction) {
    const instances = await RotaShiftInstance.findAll({
      where: { scheduleId },
      include: [
        {
          model: ShiftAssignment,
          as: 'assignments',
          required: false
        },
        {
          model: RotaShift,
          as: 'rotaShift',
          attributes: ['id']
        }
      ],
      transaction
    });

    return instances.filter(instance => {
      const required = instance.actualRequiredCount || 0;
      const assigned = instance.assignments?.length || 0;
      return assigned < required;
    }).length;
  }

  /**
   * Detect schedule conflicts
   * @param {number} scheduleId - Schedule ID
   * @param {Object} transaction - Database transaction
   * @returns {Object} Conflict analysis results
   */
  async detectScheduleConflicts(scheduleId, transaction) {
    const conflicts = {
      critical: [],
      warnings: [],
      details: []
    };

    try {
      // Get all assignments for this schedule
      const assignments = await ShiftAssignment.findAll({
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { scheduleId },
            include: [
              {
                model: RotaShift,
                as: 'rotaShift',
                attributes: ['name', 'startTime', 'endTime']
              }
            ]
          },
          {
            model: Employee,
            as: 'employee',
            attributes: ['id', 'firstName', 'lastName', 'employeeId']
          }
        ],
        transaction
      });

      // Group assignments by employee and date
      const employeeAssignments = {};
      assignments.forEach(assignment => {
        const employeeId = assignment.employeeId;
        const date = assignment.shiftInstance.date;
        const key = `${employeeId}_${date}`;

        if (!employeeAssignments[key]) {
          employeeAssignments[key] = [];
        }
        employeeAssignments[key].push(assignment);
      });

      // Check for overlapping shifts
      Object.keys(employeeAssignments).forEach(key => {
        const dayAssignments = employeeAssignments[key];
        if (dayAssignments.length > 1) {
          // Check for time overlaps
          for (let i = 0; i < dayAssignments.length; i++) {
            for (let j = i + 1; j < dayAssignments.length; j++) {
              const shift1 = dayAssignments[i].shiftInstance.rotaShift;
              const shift2 = dayAssignments[j].shiftInstance.rotaShift;

              if (this.checkTimeOverlap(shift1.startTime, shift1.endTime, shift2.startTime, shift2.endTime)) {
                const employee = dayAssignments[i].employee;
                conflicts.critical.push(
                  `Employee ${employee.firstName} ${employee.lastName} has overlapping shifts on ${dayAssignments[i].shiftInstance.date}`
                );
              }
            }
          }
        }
      });

    } catch (error) {
      console.error('Error detecting schedule conflicts:', error);
      conflicts.warnings.push('Unable to complete conflict detection');
    }

    return conflicts;
  }

  /**
   * Check if two time ranges overlap
   * @param {string} start1 - Start time 1
   * @param {string} end1 - End time 1
   * @param {string} start2 - Start time 2
   * @param {string} end2 - End time 2
   * @returns {boolean} Whether times overlap
   */
  checkTimeOverlap(start1, end1, start2, end2) {
    const parseTime = (timeStr) => {
      const [hours, minutes] = timeStr.split(':').map(Number);
      return hours * 60 + minutes;
    };

    const start1Min = parseTime(start1);
    const end1Min = parseTime(end1);
    const start2Min = parseTime(start2);
    const end2Min = parseTime(end2);

    return start1Min < end2Min && start2Min < end1Min;
  }

  /**
   * Analyze schedule coverage
   * @param {number} scheduleId - Schedule ID
   * @param {Object} transaction - Database transaction
   * @returns {Object} Coverage analysis results
   */
  async analyzeCoverage(scheduleId, transaction) {
    const coverage = {
      overallPercentage: 0,
      criticalGaps: [],
      departmentCoverage: {},
      dailyCoverage: {}
    };

    try {
      const instances = await RotaShiftInstance.findAll({
        where: { scheduleId },
        include: [
          {
            model: ShiftAssignment,
            as: 'assignments',
            required: false
          },
          {
            model: RotaShift,
            as: 'rotaShift',
            include: [
              {
                model: Department,
                as: 'department',
                attributes: ['id', 'name']
              }
            ]
          }
        ],
        transaction
      });

      let totalRequired = 0;
      let totalAssigned = 0;

      instances.forEach(instance => {
        const required = instance.actualRequiredCount || 0;
        const assigned = instance.assignments ? instance.assignments.length : 0;

        totalRequired += required;
        totalAssigned += assigned;

        // Check for critical gaps (less than 50% coverage)
        const coveragePercent = required > 0 ? (assigned / required) * 100 : 100;
        if (coveragePercent < 50) {
          const department = instance.rotaShift.department?.name || 'Unknown';
          coverage.criticalGaps.push(
            `${department} - ${instance.rotaShift.name} on ${instance.date}: ${coveragePercent.toFixed(1)}% coverage`
          );
        }

        // Department coverage tracking
        const deptId = instance.rotaShift.department?.id || 'unknown';
        if (!coverage.departmentCoverage[deptId]) {
          coverage.departmentCoverage[deptId] = {
            name: instance.rotaShift.department?.name || 'Unknown',
            required: 0,
            assigned: 0
          };
        }
        coverage.departmentCoverage[deptId].required += required;
        coverage.departmentCoverage[deptId].assigned += assigned;

        // Daily coverage tracking
        if (!coverage.dailyCoverage[instance.date]) {
          coverage.dailyCoverage[instance.date] = { required: 0, assigned: 0 };
        }
        coverage.dailyCoverage[instance.date].required += required;
        coverage.dailyCoverage[instance.date].assigned += assigned;
      });

      coverage.overallPercentage = totalRequired > 0 ? (totalAssigned / totalRequired) * 100 : 100;

    } catch (error) {
      console.error('Error analyzing coverage:', error);
      coverage.criticalGaps.push('Unable to complete coverage analysis');
    }

    return coverage;
  }

  /**
   * Validate business rules
   * @param {Object} schedule - Schedule object
   * @param {Object} transaction - Database transaction
   * @returns {Array} Array of business rule violations
   */
  async validateBusinessRules(schedule, transaction) {
    const violations = [];

    try {
      // Rule 1: Schedule should not be too far in the future (more than 3 months)
      const threeMonthsFromNow = new Date();
      threeMonthsFromNow.setMonth(threeMonthsFromNow.getMonth() + 3);

      if (new Date(schedule.startDate) > threeMonthsFromNow) {
        violations.push('Schedule starts more than 3 months in the future');
      }

      // Rule 2: Schedule should not be in the past
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      if (new Date(schedule.startDate) < today) {
        violations.push('Schedule start date is in the past');
      }

      // Rule 3: Schedule duration should not exceed 6 months
      const startDate = new Date(schedule.startDate);
      const endDate = new Date(schedule.endDate);
      const diffMonths = (endDate.getFullYear() - startDate.getFullYear()) * 12 +
                        (endDate.getMonth() - startDate.getMonth());

      if (diffMonths > 6) {
        violations.push('Schedule duration exceeds 6 months');
      }

    } catch (error) {
      console.error('Error validating business rules:', error);
      violations.push('Unable to complete business rule validation');
    }

    return violations;
  }

  /**
   * Clone schedule instances
   * @param {Array} originalInstances - Original instances
   * @param {number} newScheduleId - New schedule ID
   * @param {Object} cloneData - Clone configuration
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async cloneScheduleInstances(originalInstances, newScheduleId, cloneData, tenantContext, transaction) {
    const startDateDiff = moment(cloneData.startDate).diff(moment(originalInstances[0]?.date || cloneData.startDate), 'days');
    let clonedCount = 0;

    console.log(`🔄 Cloning ${originalInstances.length} instances with ${startDateDiff} day offset`);

    for (const originalInstance of originalInstances) {
      const newDate = moment(originalInstance.date).add(startDateDiff, 'days').format('YYYY-MM-DD');

      // Skip if new date is outside the new schedule range
      if (moment(newDate).isBefore(cloneData.startDate) || moment(newDate).isAfter(cloneData.endDate)) {
        continue;
      }

      // Enhanced: Clone with hybrid source information
      const newInstance = await RotaShiftInstance.create({
        rotaShiftId: originalInstance.rotaShiftId,
        scheduleId: newScheduleId,
        date: newDate,

        // Enhanced: Preserve hybrid source tracking
        sourceType: originalInstance.sourceType || 'rotaShift',
        sourceId: originalInstance.sourceId || originalInstance.rotaShiftId,

        // Remove JSON fields - will be cloned to junction table
        totalRequired: originalInstance.totalRequired || originalInstance.actualRequiredCount || 0,
        totalAssigned: 0, // Reset assignments

        // Enhanced: Clone status and metadata
        status: 'draft', // Always start as draft
        actualRequiredCount: originalInstance.actualRequiredCount, // Legacy compatibility
        notes: originalInstance.notes,

        // Enhanced: Clone metadata
        assignmentMetadata: {
          clonedFrom: originalInstance.id,
          originalDate: originalInstance.date,
          clonedAt: new Date()
        },

        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        createdById: tenantContext.userId
      }, { transaction });

      // Clone designation requirements from junction table
      const originalRequirements = await this.getInstanceDesignationRequirements(originalInstance.id, transaction);
      if (originalRequirements.length > 0) {
        await this.createInstanceDesignationRequirements(
          newInstance.id,
          originalRequirements,
          'base', // Clone as base requirements
          newInstance.sourceType,
          tenantContext.userId,
          transaction
        );
      }

      clonedCount++;

      // Clone assignments if requested
      if (cloneData.includeAssignments && originalInstance.assignments) {
        for (const assignment of originalInstance.assignments) {
          await ShiftAssignment.create({
            shiftInstanceId: newInstance.id,
            employeeId: assignment.employeeId,
            status: 'assigned', // Reset to assigned status
            assignmentType: 'cloned',
            assignedBy: tenantContext.userId,
            assignedAt: new Date(),
            notes: `Cloned from schedule: ${originalInstance.scheduleId}`
          }, { transaction });
        }
      }
    }
  }

  /**
   * Lock schedule assignments to prevent changes after publish
   * @param {number} scheduleId - Schedule ID
   * @param {Object} transaction - Database transaction
   */
  async lockScheduleAssignments(scheduleId, transaction) {
    try {
      // Get all shift instances for this schedule
      const instances = await RotaShiftInstance.findAll({
        where: { scheduleId },
        attributes: ['id'],
        transaction
      });

      const instanceIds = instances.map(instance => instance.id);

      if (instanceIds.length > 0) {
        // Lock all assignments for these instances
        const [updatedCount] = await ShiftAssignment.update(
          {
            status: 'confirmed', // Change status to confirmed when locked
            confirmedAt: new Date()
          },
          {
            where: {
              shiftInstanceId: { [Op.in]: instanceIds },
              status: 'assigned' // Only lock assignments that are still in assigned status
            },
            transaction
          }
        );

        console.log(`🔒 Locked ${updatedCount} shift assignments`);
      }
    } catch (error) {
      console.error('Error locking schedule assignments:', error);
      throw error;
    }
  }

  /**
   * Update schedule statistics after publishing
   * @param {number} scheduleId - Schedule ID
   * @param {Object} transaction - Database transaction
   */
  async updateScheduleStatistics(scheduleId, transaction) {
    try {
      const stats = await this.calculateScheduleStats(scheduleId, transaction);

      await RotaSchedule.update({
        statistics: {
          totalShifts: stats.totalShifts,
          totalAssignments: stats.totalAssignments,
          coveragePercentage: stats.coveragePercentage,
          departmentBreakdown: stats.departmentBreakdown,
          publishedAt: new Date(),
          estimatedCost: stats.estimatedCost || 0
        }
      }, {
        where: { id: scheduleId },
        transaction
      });

      console.log(`📊 Updated statistics: ${stats.totalShifts} shifts, ${stats.totalAssignments} assignments`);
    } catch (error) {
      console.error('Error updating schedule statistics:', error);
      // Don't throw error - statistics update is not critical
    }
  }

  /**
   * Calculate schedule statistics
   * @param {number} scheduleId - Schedule ID
   * @param {Object} transaction - Database transaction
   * @returns {Object} Calculated statistics
   */
  async calculateScheduleStats(scheduleId, transaction) {
    const instances = await RotaShiftInstance.findAll({
      where: { scheduleId },
      include: [
        {
          model: ShiftAssignment,
          as: 'assignments',
          required: false
        },
        {
          model: RotaShift,
          as: 'rotaShift',
          include: [
            {
              model: Department,
              as: 'department',
              attributes: ['id', 'name']
            }
          ]
        }
      ],
      transaction
    });

    let totalShifts = instances.length;
    let totalAssignments = 0;
    let totalRequired = 0;
    const departmentBreakdown = {};

    instances.forEach(instance => {
      const assignmentCount = instance.assignments ? instance.assignments.length : 0;
      const requiredCount = instance.actualRequiredCount || 0;

      totalAssignments += assignmentCount;
      totalRequired += requiredCount;

      // Department breakdown
      const deptId = instance.rotaShift.department?.id || 'unknown';
      const deptName = instance.rotaShift.department?.name || 'Unknown';

      if (!departmentBreakdown[deptId]) {
        departmentBreakdown[deptId] = {
          name: deptName,
          shifts: 0,
          assignments: 0,
          required: 0
        };
      }

      departmentBreakdown[deptId].shifts++;
      departmentBreakdown[deptId].assignments += assignmentCount;
      departmentBreakdown[deptId].required += requiredCount;
    });

    const coveragePercentage = totalRequired > 0 ? (totalAssignments / totalRequired) * 100 : 100;

    return {
      totalShifts,
      totalAssignments,
      coveragePercentage: Math.round(coveragePercentage * 100) / 100,
      departmentBreakdown,
      estimatedCost: totalAssignments * 100 // Simple cost estimation
    };
  }

  /**
   * Create audit log for schedule publishing
   * @param {Object} schedule - Schedule object
   * @param {Object} validationResults - Validation results
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   */
  async createPublishAuditLog(schedule, validationResults, tenantContext, transaction) {
    try {
      const auditData = {
        action: 'schedule_published',
        scheduleId: schedule.id,
        scheduleName: schedule.name,
        publishedBy: tenantContext.userId,
        publishedAt: new Date(),
        validationResults: {
          passed: validationResults.passed,
          warnings: validationResults.warnings,
          stats: validationResults.stats
        },
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      };

      console.log('📝 Audit log created for schedule publish:', auditData.action);
      // In a real system, you would save this to an audit log table
      // await AuditLog.create(auditData, { transaction });

    } catch (error) {
      console.error('Error creating publish audit log:', error);
      // Don't throw error - audit logging is not critical for publish operation
    }
  }

  /**
   * Send enhanced publish notifications to all stakeholders
   * @param {Object} schedule - Schedule object
   * @param {Object} tenantContext - Tenant context
   */
  async sendEnhancedPublishNotifications(schedule, tenantContext) {
    try {
      console.log(`📧 Starting enhanced notifications for schedule: ${schedule.name}`);

      // Get all assigned employees
      const assignedEmployees = await this.getAssignedEmployees(schedule.id);

      // Send notifications to employees
      const employeeNotifications = assignedEmployees.map(employee =>
        this.sendEmployeeScheduleNotification(employee, schedule)
      );

      // Send Excel reports to HR and Managers
      const excelReportNotifications = this.sendScheduleExcelReports(schedule, tenantContext);

      // Execute all notifications
      const results = await Promise.allSettled([
        ...employeeNotifications,
        excelReportNotifications
      ]);

      const successful = results.filter(r => r.status === 'fulfilled').length;
      const failed = results.filter(r => r.status === 'rejected').length;

      console.log(`📧 Notification results: ${successful} successful, ${failed} failed`);

    } catch (error) {
      console.error('Error sending enhanced publish notifications:', error);
    }
  }

  /**
   * Send schedule Excel reports to HR and Managers
   * @param {Object} schedule - Schedule object
   * @param {Object} tenantContext - Tenant context
   */
  async sendScheduleExcelReports(schedule, tenantContext) {
    try {
      console.log(`📊 Generating and sending Excel reports for schedule: ${schedule.name}`);

      // Generate Excel report
      const excelFilePath = await this.generateScheduleExcelReport(schedule, tenantContext);

      // Get HR managers and reporting managers
      const recipients = await this.getScheduleReportRecipients(schedule.id, tenantContext);

      // Send emails with Excel attachment
      const emailPromises = [];

      // Send to HR managers
      for (const hrManager of recipients.hrManagers) {
        emailPromises.push(
          this.sendHRScheduleExcelReport(hrManager, schedule, excelFilePath, tenantContext)
        );
      }

      // Send to reporting managers
      for (const manager of recipients.reportingManagers) {
        emailPromises.push(
          this.sendManagerScheduleExcelReport(manager, schedule, excelFilePath, tenantContext)
        );
      }

      await Promise.allSettled(emailPromises);

      // Clean up temporary file
      const fs = require('fs');
      if (fs.existsSync(excelFilePath)) {
        fs.unlinkSync(excelFilePath);
        console.log(`🗑️ Cleaned up temporary Excel file: ${excelFilePath}`);
      }

      console.log(`✅ Excel reports sent to ${recipients.hrManagers.length} HR managers and ${recipients.reportingManagers.length} reporting managers`);

    } catch (error) {
      console.error('Error sending schedule Excel reports:', error);
      throw error;
    }
  }

  /**
   * Send publish notifications (placeholder for future implementation)
   * @param {Object} schedule - Schedule object
   * @param {Object} tenantContext - Tenant context
   */
  async sendPublishNotifications(schedule, tenantContext) {
    console.log(`📧 Sending publish notifications for schedule: ${schedule.name}`);
    // Future enhancement: Implement notification service integration
    // - Email notifications to assigned employees
    // - Mobile push notifications
    // - Calendar integration
  }

  /**
   * Get assigned employees for a schedule
   * @param {number} scheduleId - Schedule ID
   * @returns {Array} Array of assigned employees
   */
  async getAssignedEmployees(scheduleId) {
    try {
      const assignments = await ShiftAssignment.findAll({
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { scheduleId },
            include: [
              {
                model: RotaShift,
                as: 'rotaShift',
                attributes: ['name', 'startTime', 'endTime']
              }
            ]
          },
          {
            model: Employee,
            as: 'employee',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['email']
              },
              {
                model: BusinessUnit,
                as: 'businessUnit',
                include: [
                  {
                    model: Company,
                    as: 'company',
                    attributes: ['name']
                  }
                ]
              }
            ]
          }
        ]
      });

      // Group by employee to avoid duplicates
      const employeeMap = new Map();
      assignments.forEach(assignment => {
        const employee = assignment.employee;
        if (!employeeMap.has(employee.id)) {
          employeeMap.set(employee.id, {
            ...employee.toJSON(),
            shifts: []
          });
        }
        employeeMap.get(employee.id).shifts.push({
          date: assignment.shiftInstance.date,
          shiftName: assignment.shiftInstance.rotaShift.name,
          startTime: assignment.shiftInstance.rotaShift.startTime,
          endTime: assignment.shiftInstance.rotaShift.endTime
        });
      });

      return Array.from(employeeMap.values());
    } catch (error) {
      console.error('Error getting assigned employees:', error);
      return [];
    }
  }

  /**
   * Send schedule notification to individual employee
   * @param {Object} employee - Employee object
   * @param {Object} schedule - Schedule object
   */
  async sendEmployeeScheduleNotification(employee, schedule) {
    try {
      if (!employee.user?.email) {
        console.warn(`No email found for employee ${employee.firstName} ${employee.lastName}`);
        return;
      }

      const emailService = require('../email/emailService');

      const emailData = {
        to: employee.user.email,
        subject: `📅 New Schedule Published: ${schedule.name}`,
        html: this.generateEmployeeNotificationHTML(employee, schedule)
      };

      await emailService.sendEmail(emailData);
      console.log(`📧 Schedule notification sent to: ${employee.user.email}`);

    } catch (error) {
      console.error(`Error sending notification to employee ${employee.id}:`, error);
    }
  }

  /**
   * Generate HTML content for employee notification
   * @param {Object} employee - Employee object
   * @param {Object} schedule - Schedule object
   * @returns {string} HTML content
   */
  generateEmployeeNotificationHTML(employee, schedule) {
    const companyName = employee.businessUnit?.company?.name || 'Company';
    const shiftsHTML = employee.shifts.map(shift => `
      <tr>
        <td style="padding: 8px; border: 1px solid #ddd;">${shift.date}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">${shift.shiftName}</td>
        <td style="padding: 8px; border: 1px solid #ddd;">${shift.startTime} - ${shift.endTime}</td>
      </tr>
    `).join('');

    return `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #2c3e50;">📅 New Schedule Published</h2>

        <p>Dear ${employee.firstName} ${employee.lastName},</p>

        <p>A new work schedule has been published for you:</p>

        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 15px 0;">
          <h3 style="margin: 0 0 10px 0; color: #495057;">Schedule Details</h3>
          <p><strong>Schedule Name:</strong> ${schedule.name}</p>
          <p><strong>Period:</strong> ${schedule.startDate} to ${schedule.endDate}</p>
          <p><strong>Total Shifts:</strong> ${employee.shifts.length}</p>
        </div>

        <h3>Your Assigned Shifts:</h3>
        <table style="width: 100%; border-collapse: collapse; margin: 15px 0;">
          <thead>
            <tr style="background-color: #e9ecef;">
              <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Date</th>
              <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Shift</th>
              <th style="padding: 10px; border: 1px solid #ddd; text-align: left;">Time</th>
            </tr>
          </thead>
          <tbody>
            ${shiftsHTML}
          </tbody>
        </table>

        <p style="margin-top: 20px;">
          Please review your schedule and contact your manager if you have any questions or concerns.
        </p>

        <hr style="margin: 20px 0;">
        <p style="color: #6c757d; font-size: 12px;">
          This is an automated notification from ${companyName} HRMS System.
        </p>
      </div>
    `;
  }

  /**
   * Send manager notifications
   * @param {Object} schedule - Schedule object
   * @param {number} employeeCount - Number of assigned employees
   */
  async sendManagerNotifications(schedule, employeeCount) {
    try {
      console.log(`📧 Manager notification: Schedule ${schedule.name} published with ${employeeCount} employees`);
      // Future enhancement: Implement manager notification logic
      // - Get department managers
      // - Send summary notifications
    } catch (error) {
      console.error('Error sending manager notifications:', error);
    }
  }

  /**
   * Send HR schedule notification
   * @param {Object} schedule - Schedule object
   * @param {number} employeeCount - Number of assigned employees
   * @param {Object} tenantContext - Tenant context
   */
  async sendHRScheduleNotification(schedule, employeeCount, tenantContext) {
    try {
      console.log(`📧 HR notification: Schedule ${schedule.name} published with ${employeeCount} employees`);
      // Future enhancement: Implement HR notification logic
      // - Get HR team emails
      // - Send schedule summary
    } catch (error) {
      console.error('Error sending HR notification:', error);
    }
  }

  /**
   * Generate Excel report for schedule
   * @param {Object} schedule - Schedule object
   * @param {Object} tenantContext - Tenant context
   * @returns {string} Excel file path
   */
  async generateScheduleExcelReport(schedule, tenantContext) {
    const ExcelJS = require('exceljs');
    const path = require('path');
    const fs = require('fs');

    try {
      console.log(`📊 Generating Excel report for schedule: ${schedule.name}`);

      // Get complete schedule data with assignments
      const scheduleData = await this.getCompleteScheduleData(schedule.id, tenantContext);

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Schedule Report');

      // Set worksheet properties
      worksheet.properties.defaultRowHeight = 20;

      // Add headers
      worksheet.columns = [
        { header: 'Date', key: 'date', width: 12 },
        { header: 'Shift Name', key: 'shiftName', width: 20 },
        { header: 'Start Time', key: 'startTime', width: 12 },
        { header: 'End Time', key: 'endTime', width: 12 },
        { header: 'Department', key: 'department', width: 20 },
        { header: 'Employee ID', key: 'employeeId', width: 15 },
        { header: 'Employee Name', key: 'employeeName', width: 25 },
        { header: 'Email', key: 'email', width: 30 },
        { header: 'Designation', key: 'designation', width: 20 },
        { header: 'Status', key: 'status', width: 15 }
      ];

      // Style headers
      worksheet.getRow(1).font = { bold: true, color: { argb: 'FFFFFF' } };
      worksheet.getRow(1).fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: '366092' }
      };

      // Add data rows
      scheduleData.assignments.forEach((assignment, index) => {
        const row = worksheet.addRow({
          date: assignment.date,
          shiftName: assignment.shiftName,
          startTime: assignment.startTime,
          endTime: assignment.endTime,
          department: assignment.department,
          employeeId: assignment.employeeId,
          employeeName: assignment.employeeName,
          email: assignment.email,
          designation: assignment.designation,
          status: assignment.status
        });

        // Alternate row colors
        if (index % 2 === 0) {
          row.fill = {
            type: 'pattern',
            pattern: 'solid',
            fgColor: { argb: 'F8F9FA' }
          };
        }
      });

      // Add summary section
      const summaryStartRow = scheduleData.assignments.length + 3;
      worksheet.addRow([]);
      worksheet.addRow(['SCHEDULE SUMMARY']);
      worksheet.getRow(summaryStartRow + 1).font = { bold: true, size: 14 };

      worksheet.addRow(['Total Shifts:', scheduleData.statistics.totalShifts]);
      worksheet.addRow(['Total Assignments:', scheduleData.statistics.totalAssignments]);
      worksheet.addRow(['Coverage Percentage:', `${scheduleData.statistics.coveragePercentage}%`]);
      worksheet.addRow(['Published At:', new Date(schedule.publishedAt).toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })]);

      // Auto-fit columns
      worksheet.columns.forEach(column => {
        column.width = Math.max(column.width || 10, 12);
      });

      // Save file
      const fileName = `schedule-report-${schedule.id}-${Date.now()}.xlsx`;
      const filePath = path.join(__dirname, '../../temp', fileName);

      // Ensure temp directory exists
      const tempDir = path.dirname(filePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      await workbook.xlsx.writeFile(filePath);
      console.log(`✅ Excel report generated: ${fileName}`);

      return filePath;

    } catch (error) {
      console.error('Error generating Excel report:', error);
      throw error;
    }
  }

  /**
   * Get complete schedule data for Excel report
   * @param {number} scheduleId - Schedule ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Complete schedule data
   */
  async getCompleteScheduleData(scheduleId, tenantContext) {
    try {
      const assignments = await ShiftAssignment.findAll({
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { scheduleId },
            include: [
              {
                model: RotaShift,
                as: 'rotaShift',
                include: [
                  {
                    model: Department,
                    as: 'department',
                    attributes: ['name']
                  }
                ]
              }
            ]
          },
          {
            model: Employee,
            as: 'employee',
            include: [
              {
                model: User,
                as: 'user',
                attributes: ['email']
              },
              {
                model: Designation,
                as: 'designation',
                attributes: ['name']
              }
            ]
          }
        ],
        order: [
          ['shiftInstance', 'date', 'ASC'],
          ['shiftInstance', 'rotaShift', 'startTime', 'ASC']
        ]
      });

      const formattedAssignments = assignments.map(assignment => ({
        date: assignment.shiftInstance.date,
        shiftName: assignment.shiftInstance.rotaShift.name || 'Unnamed Shift',
        startTime: assignment.shiftInstance.rotaShift.startTime,
        endTime: assignment.shiftInstance.rotaShift.endTime,
        department: assignment.shiftInstance.rotaShift.department?.name || 'N/A',
        employeeId: assignment.employee.employeeId,
        employeeName: `${assignment.employee.firstName} ${assignment.employee.lastName}`,
        email: assignment.employee.user?.email || 'N/A',
        designation: assignment.employee.designation?.name || 'N/A',
        status: assignment.status || 'assigned'
      }));

      // Calculate statistics
      const statistics = await this.calculateScheduleStats(scheduleId);

      return {
        assignments: formattedAssignments,
        statistics
      };

    } catch (error) {
      console.error('Error getting complete schedule data:', error);
      throw error;
    }
  }

  /**
   * Get recipients for schedule reports (HR managers and reporting managers)
   * @param {number} scheduleId - Schedule ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Recipients object
   */
  async getScheduleReportRecipients(scheduleId, tenantContext) {
    try {
      // Get HR managers
      const hrManagers = await Employee.findAll({
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['email']
          },
          {
            model: Designation,
            as: 'designation',
            where: {
              name: { [Op.iLike]: '%HR_Manager%' }
            }
          }
        ],
        where: {
          companyId: tenantContext.companyId,
          businessUnitId: tenantContext.businessUnitId,
          status: 'active'
        }
      });

      // Get reporting managers of assigned employees
      const assignedEmployees = await this.getAssignedEmployees(scheduleId);
      const managerIds = [...new Set(assignedEmployees
        .map(emp => emp.reportingManagerId)
        .filter(id => id)
      )];

      const reportingManagers = await Employee.findAll({
        where: {
          id: { [Op.in]: managerIds },
          status: 'active'
        },
        include: [
          {
            model: User,
            as: 'user',
            attributes: ['email']
          }
        ]
      });

      return {
        hrManagers: hrManagers.filter(hr => hr.user?.email),
        reportingManagers: reportingManagers.filter(mgr => mgr.user?.email)
      };

    } catch (error) {
      console.error('Error getting schedule report recipients:', error);
      return { hrManagers: [], reportingManagers: [] };
    }
  }

  /**
   * Trigger post-publish background tasks
   * @param {Object} schedule - Schedule object
   * @param {Object} tenantContext - Tenant context
   */
  async triggerPostPublishTasks(schedule, tenantContext) {
    try {
      console.log(`🚀 Starting post-publish tasks for schedule: ${schedule.name}`);

      // Task 1: Calendar integration
      // await this.createCalendarEvents(schedule);

      // Task 2: Mobile push notifications
      // await this.sendMobilePushNotifications(schedule);

      // Task 3: Update analytics
      // await this.updateScheduleAnalytics(schedule);

      console.log(`✅ Post-publish tasks completed for schedule: ${schedule.name}`);

    } catch (error) {
      console.error('Error in post-publish tasks:', error);
    }
  }

  /**
   * Generate instances for schedule using simple template-based approach
   * @param {Object} generationRequest - Instance generation request
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Generation results
   */
  async generateScheduleInstances(generationRequest, tenantContext) {
    console.log('🔍 Enhanced generateScheduleInstances called with:', JSON.stringify(generationRequest, null, 2));

    const {
      scheduleId,
      templateIds = [],
      rotaShiftIds = [],
      startDate,
      endDate,
      customRequirements = {},
      generationMode = 'hybrid' // 'template', 'rotaShift', or 'hybrid'
    } = generationRequest;

    const transaction = await sequelize.transaction();

    try {
      // Validate schedule exists and user has permission
      console.log('🔍 Validating schedule...');
      const schedule = await this.getScheduleById(scheduleId, tenantContext);
      if (!schedule) {
        throw new NotFoundError('Schedule not found');
      }
      console.log(`✅ Schedule found: ${schedule.name} (Type: ${schedule.type})`);

      // Enhanced: Support hybrid generation
      let totalGenerated = 0;
      const generationResults = {
        templateInstances: 0,
        rotaShiftInstances: 0,
        errors: []
      };

      // Generate from templates if specified
      if ((generationMode === 'template' || generationMode === 'hybrid') && templateIds.length > 0) {
        console.log(`🔄 Generating from ${templateIds.length} templates...`);

        for (const templateId of templateIds) {
          try {
            const templateSource = {
              type: 'template',
              templateId,
              dates: this.generateDateRange(startDate, endDate),
              customRequirements: customRequirements[`template_${templateId}`] || {}
            };

            const templateInstances = await this._generateFromTemplate(
              scheduleId,
              templateSource,
              tenantContext,
              transaction
            );

            if (templateInstances.length > 0) {
              await RotaShiftInstance.bulkCreate(templateInstances, {
                transaction,
                updateOnDuplicate: ['designationRequirements', 'totalRequired', 'status']
              });

              generationResults.templateInstances += templateInstances.length;
              totalGenerated += templateInstances.length;
            }
          } catch (error) {
            generationResults.errors.push(`Template ${templateId}: ${error.message}`);
          }
        }
      }

      // Generate from individual RotaShifts if specified
      if ((generationMode === 'rotaShift' || generationMode === 'hybrid') && rotaShiftIds.length > 0) {
        console.log(`� Generating from ${rotaShiftIds.length} rota shifts...`);

        for (const rotaShiftId of rotaShiftIds) {
          try {
            const shiftSource = {
              type: 'rotaShift',
              rotaShiftId,
              dates: this.generateDateRange(startDate, endDate),
              customRequirements: customRequirements[`rotaShift_${rotaShiftId}`] || {}
            };

            const shiftInstances = await this._generateFromRotaShift(
              scheduleId,
              shiftSource,
              tenantContext,
              transaction
            );

            if (shiftInstances.length > 0) {
              await RotaShiftInstance.bulkCreate(shiftInstances, {
                transaction,
                updateOnDuplicate: ['designationRequirements', 'totalRequired', 'status']
              });

              generationResults.rotaShiftInstances += shiftInstances.length;
              totalGenerated += shiftInstances.length;
            }
          } catch (error) {
            generationResults.errors.push(`RotaShift ${rotaShiftId}: ${error.message}`);
          }
        }
      }

      await transaction.commit();

      console.log(`✅ Generated ${totalGenerated} instances for schedule ${scheduleId}`);
      console.log('📊 Generation breakdown:', generationResults);

      return {
        generatedInstances: totalGenerated,
        templateInstances: generationResults.templateInstances,
        rotaShiftInstances: generationResults.rotaShiftInstances,
        errors: generationResults.errors,
        generationMode,
        averageConfidence: 100,
        instances: result.created
      };
    } catch (error) {
      console.error('❌ Error in generateScheduleInstances:', error.message);
      console.error('Stack:', error.stack);
      throw error;
    }
  }
  /**
   * Send HR schedule Excel report
   * @param {Object} hrManager - HR manager object
   * @param {Object} schedule - Schedule object
   * @param {string} excelFilePath - Excel file path
   * @param {Object} tenantContext - Tenant context
   */
  async sendHRScheduleExcelReport(hrManager, schedule, excelFilePath, tenantContext) {
    try {
      const ejs = require('ejs');
      const path = require('path');
      const emailService = require('../email/emailService');

      // Get additional data for template
      const scheduleData = await this.getScheduleById(schedule.id, tenantContext, {
        includeInstances: true,
        includeStatistics: true
      });

      const statistics = await this.calculateScheduleStats(schedule.id);
      const businessUnit = await BusinessUnit.findOne({
        where: { id: tenantContext.businessUnitId },
        include: [
          {
            model: Company,
            as: 'company',
            attributes: ['name']
          }
        ]
      });

      const templateData = {
        schedule: scheduleData,
        hrManager: hrManager,
        businessUnit: businessUnit,
        publishedBy: {
          firstName: 'System', // You can get actual user data
          lastName: 'Admin'
        },
        statistics: statistics,
        validationResults: scheduleData.publishValidation || {},
        excelFileName: path.basename(excelFilePath)
      };

      // Render email template
      const emailContent = await ejs.renderFile(
        path.join(__dirname, '../../views/emails/schedule-published-report.ejs'),
        templateData
      );

      const subject = `📅 Schedule Published - ${schedule.name} | Complete Excel Report Attached`;

      await emailService.sendEmail({
        to: hrManager.user.email,
        subject: subject,
        html: emailContent,
        attachments: [
          {
            filename: path.basename(excelFilePath),
            path: excelFilePath
          }
        ]
      });

      console.log(`📧 HR Excel report sent to: ${hrManager.user.email}`);

    } catch (error) {
      console.error(`Error sending HR Excel report to ${hrManager.user?.email}:`, error);
    }
  }

  /**
   * Send Manager schedule Excel report
   * @param {Object} manager - Manager object
   * @param {Object} schedule - Schedule object
   * @param {string} excelFilePath - Excel file path
   * @param {Object} tenantContext - Tenant context
   */
  async sendManagerScheduleExcelReport(manager, schedule, excelFilePath, tenantContext) {
    try {
      const ejs = require('ejs');
      const path = require('path');
      const emailService = require('../email/emailService');

      // Get additional data for template
      const scheduleData = await this.getScheduleById(schedule.id, tenantContext, {
        includeInstances: true,
        includeStatistics: true
      });

      const statistics = await this.calculateScheduleStats(schedule.id);
      const businessUnit = await BusinessUnit.findOne({
        where: { id: tenantContext.businessUnitId },
        include: [
          {
            model: Company,
            as: 'company',
            attributes: ['name']
          }
        ]
      });

      // Get manager's team members in this schedule
      const teamMembers = await this.getManagerTeamInSchedule(manager.id, schedule.id);

      const templateData = {
        schedule: scheduleData,
        manager: manager,
        businessUnit: businessUnit,
        publishedBy: {
          firstName: 'System', // You can get actual user data
          lastName: 'Admin'
        },
        statistics: statistics,
        validationResults: scheduleData.publishValidation || {},
        excelFileName: path.basename(excelFilePath),
        teamMembers: teamMembers
      };

      // Render email template (same template, different data)
      const emailContent = await ejs.renderFile(
        path.join(__dirname, '../../views/emails/schedule-published-report.ejs'),
        templateData
      );

      const subject = `📅 Team Schedule Published - ${schedule.name} | ${teamMembers.length} Team Members Assigned`;

      await emailService.sendEmail({
        to: manager.user.email,
        subject: subject,
        html: emailContent,
        attachments: [
          {
            filename: path.basename(excelFilePath),
            path: excelFilePath
          }
        ]
      });

      console.log(`📧 Manager Excel report sent to: ${manager.user.email} (${teamMembers.length} team members)`);

    } catch (error) {
      console.error(`Error sending Manager Excel report to ${manager.user?.email}:`, error);
    }
  }

  /**
   * Get manager's team members in schedule
   * @param {number} managerId - Manager ID
   * @param {number} scheduleId - Schedule ID
   * @returns {Array} Team members in schedule
   */
  async getManagerTeamInSchedule(managerId, scheduleId) {
    try {
      const teamAssignments = await ShiftAssignment.findAll({
        include: [
          {
            model: RotaShiftInstance,
            as: 'shiftInstance',
            where: { scheduleId }
          },
          {
            model: Employee,
            as: 'employee',
            where: { reportingManagerId: managerId },
            attributes: ['id', 'firstName', 'lastName', 'employeeId']
          }
        ]
      });

      // Group by employee to avoid duplicates
      const employeeMap = new Map();
      teamAssignments.forEach(assignment => {
        const employee = assignment.employee;
        if (!employeeMap.has(employee.id)) {
          employeeMap.set(employee.id, {
            ...employee.toJSON(),
            shiftsCount: 0
          });
        }
        employeeMap.get(employee.id).shiftsCount++;
      });

      return Array.from(employeeMap.values());

    } catch (error) {
      console.error('Error getting manager team in schedule:', error);
      return [];
    }
  }

  /**
   * Validate schedule for conflicts and coverage
   * @param {number} id - Schedule ID
   * @param {Object} options - Validation options
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Validation results
   */
  async validateSchedule(id, options, tenantContext) {
    const { checkConflicts = true, checkCoverage = true } = options;

    const schedule = await RotaSchedule.findOne({
      where: {
        id,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      },
      include: [{
        model: RotaShiftInstance,
        as: 'shiftInstances',
        include: [{
          model: RotaShift,
          as: 'rotaShift'
        }, {
          model: ShiftAssignment,
          as: 'assignments',
          include: [{
            model: Employee,
            as: 'employee'
          }]
        }]
      }]
    });

    if (!schedule) {
      throw new NotFoundError('Schedule not found');
    }

    const validation = {
      passed: true,
      errors: [],
      warnings: [],
      stats: {
        totalInstances: schedule.shiftInstances?.length || 0,
        totalAssignments: 0,
        understaffedInstances: 0,
        overstaffedInstances: 0,
        conflicts: []
      }
    };

    // Count total assignments
    validation.stats.totalAssignments = schedule.shiftInstances?.reduce((sum, instance) =>
      sum + (instance.assignments?.length || 0), 0) || 0;

    // Check conflicts if requested
    if (checkConflicts) {
      const conflicts = await this.detectScheduleConflicts(id);
      validation.stats.conflicts = conflicts;

      if (conflicts.critical && conflicts.critical.length > 0) {
        validation.errors.push(...conflicts.critical);
        validation.passed = false;
      }

      if (conflicts.warnings && conflicts.warnings.length > 0) {
        validation.warnings.push(...conflicts.warnings);
      }
    }

    // Check coverage if requested
    if (checkCoverage) {
      const coverage = await this.analyzeCoverage(id);
      validation.stats.coverage = coverage;

      if (coverage.criticalGaps && coverage.criticalGaps.length > 0) {
        validation.errors.push(...coverage.criticalGaps);
        validation.passed = false;
      }
    }

    return validation;
  }

  /**
   * Cancel schedule
   * @param {number} id - Schedule ID
   * @param {Object} cancelData - Cancel data
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Cancelled schedule
   */
  async cancelSchedule(id, cancelData, tenantContext) {
    const { reason, notifyEmployees = true } = cancelData;

    const schedule = await RotaSchedule.findOne({
      where: {
        id,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      }
    });

    if (!schedule) {
      throw new NotFoundError('Schedule not found');
    }

    if (schedule.status === 'cancelled') {
      throw new ValidationError('Schedule is already cancelled');
    }

    const transaction = await sequelize.transaction();

    try {
      // Update schedule status
      await schedule.update({
        status: 'cancelled',
        cancelledAt: new Date(),
        cancelledBy: tenantContext.userId,
        cancelReason: reason
      }, { transaction });

      // Cancel all shift instances
      await RotaShiftInstance.update({
        status: 'cancelled'
      }, {
        where: { scheduleId: id },
        transaction
      });

      // Cancel all assignments
      await ShiftAssignment.update({
        status: 'cancelled'
      }, {
        where: {
          shiftInstanceId: {
            [Op.in]: sequelize.literal(`(
              SELECT id FROM rota_shift_instances
              WHERE schedule_id = ${id}
            )`)
          }
        },
        transaction
      });

      await transaction.commit();

      // Send notifications if requested
      if (notifyEmployees) {
        await this.sendCancellationNotifications(schedule, tenantContext);
      }

      return await this.getScheduleById(id, tenantContext);

    } catch (error) {
      await transaction.rollback();
      throw error;
    }
  }

  /**
   * Get schedule conflicts
   * @param {number} id - Schedule ID
   * @param {Object} options - Conflict options
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Conflicts data
   */
  async getScheduleConflicts(id, options, tenantContext) {
    const { conflictType, severity } = options;

    const schedule = await RotaSchedule.findOne({
      where: {
        id,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId
      }
    });

    if (!schedule) {
      throw new NotFoundError('Schedule not found');
    }

    const conflicts = await this.detectScheduleConflicts(id);

    // Filter by type if specified
    if (conflictType) {
      Object.keys(conflicts).forEach(key => {
        if (Array.isArray(conflicts[key])) {
          conflicts[key] = conflicts[key].filter(conflict =>
            conflict.type === conflictType
          );
        }
      });
    }

    // Filter by severity if specified
    if (severity) {
      Object.keys(conflicts).forEach(key => {
        if (Array.isArray(conflicts[key])) {
          conflicts[key] = conflicts[key].filter(conflict =>
            conflict.severity === severity
          );
        }
      });
    }

    return {
      scheduleId: id,
      scheduleName: schedule.name,
      conflicts,
      summary: {
        totalConflicts: Object.values(conflicts).flat().length,
        criticalCount: conflicts.critical?.length || 0,
        warningCount: conflicts.warnings?.length || 0
      }
    };
  }

  /**
   * Send cancellation notifications
   * @param {Object} schedule - Schedule object
   * @param {Object} tenantContext - Tenant context
   */
  async sendCancellationNotifications(schedule, tenantContext) {
    try {
      console.log(`📧 Sending cancellation notifications for schedule: ${schedule.name}`);
      // Future enhancement: Implement notification service integration
      // - Email notifications to assigned employees
      // - SMS notifications if enabled
      // - Push notifications to mobile app
    } catch (error) {
      console.error('Failed to send cancellation notifications:', error);
      // Don't throw error - notifications are not critical
    }
  }

  /**
   * Auto-generate schedule name based on template and date range
   * @param {Object} template - Shift template object
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @returns {string} Generated schedule name
   */
  generateScheduleName(template, startDate, endDate) {
    const start = moment(startDate);
    const end = moment(endDate);

    // Calculate duration
    const duration = end.diff(start, 'days') + 1;

    // Generate name based on duration and template
    let scheduleName;

    if (duration <= 7) {
      scheduleName = `Weekly Schedule - ${template.name} (${start.format('MMM DD')} - ${end.format('MMM DD, YYYY')})`;
    } else if (duration <= 31) {
      scheduleName = `Monthly Schedule - ${template.name} (${start.format('MMM YYYY')})`;
    } else {
      scheduleName = `Custom Schedule - ${template.name} (${start.format('MMM DD')} - ${end.format('MMM DD, YYYY')})`;
    }

    return scheduleName;
  }

  /**
   * Auto-generate schedule description based on template and parameters
   * @param {Object} template - Shift template object
   * @param {string} startDate - Start date
   * @param {string} endDate - End date
   * @param {string} category - Schedule category
   * @returns {string} Generated schedule description
   */
  generateScheduleDescription(template, startDate, endDate, category) {
    const start = moment(startDate);
    const end = moment(endDate);
    const duration = end.diff(start, 'days') + 1;

    // Count working days from normalized template configuration
    const workingDays = [];
    let totalShifts = 0;

    if (template.dayConfigs) {
      for (const dayConfig of template.dayConfigs) {
        if (dayConfig.isWorkingDay && dayConfig.dayShifts && dayConfig.dayShifts.length > 0) {
          workingDays.push(dayConfig.dayOfWeek);
          totalShifts += dayConfig.dayShifts.length;
        }
      }
    }

    const description = `Auto-generated ${category} schedule based on template "${template.name}" (${template.code}). ` +
      `Duration: ${duration} days (${start.format('MMM DD, YYYY')} to ${end.format('MMM DD, YYYY')}). ` +
      `Template includes ${workingDays.length} working days with ${totalShifts} shift configurations. ` +
      `Generated on ${moment().format('MMM DD, YYYY [at] HH:mm')}.`;

    return description;
  }

  /**
   * Get shift template by ID with full details
   * @param {number} templateId - Template ID
   * @param {Object} tenantContext - Tenant context
   * @returns {Object} Shift template with details
   */
  async getShiftTemplateById(templateId, tenantContext) {
    console.log(`🔍 Getting shift template: ${templateId}`);

    const template = await ShiftTemplate.findOne({
      where: {
        id: templateId,
        companyId: tenantContext.companyId,
        businessUnitId: tenantContext.businessUnitId,
        isActive: true
      },
      include: [
        {
          model: ShiftTemplateDayConfig,
          as: 'dayConfigs',
          include: [
            {
              model: ShiftTemplateDayShift,
              as: 'dayShifts',
              include: [
                {
                  model: ShiftTemplateDayShiftDesignation,
                  as: 'designationRequirements'
                }
              ]
            }
          ]
        }
      ]
    });

    if (!template) {
      throw new NotFoundError('Shift template not found or inactive');
    }

    console.log(`✅ Found template: ${template.name} (${template.code})`);
    return template;
  }

  /**
   * Check if a date is a holiday
   * @param {string} dateStr - Date string (YYYY-MM-DD)
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Holiday check result
   */
  async checkIfHoliday(dateStr, tenantContext, transaction) {
    try {
      console.log(`🔍 Checking if ${dateStr} is a holiday`);

      // Import Holiday model

      // Check for holidays in the company/business unit
      const holiday = await Holiday.findOne({
        where: {
          date: dateStr,
          companyId: tenantContext.companyId,
          [Op.or]: [
            { businessUnitId: tenantContext.businessUnitId },
            { businessUnitId: null }, // Company-wide holidays
            { isCompanyWide: true }
          ]
        },
        transaction
      });

      if (holiday) {
        console.log(`🏖️ Found holiday: ${holiday.name} on ${dateStr}`);
        return {
          isHoliday: true,
          holidayName: holiday.name,
          holidayType: holiday.optionalHoliday ? 'optional' : 'mandatory',
          isRecurring: holiday.isRecurring,
          holiday: holiday
        };
      }

      console.log(`✅ ${dateStr} is not a holiday`);
      return {
        isHoliday: false,
        holidayName: null,
        holidayType: null,
        isRecurring: false,
        holiday: null
      };

    } catch (error) {
      console.error(`❌ Error checking holiday for ${dateStr}:`, error.message);
      // Don't throw error - assume not a holiday if check fails
      return {
        isHoliday: false,
        holidayName: null,
        holidayType: null,
        isRecurring: false,
        holiday: null,
        error: error.message
      };
    }
  }

  /**
   * Calculate dates from template based on schedule date range and template day configurations
   * @param {Object} template - Shift template with day configurations
   * @param {string} startDate - Schedule start date
   * @param {string} endDate - Schedule end date
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Array} Array of calculated dates
   */
  async _calculateDatesFromTemplate(template, startDate, endDate, tenantContext, transaction) {
    
    const calculatedDates = [];

    // Parse template day configurations from normalized structure
    const configuredDays = [];
    if (template.dayConfigs) {
      for (const dayConfig of template.dayConfigs) {
        if (dayConfig.isWorkingDay && dayConfig.dayShifts && dayConfig.dayShifts.length > 0) {
          configuredDays.push(dayConfig.dayOfWeek);
        }
      }
    }

    console.log(`📋 Template "${template.name}" has configurations for: ${configuredDays.join(', ')}`);

    if (configuredDays.length === 0) {
      console.log(`⚠️ Template "${template.name}" has no working day configurations`);
      return [];
    }

    // Iterate through date range and find matching days
    const currentDate = moment(startDate);
    const endMoment = moment(endDate);
    let totalDays = 0;
    let matchedDays = 0;

    while (currentDate.isSameOrBefore(endMoment)) {
      totalDays++;
      const dateStr = currentDate.format('YYYY-MM-DD');
      const dayName = currentDate.format('dddd').toLowerCase();

      // Step 1: Check if it's weekend (Saturday/Sunday)
      if (dayName === 'saturday' || dayName === 'sunday') {
        console.log(`⏭️ Skipping ${dateStr} (${dayName}) - Weekend`);
        currentDate.add(1, 'day');
        continue;
      }

      // Step 2: Check if date is holiday
      const isHoliday = await this.checkIfHoliday(dateStr, tenantContext, transaction);
      if (isHoliday.isHoliday) {
        console.log(`🏖️ Skipping ${dateStr} (${dayName}) - Holiday: ${isHoliday.holidayName}`);
        currentDate.add(1, 'day');
        continue;
      }

      // Step 3: Check if template has configuration for this day
      if (configuredDays.includes(dayName)) {
        calculatedDates.push(dateStr);
        matchedDays++;
        console.log(`✅ Added ${dateStr} (${dayName}) - Template has configuration`);
      } else {
        console.log(`⏭️ Skipping ${dateStr} (${dayName}) - No template configuration`);
      }

      currentDate.add(1, 'day');
    }

    console.log(`🎯 Date calculation summary:`);
    console.log(`   - Total days in range: ${totalDays}`);
    console.log(`   - Template configured days: ${configuredDays.length}`);
    console.log(`   - Matched dates: ${matchedDays}`);
    console.log(`   - Final calculated dates: ${calculatedDates.length}`);

    return calculatedDates;
  }

  /**
   * Process template-based employee assignments
   * @param {number} scheduleId - Schedule ID
   * @param {Array} templateAssignments - Template assignment objects
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Assignment results
   */
  async processTemplateAssignments(scheduleId, templateAssignments, tenantContext, transaction) {
    console.log(`🎯 Processing ${templateAssignments.length} template-based assignments`);

    const results = {
      successful: 0,
      failed: 0,
      assignments: [],
      errors: []
    };

    for (const assignment of templateAssignments) {
      try {
        const result = await this.processTemplateAssignment(scheduleId, assignment, tenantContext, transaction);
        results.successful += result.successful;
        results.failed += result.failed;
        if (result.assignments) {
          results.assignments.push(...result.assignments);
        }
        if (result.errors) {
          results.errors.push(...result.errors);
        }
      } catch (error) {
        console.error(`❌ Template assignment failed:`, error.message);
        results.failed++;
        results.errors.push({
          assignment: assignment,
          error: error.message
        });
      }
    }

    console.log(`✅ Template assignments completed: ${results.successful} successful, ${results.failed} failed`);
    return results;
  }

  /**
   * Process single template-based assignment
   * @param {number} scheduleId - Schedule ID
   * @param {Object} assignment - Template assignment object
   * @param {Object} tenantContext - Tenant context
   * @param {Object} transaction - Database transaction
   * @returns {Object} Assignment result
   */
  async processTemplateAssignment(scheduleId, assignment, tenantContext, transaction) {
    const {
      templateId,
      dayOfWeek,
      rotaShiftId,
      employeeId,
      date,
      designationId,
      assignmentType = 'template_assigned',
      notes
    } = assignment;

    console.log(`🎯 Processing template assignment: Employee ${employeeId} → Template ${templateId} → ${dayOfWeek} → ${date}`);

    const result = {
      successful: 0,
      failed: 0,
      assignments: [],
      errors: []
    };

    try {
      // 1. Validate template exists and has day configuration (normalized structure)
      const template = await ShiftTemplate.findByPk(templateId, {
        include: [
          {
            model: ShiftTemplateDayConfig,
            as: 'dayConfigs',
            where: {
              dayOfWeek: dayOfWeek,
              isActive: true
            },
            required: false,
            include: [
              {
                model: ShiftTemplateDayShift,
                as: 'dayShifts',
                where: { isActive: true },
                required: false,
                include: [
                  {
                    model: ShiftTemplateDayShiftDesignation,
                    as: 'designationRequirements',
                    where: { isActive: true },
                    required: false
                  }
                ]
              }
            ]
          }
        ],
        transaction
      });

      if (!template) {
        throw new ValidationError(`Template ${templateId} not found`);
      }

      // Find the specific day configuration
      const dayConfig = template.dayConfigs?.find(config => config.dayOfWeek === dayOfWeek);
      if (!dayConfig || !dayConfig.isWorkingDay || !dayConfig.dayShifts || dayConfig.dayShifts.length === 0) {
        throw new ValidationError(`Template ${templateId} has no shifts configured for ${dayOfWeek}`);
      }

      // 2. Validate the rotaShiftId exists in template day configuration
      const shiftConfig = dayConfig.dayShifts.find(shift => shift.rotaShiftId === rotaShiftId);
      if (!shiftConfig) {
        throw new ValidationError(`RotaShift ${rotaShiftId} not configured for ${dayOfWeek} in template ${templateId}`);
      }

      // 3. Find the specific shift instance for this date + rotaShiftId
      const shiftInstance = await RotaShiftInstance.findOne({
        where: {
          scheduleId,
          rotaShiftId,
          date
        },
        include: [
          {
            model: RotaShiftDesignationRequirement,
            as: 'designationRequirements'
          }
        ],
        transaction
      });

      if (!shiftInstance) {
        throw new ValidationError(`Shift instance not found for rotaShiftId ${rotaShiftId} on ${date}`);
      }

      // 4. Validate designation requirement exists for this shift
      const designationReq = shiftInstance.designationRequirements?.find(req => req.designationId === designationId);
      if (!designationReq) {
        throw new ValidationError(`Designation ${designationId} not required for this shift instance`);
      }

      // 5. Check if designation has available slots (allow conflicts as requested)
      console.log(`📊 Designation ${designationId} slots: ${designationReq.assignedCount}/${designationReq.requiredCount}`);

      // 6. Create the assignment using existing service
      const shiftAssignmentService = require('./shiftAssignmentService');
      const assignmentData = {
        rotaShiftId,
        employeeId,
        date,
        designationId,
        assignmentType,
        notes: notes || `Template-based assignment from ${template.name} (${dayOfWeek})`
      };

      const assignmentResult = await shiftAssignmentService._assignEmployeesToScheduleWithTransaction(
        scheduleId,
        [assignmentData],
        tenantContext,
        transaction
      );

      if (assignmentResult.successful > 0) {
        result.successful = 1;
        result.assignments = assignmentResult.assignments || [];
        console.log(`✅ Template assignment successful: Employee ${employeeId} assigned to ${dayOfWeek} shift`);
      } else {
        result.failed = 1;
        result.errors.push(`Assignment failed: ${assignmentResult.errors?.[0] || 'Unknown error'}`);
      }

    } catch (error) {
      console.error(`❌ Template assignment error:`, error.message);
      result.failed = 1;
      result.errors.push(error.message);
    }

    return result;
  }

  // ==========================================
  // JUNCTION TABLE HELPER METHODS
  // ==========================================

  /**
   * Create or update designation requirements in junction table (UPSERT logic)
   * @param {number} instanceId - Shift instance ID
   * @param {Array} requirements - Designation requirements array
   * @param {string} requirementType - Type: 'base', 'custom', 'override'
   * @param {string} sourceType - Source: 'template', 'rotaShift', 'manual', 'forecast'
   * @param {number} userId - User ID for audit
   * @param {Object} transaction - Database transaction
   */
  async createInstanceDesignationRequirements(instanceId, requirements, requirementType = 'base', sourceType = 'template', userId, transaction) {
    if (!requirements || requirements.length === 0) return;

    console.log(`🔧 Creating/updating ${requirements.length} designation requirements for instance ${instanceId} (type: ${requirementType})`);

    for (const req of requirements) {
      try {
        // Check if requirement already exists for this instance + designation
        const existingRequirement = await RotaShiftInstanceDesignationRequirement.findOne({
          where: {
            shiftInstanceId: instanceId,
            designationId: req.designationId
          },
          transaction
        });

        const requirementData = {
          shiftInstanceId: instanceId,
          designationId: req.designationId,
          requiredCount: req.requiredCount || 1,
          assignedCount: req.assignedCount || 0,
          priority: req.priority || 0,
          requirementType,
          sourceType,
          overrideReason: req.overrideReason || null,
          updatedById: userId
        };

        if (existingRequirement) {
          // UPDATE: Override with higher priority requirement type
          const typePriority = { 'override': 3, 'custom': 2, 'base': 1 };
          const existingPriority = typePriority[existingRequirement.requirementType] || 1;
          const newPriority = typePriority[requirementType] || 1;

          // if (newPriority >= existingPriority) {
            await existingRequirement.update(requirementData, { transaction });
          //   console.log(`✅ Updated designation ${req.designationId} requirement (${existingRequirement.requirementType} → ${requirementType})`);
          // } else {
          //   console.log(`⚠️ Skipped designation ${req.designationId} - existing ${existingRequirement.requirementType} has higher priority than ${requirementType}`);
          // }
        } else {
          // CREATE: New requirement
          requirementData.createdById = userId;
          await RotaShiftInstanceDesignationRequirement.create(requirementData, { transaction });
          console.log(`✅ Created designation ${req.designationId} requirement (type: ${requirementType})`);
        }

      } catch (error) {
        console.error(`❌ Error processing designation ${req.designationId} requirement:`, error.message);
        throw error;
      }
    }
  }

  /**
   * Get designation requirements for shift instance (with priority resolution)
   * @param {number} instanceId - Shift instance ID
   * @param {Object} transaction - Database transaction
   * @returns {Array} Resolved designation requirements
   */
  async getInstanceDesignationRequirements(instanceId, transaction) {
    const junctionRequirements = await RotaShiftInstanceDesignationRequirement.findAll({
      where: { shiftInstanceId: instanceId },
      include: [
        {
          model: Designation,
          as: 'designation',
          attributes: ['id', 'name', 'code']
        }
      ],
      order: [['designationId', 'ASC'], ['requirementType', 'DESC']], // Override > Custom > Base
      transaction
    });

    // Resolve conflicts: Override > Custom > Base
    const resolvedRequirements = {};
    const typePriority = { 'override': 3, 'custom': 2, 'base': 1 };

    junctionRequirements.forEach(req => {
      const key = req.designationId;
      const currentPriority = typePriority[req.requirementType] || 1;

      if (!resolvedRequirements[key] ||
          currentPriority > (typePriority[resolvedRequirements[key].requirementType] || 1)) {
        resolvedRequirements[key] = req;
      }
    });

    return Object.values(resolvedRequirements).map(req => ({
      designationId: req.designationId,
      requiredCount: req.requiredCount,
      assignedCount: req.assignedCount,
      priority: req.priority,
      requirementType: req.requirementType,
      sourceType: req.sourceType,
      designation: req.designation
    }));
  }

  /**
   * Update assigned count for designation requirement
   * @param {number} instanceId - Shift instance ID
   * @param {number} designationId - Designation ID
   * @param {number} increment - Count to add (can be negative)
   * @param {Object} transaction - Database transaction
   */
  async updateInstanceDesignationAssignedCount(instanceId, designationId, increment, transaction) {
    await RotaShiftInstanceDesignationRequirement.increment(
      'assignedCount',
      {
        by: increment,
        where: {
          shiftInstanceId: instanceId,
          designationId: designationId
        },
        transaction
      }
    );
  }

  /**
   * Format instance with designation requirements for API response
   * Maintains backward compatibility with JSON field format
   * @param {Object} instance - Shift instance
   * @param {Object} transaction - Database transaction
   * @returns {Object} Formatted instance
   */
  async formatInstanceForResponse(instance, transaction) {
    // Get designation requirements from junction table
    const designationRequirements = await this.getInstanceDesignationRequirements(instance.id, transaction);

    // Format as JSON array (same as before) with designation details
    const formattedRequirements = designationRequirements.map(req => ({
      designationId: req.designationId,
      requiredCount: req.requiredCount,
      assignedCount: req.assignedCount,
      priority: req.priority,
      designation: req.designation ? {
        id: req.designation.id,
        name: req.designation.name,
        code: req.designation.code
      } : null
    }));

    // Return instance with formatted requirements (backward compatible)
    return {
      ...instance.toJSON(),
      designationRequirements: formattedRequirements
    };
  }
}

module.exports = new RotaScheduleService();
