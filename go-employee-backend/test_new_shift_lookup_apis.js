const axios = require('axios');

// Test script for new shift lookup APIs
async function testShiftLookupAPIs() {
  console.log('🧪 Testing New Shift Lookup APIs');
  
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:3000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'Admin@123'
    });
    
    const token = loginResponse.data.result.accessToken;
    console.log('✅ Login successful');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // Test 1: Get employee shift by date
    console.log('\n📋 Test 1: Get Employee Shift by Date');
    console.log('API: GET /api/v1/mobile/employee-shift/:employeeId/:date');
    
    try {
      const employeeShiftResponse = await axios.get(
        'http://localhost:3000/api/v1/mobile/employee-shift/1/2025-12-01',
        { headers }
      );
      
      console.log('✅ Employee Shift API Success');
      console.log('📊 Response:', JSON.stringify(employeeShiftResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ Employee Shift API Error:', error.response?.data || error.message);
    }
    
    // Test 2: Get available shifts by date
    console.log('\n📋 Test 2: Get Available Shifts by Date');
    console.log('API: GET /api/v1/mobile/available-shifts/:date');
    
    try {
      const availableShiftsResponse = await axios.get(
        'http://localhost:3000/api/v1/mobile/available-shifts/2025-12-01',
        { headers }
      );
      
      console.log('✅ Available Shifts API Success');
      console.log('📊 Response:', JSON.stringify(availableShiftsResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ Available Shifts API Error:', error.response?.data || error.message);
    }
    
    // Test 3: Get available shifts with filters
    console.log('\n📋 Test 3: Get Available Shifts with Filters');
    console.log('API: GET /api/v1/mobile/available-shifts/:date?departmentId=1&includeAssignments=true');
    
    try {
      const filteredShiftsResponse = await axios.get(
        'http://localhost:3000/api/v1/mobile/available-shifts/2025-12-01?departmentId=1&includeAssignments=true',
        { headers }
      );
      
      console.log('✅ Filtered Shifts API Success');
      console.log('📊 Response:', JSON.stringify(filteredShiftsResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ Filtered Shifts API Error:', error.response?.data || error.message);
    }
    
    // Test 4: Test with different employee ID
    console.log('\n📋 Test 4: Get Different Employee Shift');
    console.log('API: GET /api/v1/mobile/employee-shift/2/2025-12-01');
    
    try {
      const employee2ShiftResponse = await axios.get(
        'http://localhost:3000/api/v1/mobile/employee-shift/2/2025-12-01',
        { headers }
      );
      
      console.log('✅ Employee 2 Shift API Success');
      console.log('📊 Response:', JSON.stringify(employee2ShiftResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ Employee 2 Shift API Error:', error.response?.data || error.message);
    }
    
    // Test 5: Test with future date
    console.log('\n📋 Test 5: Get Shifts for Future Date');
    console.log('API: GET /api/v1/mobile/available-shifts/2025-12-15');
    
    try {
      const futureShiftsResponse = await axios.get(
        'http://localhost:3000/api/v1/mobile/available-shifts/2025-12-15',
        { headers }
      );
      
      console.log('✅ Future Shifts API Success');
      console.log('📊 Response:', JSON.stringify(futureShiftsResponse.data, null, 2));
      
    } catch (error) {
      console.log('❌ Future Shifts API Error:', error.response?.data || error.message);
    }
    
    console.log('\n🎯 All tests completed!');
    
  } catch (error) {
    console.error('❌ Test setup failed:', error.response?.data || error.message);
  }
}

// Run the test
testShiftLookupAPIs();
