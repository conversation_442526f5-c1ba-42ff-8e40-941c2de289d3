# Mobile Shift Swap API Documentation

## Overview
The Mobile Shift Swap API provides comprehensive endpoints for managing shift swap requests on mobile devices. It follows the same patterns as the leave management system with mobile-optimized responses, extended token expiry, and **full compatibility with web API validators**.

## Base URL
```
/api/v1/mobile/
```

## Authentication
All endpoints require JWT authentication with extended mobile token expiry:
- **Access Token**: 90 days
- **Refresh Token**: 1 year

## Response Format
All mobile endpoints return responses in the following format:
```json
{
  "success": boolean,
  "status_code": number,
  "message": string,
  "result": object|array,
  "time": timestamp
}
```

## ✅ **NEW: Enhanced Validator Compatibility**

**Mobile APIs now support both mobile and web field formats for maximum compatibility:**

### **Field Mapping Support**
- **Mobile Format**: `requesterAssignmentId`, `reason`, `swapType`
- **Web Format**: `currentShiftAssignmentId`, `reasonDescription`, `reasonForSwap`
- **Both Supported**: APIs automatically map mobile fields to web format internally

### **Enhanced Features Added**
- ✅ **Recurring Swaps**: Full support for recurring swap patterns
- ✅ **Advanced Reasons**: Categorized reason types with detailed descriptions
- ✅ **Enhanced Validation**: Same business rules as web APIs
- ✅ **Backward Compatibility**: Existing mobile apps continue to work

## Endpoints

## ✅ **NEW: Target Employee Discovery APIs**

### 1. Get Target Employees
**GET** `/target-employees`

Get all available target employees for shift swap.

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| designation_id | integer | - | Filter by designation ID |
| department_id | integer | - | Filter by department ID |
| date | date | - | Filter by availability on specific date |
| exclude_self | boolean | true | Exclude current user from results |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Target employees retrieved successfully",
  "result": {
    "employees": [
      {
        "employee_id": 123,
        "employee_code": "EMP001",
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "designation": {
          "id": 5,
          "name": "Senior Associate",
          "code": "SA"
        },
        "department": {
          "id": 10,
          "name": "Operations"
        },
        "profile_image": "https://example.com/profile.jpg",
        "is_available": true
      }
    ],
    "total_count": 25,
    "filters_applied": {
      "designation_id": 5,
      "department_id": 10,
      "date": null,
      "exclude_self": true
    }
  },
  "time": 1704967200000
}
```

### 2. Get Target Employees by Date
**GET** `/target-employees/:date`

Get target employees available on specific date.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| date | date | Date in ISO format (YYYY-MM-DD) |

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| designation_id | integer | - | Filter by designation ID |
| department_id | integer | - | Filter by department ID |
| exclude_self | boolean | true | Exclude current user from results |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Target employees for 2025-12-01 retrieved successfully",
  "result": {
    "date": "2025-12-01",
    "employees": [
      {
        "employee_id": 123,
        "employee_code": "EMP001",
        "first_name": "John",
        "last_name": "Doe",
        "full_name": "John Doe",
        "email": "<EMAIL>",
        "phone": "+1234567890",
        "designation": {
          "id": 5,
          "name": "Senior Associate"
        },
        "current_shift": {
          "id": 201,
          "name": "Morning Shift",
          "start_time": "09:00",
          "end_time": "17:00",
          "assignment_id": 301
        },
        "availability_status": "available",
        "can_swap": true
      }
    ],
    "total_count": 15,
    "filters_applied": {
      "designation_id": 5,
      "department_id": null,
      "exclude_self": true
    }
  },
  "time": 1704967200000
}
```

## ✅ **NEW: Shift Lookup APIs**

### 3. Get Employee Shift by Date ✅ **NEW**
**GET** `/employee-shift/:employeeId/:date`

Get specific employee's shift assignment on a given date.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| employeeId | integer | Employee ID |
| date | date | Date in ISO format (YYYY-MM-DD) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Employee shift retrieved successfully",
  "result": {
    "assignment_id": 301,
    "employee": {
      "id": 123,
      "employee_code": "EMP001",
      "name": "John Doe",
      "email": "<EMAIL>"
    },
    "date": "2025-12-01",
    "shift": {
      "id": 201,
      "name": "Morning Shift",
      "template": {
        "id": 101,
        "name": "Morning Template",
        "start_time": "09:00:00",
        "end_time": "17:00:00",
        "duration_hours": 8
      }
    },
    "status": "assigned",
    "can_swap": true,
    "created_at": "2025-01-15T08:00:00Z",
    "updated_at": "2025-01-15T08:00:00Z"
  },
  "time": 1704967200000
}
```

### 4. Get Available Shifts by Date ✅ **NEW**
**GET** `/available-shifts/:date`

Get all available shifts on a specific date with optional filtering.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| date | date | Date in ISO format (YYYY-MM-DD) |

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| departmentId | integer | - | Filter by department ID |
| designationId | integer | - | Filter by designation ID |
| includeAssignments | boolean | false | Include current assignments |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Available shifts for 2025-12-01 retrieved successfully",
  "result": {
    "date": "2025-12-01",
    "total_shifts": 8,
    "available_shifts": 3,
    "shifts": [
      {
        "instance_id": 501,
        "date": "2025-12-01",
        "shift": {
          "id": 201,
          "name": "Morning Shift",
          "department": {
            "id": 10,
            "name": "Operations"
          },
          "template": {
            "id": 101,
            "name": "Morning Template",
            "start_time": "09:00:00",
            "end_time": "17:00:00",
            "duration_hours": 8
          }
        },
        "required_count": 5,
        "assigned_count": 3,
        "available_slots": 2,
        "has_availability": true,
        "assignments": [
          {
            "assignment_id": 301,
            "employee": {
              "id": 123,
              "employee_code": "EMP001",
              "name": "John Doe",
              "designation": {
                "id": 5,
                "name": "Senior Associate"
              }
            },
            "status": "assigned"
          }
        ]
      }
    ],
    "filters_applied": {
      "department_id": null,
      "designation_id": null,
      "include_assignments": false
    }
  },
  "time": 1704967200000
}
```

## Core CRUD Operations

### 3. Get All Shift Swap Requests
**GET** `/shift-swaps`

Get all shift swap requests (my requests and requests for approval).

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| limit | integer | 20 | Items per page (max 50) |
| status | string | - | Filter by status: pending, approved, rejected, executed, cancelled |
| requesterId | integer | - | Filter by requester ID |
| targetId | integer | - | Filter by target employee ID |
| departmentId | integer | - | Filter by department |
| startDate | date | - | Filter from date (ISO format) |
| endDate | date | - | Filter to date (ISO format) |
| search | string | - | Search term (max 100 chars) |
| sortBy | string | createdAt | Sort field: createdAt, requestDate, status, urgency |
| sortOrder | string | desc | Sort order: asc, desc |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap requests retrieved successfully",
  "result": {
    "count": 25,
    "rows": [
      {
        "id": 123,
        "requester_id": 456,
        "target_id": 789,
        "requester_assignment_id": 101,
        "target_assignment_id": 102,
        "swap_date": "2024-01-15",
        "reason": "Personal emergency",
        "notes": "Need to attend family function",
        "status": "pending",
        "urgency": "high",
        "request_date": "2024-01-10T10:00:00Z",
        "response_date": null,
        "is_my_request": true,
        "can_respond": false,
        "can_cancel": true,
        "can_approve": false,
        "can_reject": false,
        "approver_status": "pending",
        "requester": {
          "id": 456,
          "name": "John Doe",
          "employee_id": "EMP001",
          "profile_image": ""
        },
        "target": {
          "id": 789,
          "name": "Jane Smith",
          "employee_id": "EMP002",
          "profile_image": ""
        },
        "requester_shift": {
          "id": 101,
          "shift_name": "Morning Shift",
          "start_time": "09:00",
          "end_time": "17:00",
          "date": "2024-01-15"
        },
        "target_shift": {
          "id": 102,
          "shift_name": "Evening Shift",
          "start_time": "14:00",
          "end_time": "22:00",
          "date": "2024-01-15"
        },
        "created_at": "2024-01-10T10:00:00Z",
        "updated_at": "2024-01-10T10:00:00Z"
      }
    ]
  },
  "time": 1704967200000
}
```

### 4. Create Shift Swap Request ✅ **ENHANCED**
**POST** `/shift-swaps`

Create a new shift swap request with **full web API compatibility**.

#### Request Body Options

**✅ NEW: Enhanced Format (Recommended)**
```json
{
  "currentShiftAssignmentId": 101,
  "swapDate": "2025-12-05",
  "targetEmployeeId": 789,
  "reasonForSwap": "family_commitment",
  "reasonDescription": "Need to attend family function on this day",
  "urgency": "high",
  "notes": "Urgent swap request",
  "swapWithAnyAvailableEmployee": false,
  "swapWithAnyAvailableShift": false,
  "isRecurring": false
}
```

**✅ BACKWARD COMPATIBLE: Mobile Format (Still Supported)**
```json
{
  "requesterAssignmentId": 101,
  "targetEmployeeId": 789,
  "swapType": "direct",
  "reason": "Need to attend family function on this day",
  "notes": "Urgent swap request",
  "urgency": "high"
}
```

**✅ ADVANCED: Recurring Swap Example**
```json
{
  "currentShiftAssignmentId": 101,
  "swapDate": "2025-12-05",
  "targetEmployeeId": 789,
  "reasonForSwap": "education_training",
  "reasonDescription": "Weekly training sessions",
  "urgency": "medium",
  "isRecurring": true,
  "recurringPattern": {
    "frequency": "weekly",
    "endDate": "2025-12-31",
    "specificDays": [1, 3, 5]
  }
}
```

#### Enhanced Field Descriptions
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| **currentShiftAssignmentId** | integer | Yes | ID of requester's shift assignment (web format) |
| requesterAssignmentId | integer | Yes* | Mobile compatibility: same as above |
| **swapDate** | date | Yes | Date when swap should happen (ISO format) |
| targetEmployeeId | integer | No** | ID of target employee |
| targetAssignmentId | integer | No** | ID of target shift assignment |
| desiredShiftId | integer | No** | ID of desired shift |
| **reasonForSwap** | string | No | Category: personal_emergency, medical_appointment, family_commitment, education_training, transportation_issue, work_life_balance, other |
| **reasonDescription** | string | Yes | Detailed explanation (10-500 chars) |
| reason | string | Yes* | Mobile compatibility: maps to reasonDescription |
| urgency | string | No | Urgency: low, medium, high, critical (default: medium) |
| notes | string | No | Additional notes (max 1000 chars) |
| swapWithAnyAvailableEmployee | boolean | No | Accept any available employee (default: false) |
| swapWithAnyAvailableShift | boolean | No | Accept any available shift (default: false) |
| **isRecurring** | boolean | No | Whether this is recurring (default: false) |
| **recurringPattern** | object | No*** | Required if isRecurring is true |

*Mobile compatibility fields - automatically mapped to web format
**At least one target option must be specified
***Required when isRecurring is true

#### Recurring Pattern Object
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| frequency | string | Yes | weekly, bi-weekly, monthly |
| endDate | date | Yes | End date for recurring pattern |
| specificDays | array | No | Days of week (0=Sunday, 6=Saturday) |

#### Response
```json
{
  "success": true,
  "status_code": 201,
  "message": "Shift swap request created successfully",
  "result": {
    "id": 123,
    "requester_id": 456,
    "target_id": 789,
    "requester_assignment_id": 101,
    "target_assignment_id": 102,
    "swap_date": "2024-01-15",
    "reason": "Personal emergency",
    "notes": "Need to attend family function",
    "status": "pending",
    "urgency": "high",
    "request_date": "2024-01-10T10:00:00Z",
    "response_date": null,
    "is_my_request": true,
    "can_respond": false,
    "can_cancel": true,
    "can_approve": false,
    "can_reject": false,
    "created_at": "2024-01-10T10:00:00Z",
    "updated_at": "2024-01-10T10:00:00Z"
  },
  "time": 1704967200000
}
```

### 3. Get Shift Swap Request by ID
**GET** `/shift-swaps/:id`

Get detailed information about a specific shift swap request.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Swap request retrieved successfully",
  "result": {
    "id": 123,
    "requester_id": 456,
    "target_id": 789,
    "requester_assignment_id": 101,
    "target_assignment_id": 102,
    "swap_date": "2024-01-15",
    "reason": "Personal emergency",
    "notes": "Need to attend family function",
    "status": "pending",
    "urgency": "high",
    "request_date": "2024-01-10T10:00:00Z",
    "response_date": null,
    "is_my_request": true,
    "can_respond": false,
    "can_cancel": true,
    "can_approve": false,
    "can_reject": false,
    "approver_status": "pending",
    "requester": {
      "id": 456,
      "name": "John Doe",
      "employee_id": "EMP001",
      "profile_image": ""
    },
    "target": {
      "id": 789,
      "name": "Jane Smith",
      "employee_id": "EMP002",
      "profile_image": ""
    },
    "requester_shift": {
      "id": 101,
      "shift_name": "Morning Shift",
      "start_time": "09:00",
      "end_time": "17:00",
      "date": "2024-01-15"
    },
    "target_shift": {
      "id": 102,
      "shift_name": "Evening Shift",
      "start_time": "14:00",
      "end_time": "22:00",
      "date": "2024-01-15"
    },
    "approvals": [
      {
        "approver_id": 999,
        "approver_name": "Manager Name",
        "status": "Pending",
        "comments": "",
        "step_name": "Manager Approval",
        "created_at": "2024-01-10T10:00:00Z",
        "updated_at": "2024-01-10T10:00:00Z"
      }
    ],
    "created_at": "2024-01-10T10:00:00Z",
    "updated_at": "2024-01-10T10:00:00Z"
  },
  "time": 1704967200000
}
```

### 6. Update Shift Swap Request ✅ **ENHANCED**
**PUT** `/shift-swaps/:id`

Update an existing shift swap request with **full web API compatibility** (only allowed for pending requests by the requester).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body Options

**✅ NEW: Enhanced Format (Recommended)**
```json
{
  "swapDate": "2025-12-06",
  "targetEmployeeId": 456,
  "reasonForSwap": "medical_appointment",
  "reasonDescription": "Updated: Doctor appointment rescheduled",
  "urgency": "high",
  "notes": "Updated notes with more details",
  "swapWithAnyAvailableEmployee": true,
  "isRecurring": false
}
```

**✅ BACKWARD COMPATIBLE: Mobile Format (Still Supported)**
```json
{
  "reason": "Updated reason for swap",
  "notes": "Updated notes",
  "urgency": "medium"
}
```

#### Enhanced Field Descriptions
| Field | Type | Description |
|-------|------|-------------|
| swapDate | date | Updated swap date (ISO format) |
| targetEmployeeId | integer | Updated target employee |
| targetAssignmentId | integer | Updated target assignment |
| desiredShiftId | integer | Updated desired shift |
| reasonForSwap | string | Updated reason category |
| reasonDescription | string | Updated detailed reason (10-500 chars) |
| reason | string | Mobile compatibility: maps to reasonDescription |
| urgency | string | Updated urgency: low, medium, high, critical |
| notes | string | Updated notes (max 1000 chars) |
| swapWithAnyAvailableEmployee | boolean | Updated flexible employee option |
| swapWithAnyAvailableShift | boolean | Updated flexible shift option |
| isRecurring | boolean | Updated recurring flag |
| recurringPattern | object | Updated recurring pattern (if isRecurring is true) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request updated successfully",
  "result": {
    "id": 123,
    "requester_id": 456,
    "target_id": 789,
    "reason": "Updated reason for swap",
    "notes": "Updated notes",
    "urgency": "medium",
    "status": "pending",
    "created_at": "2024-01-10T10:00:00Z",
    "updated_at": "2024-01-10T11:00:00Z"
  },
  "time": 1704967200000
}
```

## Workflow Operations

### 5. Cancel Shift Swap Request
**POST** `/shift-swaps/:id/cancel`

Cancel a shift swap request (only allowed by the requester for pending requests).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "reason": "No longer needed"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request cancelled successfully",
  "result": {},
  "time": 1704967200000
}
```

### 6. Approve Shift Swap Request ✅ **ENHANCED**
**POST** `/shift-swaps/:id/approve`

Approve a shift swap request with **enhanced execution control** (Manager/HR only).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body Options

**✅ NEW: Enhanced Format (Recommended)**
```json
{
  "notes": "Approved due to valid reason and proper documentation",
  "executeImmediately": true,
  "executionNotes": "Auto-executing swap upon approval"
}
```

**✅ BACKWARD COMPATIBLE: Mobile Format (Still Supported)**
```json
{
  "comments": "Approved due to valid reason",
  "notes": "Additional approval notes"
}
```

#### Enhanced Field Descriptions
| Field | Type | Default | Description |
|-------|------|---------|-------------|
| notes | string | - | Approval notes or comments (max 500 chars) |
| comments | string | - | Mobile compatibility: maps to notes |
| executeImmediately | boolean | true | Auto-execute swap upon approval |
| executionNotes | string | - | Additional notes for execution process |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request approved successfully",
  "result": {
    "swap_request": {
      "id": 123,
      "status": "approved",
      "approved_at": "2025-01-15T10:30:00Z",
      "approved_by": {
        "id": 456,
        "name": "Manager Name"
      },
      "execution_status": "executed",
      "executed_at": "2025-01-15T10:30:05Z"
    }
  },
  "time": 1704967200000
}
```

### 7. Reject Shift Swap Request ✅ **ENHANCED**
**POST** `/shift-swaps/:id/reject`

Reject a shift swap request with **categorized rejection reasons** (Manager/HR only).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body Options

**✅ NEW: Enhanced Format (Recommended)**
```json
{
  "reason": "policy_violation",
  "notes": "Does not meet company policy requirements for emergency swaps"
}
```

**✅ BACKWARD COMPATIBLE: Mobile Format (Still Supported)**
```json
{
  "rejectionReason": "Policy violation - insufficient notice period",
  "comments": "Does not meet company policy requirements"
}
```

#### Enhanced Field Descriptions
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| reason | string | Yes | Category: policy_violation, skill_mismatch, scheduling_conflict, insufficient_coverage, other |
| notes | string | No | Detailed rejection explanation (max 500 chars) |
| rejectionReason | string | Yes* | Mobile compatibility: maps to notes with reason='other' |
| comments | string | No | Mobile compatibility: maps to notes |

*Mobile compatibility field - automatically mapped to web format

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request rejected successfully",
  "result": {
    "swap_request": {
      "id": 123,
      "status": "rejected",
      "rejected_at": "2025-01-15T10:30:00Z",
      "rejected_by": {
        "id": 456,
        "name": "Manager Name"
      },
      "rejection_reason": "policy_violation",
      "rejection_notes": "Does not meet company policy requirements"
    }
  },
  "time": 1704967200000
}
```

### 8. Respond to Shift Swap Request
**POST** `/shift-swaps/:id/respond`

Employee response to a swap request (accept/decline by target employee).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "action": "accept",
  "notes": "Happy to help with the swap"
}
```

#### Field Descriptions
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| action | string | Yes | Response: accept, decline |
| notes | string | No | Response message (max 500 chars) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Swap request accepted successfully",
  "result": {},
  "time": 1704967200000
}
```

### 9. Execute Approved Swap
**POST** `/shift-swaps/:id/execute`

Execute an approved swap request (Manager/HR only).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "notes": "Swap executed successfully",
  "executedAt": "2024-01-15T10:00:00Z"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap executed successfully",
  "result": {},
  "time": 1704967200000
}
```

## Bulk Operations

### 10. Bulk Approve Swap Requests
**POST** `/shift-swaps/bulk-approve`

Approve multiple swap requests at once (Manager/HR only).

#### Request Body
```json
{
  "swapRequestIds": [123, 124, 125],
  "comments": "Bulk approved for valid reasons"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Mobile bulk approval completed. 2 successful, 1 failed.",
  "result": {
    "successful": [
      {
        "id": 123,
        "success": true,
        "message": "Swap approved successfully"
      },
      {
        "id": 124,
        "success": true,
        "message": "Swap approved successfully"
      }
    ],
    "failed": [
      {
        "id": 125,
        "success": false,
        "error": "Swap request not found"
      }
    ],
    "summary": {
      "total": 3,
      "successful": 2,
      "failed": 1
    }
  },
  "time": 1704967200000
}
```

### 11. Unified Approve/Reject
**PUT** `/shift-swaps/approve-reject/:id`

Unified endpoint for approving or rejecting a swap request.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "status": "Approved",
  "comment": "Approved with conditions",
  "rejectionReason": "Only required if status is Rejected"
}
```

#### Field Descriptions
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| status | string | Yes | Status: Approved, Rejected |
| comment | string | No | Approval/rejection comment |
| rejectionReason | string | No* | Required if status is Rejected |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request approved successfully",
  "result": {},
  "time": 1704967200000
}
```

### 12. Bulk Approve/Reject (v2)
**PUT** `/v2/shift-swaps/approve-reject`

Bulk approve or reject multiple swap requests.

#### Request Body
```json
{
  "swapRequestIds": [123, 124, 125],
  "status": "Approved",
  "comment": "Bulk approved for operational needs",
  "rejectionReason": "Only required if status is Rejected"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Mobile bulk approved completed. 3 successful, 0 failed.",
  "result": {
    "successful": [
      {
        "id": 123,
        "success": true,
        "message": "Swap approved successfully"
      },
      {
        "id": 124,
        "success": true,
        "message": "Swap approved successfully"
      },
      {
        "id": 125,
        "success": true,
        "message": "Swap approved successfully"
      }
    ],
    "failed": [],
    "summary": {
      "total": 3,
      "successful": 3,
      "failed": 0
    }
  },
  "time": 1704967200000
}
```

## Discovery & Analytics

### 13. Get Available Swaps
**GET** `/available-swaps`

Get available shifts for swapping.

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| startDate | date | today | Start date (ISO format) |
| endDate | date | +14 days | End date (ISO format, max 30 days range) |
| shiftType | string | - | Filter by shift type |
| departmentId | integer | - | Filter by department |
| limit | integer | 20 | Max results (1-50) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Available swaps retrieved successfully",
  "result": [
    {
      "id": 201,
      "requester_id": 789,
      "requester_assignment_id": 301,
      "swap_date": "2024-01-16",
      "reason": "Medical appointment",
      "status": "pending",
      "urgency": "medium",
      "requester": {
        "id": 789,
        "name": "Alice Johnson",
        "employee_id": "EMP003",
        "profile_image": ""
      },
      "requester_shift": {
        "id": 301,
        "shift_name": "Night Shift",
        "start_time": "22:00",
        "end_time": "06:00",
        "date": "2024-01-16"
      },
      "created_at": "2024-01-12T14:00:00Z",
      "updated_at": "2024-01-12T14:00:00Z"
    }
  ],
  "time": 1704967200000
}
```


## Approver-Specific Endpoints

### 18. Get Employee Shift Swaps (For Approval)
**GET** `/employee-shift-swap`

Get shift swap requests assigned to current user for approval (similar to employee-leave endpoint).

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| size | integer | 10 | Items per page (max 50) |
| swap_status | string | pending | Filter by status: pending, approved, rejected |
| search | string | - | Search term (max 100 chars) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap requests for approval retrieved successfully",
  "result": {
    "count": 8,
    "rows": [
      {
        "id": 701,
        "requester_id": 456,
        "target_id": 789,
        "requester_assignment_id": 801,
        "target_assignment_id": 802,
        "swap_date": "2024-01-20",
        "reason": "Personal appointment",
        "status": "pending",
        "urgency": "medium",
        "is_my_request": false,
        "can_approve": true,
        "can_reject": true,
        "approver_status": "pending",
        "requester": {
          "id": 456,
          "name": "John Doe",
          "employee_id": "EMP001",
          "profile_image": ""
        },
        "target": {
          "id": 789,
          "name": "Jane Smith",
          "employee_id": "EMP002",
          "profile_image": ""
        },
        "requester_shift": {
          "id": 801,
          "shift_name": "Morning Shift",
          "start_time": "09:00",
          "end_time": "17:00",
          "date": "2024-01-20"
        },
        "target_shift": {
          "id": 802,
          "shift_name": "Evening Shift",
          "start_time": "14:00",
          "end_time": "22:00",
          "date": "2024-01-20"
        },
        "approvals": [
          {
            "approval_id": 999,
            "swap_request_id": 701,
            "approval_status": "Pending",
            "created_by": 456,
            "createdAt": "2024-01-18T10:00:00Z"
          }
        ],
        "created_at": "2024-01-18T10:00:00Z",
        "updated_at": "2024-01-18T10:00:00Z"
      }
    ]
  },
  "time": 1704967200000
}
```

## Legacy Endpoints (Backward Compatibility)

### 19. Get My Swap Requests (Legacy)
**GET** `/my-swap-requests`

Legacy endpoint - same as `/shift-swaps` but with different query parameters.

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| limit | integer | 20 | Items per page |
| status | string | - | Filter by status |
| type | string | all | Filter type: all, sent, received |

### 20. Create Swap Request (Legacy)
**POST** `/swap-requests`

Legacy endpoint - same as `POST /shift-swaps`.

### 21. Get Swap Request Details (Legacy)
**GET** `/swap-requests/:id`

Legacy endpoint - same as `GET /shift-swaps/:id`.

### 22. Respond to Swap Request (Legacy)
**POST** `/swap-requests/:id/respond`

Legacy endpoint - same as `POST /shift-swaps/:id/respond`.

### 23. Cancel Swap Request (Legacy)
**POST** `/swap-requests/:id/cancel`

Legacy endpoint - same as `POST /shift-swaps/:id/cancel`.

## Error Handling

All endpoints return errors in the following format:

```json
{
  "success": false,
  "status_code": 400,
  "message": "Validation error message",
  "result": {},
  "time": 1704967200000
}
```

### Common Error Codes

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or expired token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Business logic conflict |
| 422 | Unprocessable Entity - Validation failed |
| 500 | Internal Server Error - Server error |

### Common Error Messages

- `"Employee record not found"` - User's employee record not found
- `"Shift swap request not found"` - Swap request doesn't exist
- `"Insufficient permissions to perform this action"` - User lacks required permissions
- `"Swap request cannot be modified in current status"` - Status doesn't allow modification
- `"Invalid swap request data"` - Validation failed
- `"Workflow assignment not found"` - User not assigned to approve this request

## Field Descriptions

### Swap Request Status Values
- `pending` - Initial request, awaiting approval
- `approved` - Approved by manager/HR
- `rejected` - Rejected by manager/HR
- `cancelled` - Cancelled by requester
- `completed` - Swap executed successfully

### Urgency Levels
- `low` - Non-urgent request
- `medium` - Normal priority (default)
- `high` - High priority
- `critical` - Critical/emergency request

### Swap Types
- `direct` - Direct swap with specific employee/shift
- `open` - Open request for any available employee
- `any_shift` - Willing to swap with any available shift

### Permission Flags
- `is_my_request` - True if current user created the request
- `can_respond` - True if current user can accept/decline the request
- `can_cancel` - True if current user can cancel the request
- `can_approve` - True if current user can approve the request
- `can_reject` - True if current user can reject the request

## Rate Limiting

Mobile endpoints have the following rate limits:
- **General endpoints**: 100 requests per minute
- **Bulk operations**: 10 requests per minute
- **Statistics endpoints**: 20 requests per minute

## Mobile-Specific Features

1. **Extended Token Expiry**: 90 days access token, 1 year refresh token
2. **Optimized Pagination**: Smaller page sizes for mobile performance
3. **Compressed Responses**: Minimal data structure for bandwidth efficiency
4. **Offline Support**: Data structure designed for offline caching
5. **Push Notifications**: Integration ready for real-time updates
6. **Date Range Limits**: Shorter date ranges (30-60 days) for mobile performance

## Testing Credentials

For testing the mobile shift swap API:
- **Email**: `<EMAIL>`
- **Password**: `Admin@123`

---

## ✅ **Complete API Summary**

### **🎯 All Available Mobile Shift Swap APIs**

| **API Endpoint** | **Method** | **Purpose** | **Status** |
|------------------|------------|-------------|------------|
| `/target-employees` | GET | Get all available target employees | ✅ **NEW** |
| `/target-employees/:date` | GET | Get target employees for specific date | ✅ **NEW** |
| `/employee-shift/:employeeId/:date` | GET | Get employee's shift on specific date | ✅ **NEW** |
| `/available-shifts/:date` | GET | Get all available shifts on specific date | ✅ **NEW** |
| `/shift-swaps` | GET | Get all swap requests with filters | ✅ Enhanced |
| `/shift-swaps` | POST | Create new swap request | ✅ **Enhanced** |
| `/shift-swaps/:id` | GET | Get specific swap request details | ✅ Enhanced |
| `/shift-swaps/:id` | PUT | Update swap request | ✅ **Enhanced** |
| `/shift-swaps/:id/approve` | POST | Approve swap request | ✅ **Enhanced** |
| `/shift-swaps/:id/reject` | POST | Reject swap request | ✅ **Enhanced** |
| `/shift-swaps/:id/cancel` | POST | Cancel swap request | ✅ Enhanced |
| `/shift-swaps/:id/execute` | POST | Execute approved swap | ✅ Enhanced |
| `/available-swaps` | GET | Get available swap opportunities | ✅ Enhanced |
| `/my-swappable-shifts` | GET | Get my shifts available for swap | ✅ Enhanced |
| `/pending-approvals` | GET | Get pending approvals (Manager/HR) | ✅ Enhanced |
| `/swap-statistics` | GET | Get swap statistics and analytics | ✅ Enhanced |

### **🚀 Key Enhancements Made**

#### **1. ✅ Full Web API Compatibility**
- **Field Mapping**: Automatic mapping between mobile and web field formats
- **Enhanced Validation**: Same business rules as web APIs
- **Advanced Features**: Recurring swaps, categorized reasons, execution control
- **Backward Compatibility**: Existing mobile apps continue to work

#### **2. ✅ New Target Employee Discovery**
- **Smart Filtering**: By designation, department, date, availability
- **Real-time Data**: Current shift assignments and availability status
- **Mobile Optimized**: Lightweight responses with essential employee info

#### **3. ✅ Enhanced Request Management**
- **Flexible Swap Options**: Direct swaps, any employee, any shift
- **Recurring Patterns**: Weekly, bi-weekly, monthly recurring swaps
- **Rich Metadata**: Detailed reasons, urgency levels, execution notes
- **Smart Validation**: Conflict detection and resolution suggestions

#### **4. ✅ Advanced Approval Workflow**
- **Categorized Rejections**: Policy violation, skill mismatch, scheduling conflict
- **Execution Control**: Auto-execute or manual execution options
- **Audit Trail**: Complete approval/rejection history with reasons
- **Role-based Access**: Proper permission enforcement

### **📱 Mobile-Specific Features**

#### **Extended Token Expiry**
- **Access Token**: 90 days (vs 24 hours for web)
- **Refresh Token**: 1 year (vs 30 days for web)

#### **Optimized Response Format**
- **Consistent Structure**: All responses follow mobile format
- **Lightweight Data**: Only essential fields for mobile UI
- **Error Handling**: Mobile-friendly error messages

## Changelog

### Version 2.0.0 ✅ **ENHANCED** (January 2025)
- ✅ **NEW**: Target employee discovery APIs (`/target-employees`, `/target-employees/:date`)
- ✅ **ENHANCED**: Full web API compatibility with automatic field mapping
- ✅ **ENHANCED**: Advanced validation matching web business rules
- ✅ **ENHANCED**: Recurring swap support with flexible patterns
- ✅ **ENHANCED**: Categorized rejection reasons and execution control
- ✅ **ENHANCED**: Rich metadata support (detailed reasons, urgency, notes)
- ✅ **ENHANCED**: Backward compatibility maintained for existing mobile apps
- ✅ **ENHANCED**: Comprehensive documentation with examples

### Version 1.0.0
- Initial release with complete CRUD operations
- Workflow integration with approval system
- Bulk operations support
- Discovery and analytics endpoints
- Legacy endpoint compatibility
- Mobile-optimized responses

---

**📱 Mobile Shift Swap API Documentation - Updated with Full Web Compatibility**
**Version**: 2.0 Enhanced
**Last Updated**: January 2025
**Status**: ✅ Production Ready with Complete Web API Compatibility
