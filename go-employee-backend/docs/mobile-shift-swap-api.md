# Mobile Shift Swap API Documentation

## Overview
The Mobile Shift Swap API provides comprehensive endpoints for managing shift swap requests on mobile devices. It follows the same patterns as the leave management system with mobile-optimized responses and extended token expiry.

## Base URL
```
/api/v1/mobile/
```

## Authentication
All endpoints require JWT authentication with extended mobile token expiry:
- **Access Token**: 90 days
- **Refresh Token**: 1 year

## Response Format
All mobile endpoints return responses in the following format:
```json
{
  "success": boolean,
  "status_code": number,
  "message": string,
  "result": object|array,
  "time": timestamp
}
```

## Endpoints

### 1. Get All Shift Swap Requests
**GET** `/shift-swaps`

Get all shift swap requests (my requests and requests for approval).

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| limit | integer | 20 | Items per page (max 50) |
| status | string | - | Filter by status: pending, approved, rejected, executed, cancelled |
| requesterId | integer | - | Filter by requester ID |
| targetId | integer | - | Filter by target employee ID |
| departmentId | integer | - | Filter by department |
| startDate | date | - | Filter from date (ISO format) |
| endDate | date | - | Filter to date (ISO format) |
| search | string | - | Search term (max 100 chars) |
| sortBy | string | createdAt | Sort field: createdAt, requestDate, status, urgency |
| sortOrder | string | desc | Sort order: asc, desc |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap requests retrieved successfully",
  "result": {
    "count": 25,
    "rows": [
      {
        "id": 123,
        "requester_id": 456,
        "target_id": 789,
        "requester_assignment_id": 101,
        "target_assignment_id": 102,
        "swap_date": "2024-01-15",
        "reason": "Personal emergency",
        "notes": "Need to attend family function",
        "status": "pending",
        "urgency": "high",
        "request_date": "2024-01-10T10:00:00Z",
        "response_date": null,
        "is_my_request": true,
        "can_respond": false,
        "can_cancel": true,
        "can_approve": false,
        "can_reject": false,
        "approver_status": "pending",
        "requester": {
          "id": 456,
          "name": "John Doe",
          "employee_id": "EMP001",
          "profile_image": ""
        },
        "target": {
          "id": 789,
          "name": "Jane Smith",
          "employee_id": "EMP002",
          "profile_image": ""
        },
        "requester_shift": {
          "id": 101,
          "shift_name": "Morning Shift",
          "start_time": "09:00",
          "end_time": "17:00",
          "date": "2024-01-15"
        },
        "target_shift": {
          "id": 102,
          "shift_name": "Evening Shift",
          "start_time": "14:00",
          "end_time": "22:00",
          "date": "2024-01-15"
        },
        "created_at": "2024-01-10T10:00:00Z",
        "updated_at": "2024-01-10T10:00:00Z"
      }
    ]
  },
  "time": 1704967200000
}
```

### 2. Create Shift Swap Request
**POST** `/shift-swaps`

Create a new shift swap request.

#### Request Body
```json
{
  "requester_assignment_id": 101,
  "target_assignment_id": 102,
  "target_employee_id": 789,
  "swap_type": "direct",
  "reason": "Personal emergency",
  "notes": "Need to attend family function",
  "urgency": "high"
}
```

#### Field Descriptions
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| requester_assignment_id | integer | Yes | ID of requester's shift assignment |
| target_assignment_id | integer | No* | ID of target shift assignment |
| target_employee_id | integer | No* | ID of target employee |
| swap_type | string | Yes | Type: direct, open, any_shift |
| reason | string | Yes | Reason for swap (10-500 chars) |
| notes | string | No | Additional notes (max 500 chars) |
| urgency | string | No | Urgency: low, medium, high (default: medium) |

*Either target_assignment_id or target_employee_id must be provided for direct swaps.

#### Response
```json
{
  "success": true,
  "status_code": 201,
  "message": "Shift swap request created successfully",
  "result": {
    "id": 123,
    "requester_id": 456,
    "target_id": 789,
    "requester_assignment_id": 101,
    "target_assignment_id": 102,
    "swap_date": "2024-01-15",
    "reason": "Personal emergency",
    "notes": "Need to attend family function",
    "status": "pending",
    "urgency": "high",
    "request_date": "2024-01-10T10:00:00Z",
    "response_date": null,
    "is_my_request": true,
    "can_respond": false,
    "can_cancel": true,
    "can_approve": false,
    "can_reject": false,
    "created_at": "2024-01-10T10:00:00Z",
    "updated_at": "2024-01-10T10:00:00Z"
  },
  "time": 1704967200000
}
```

### 3. Get Shift Swap Request by ID
**GET** `/shift-swaps/:id`

Get detailed information about a specific shift swap request.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Swap request retrieved successfully",
  "result": {
    "id": 123,
    "requester_id": 456,
    "target_id": 789,
    "requester_assignment_id": 101,
    "target_assignment_id": 102,
    "swap_date": "2024-01-15",
    "reason": "Personal emergency",
    "notes": "Need to attend family function",
    "status": "pending",
    "urgency": "high",
    "request_date": "2024-01-10T10:00:00Z",
    "response_date": null,
    "is_my_request": true,
    "can_respond": false,
    "can_cancel": true,
    "can_approve": false,
    "can_reject": false,
    "approver_status": "pending",
    "requester": {
      "id": 456,
      "name": "John Doe",
      "employee_id": "EMP001",
      "profile_image": ""
    },
    "target": {
      "id": 789,
      "name": "Jane Smith",
      "employee_id": "EMP002",
      "profile_image": ""
    },
    "requester_shift": {
      "id": 101,
      "shift_name": "Morning Shift",
      "start_time": "09:00",
      "end_time": "17:00",
      "date": "2024-01-15"
    },
    "target_shift": {
      "id": 102,
      "shift_name": "Evening Shift",
      "start_time": "14:00",
      "end_time": "22:00",
      "date": "2024-01-15"
    },
    "approvals": [
      {
        "approver_id": 999,
        "approver_name": "Manager Name",
        "status": "Pending",
        "comments": "",
        "step_name": "Manager Approval",
        "created_at": "2024-01-10T10:00:00Z",
        "updated_at": "2024-01-10T10:00:00Z"
      }
    ],
    "created_at": "2024-01-10T10:00:00Z",
    "updated_at": "2024-01-10T10:00:00Z"
  },
  "time": 1704967200000
}
```

### 4. Update Shift Swap Request
**PUT** `/shift-swaps/:id`

Update an existing shift swap request (only allowed for pending requests by the requester).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "reason": "Updated reason for swap",
  "notes": "Updated notes",
  "urgency": "medium"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request updated successfully",
  "result": {
    "id": 123,
    "requester_id": 456,
    "target_id": 789,
    "reason": "Updated reason for swap",
    "notes": "Updated notes",
    "urgency": "medium",
    "status": "pending",
    "created_at": "2024-01-10T10:00:00Z",
    "updated_at": "2024-01-10T11:00:00Z"
  },
  "time": 1704967200000
}
```

## Workflow Operations

### 5. Cancel Shift Swap Request
**POST** `/shift-swaps/:id/cancel`

Cancel a shift swap request (only allowed by the requester for pending requests).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "reason": "No longer needed"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request cancelled successfully",
  "result": {},
  "time": 1704967200000
}
```

### 6. Approve Shift Swap Request
**POST** `/shift-swaps/:id/approve`

Approve a shift swap request (Manager/HR only).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "comments": "Approved due to valid reason",
  "notes": "Additional approval notes"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request approved successfully",
  "result": {},
  "time": 1704967200000
}
```

### 7. Reject Shift Swap Request
**POST** `/shift-swaps/:id/reject`

Reject a shift swap request (Manager/HR only).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "rejectionReason": "Insufficient staffing on requested date",
  "comments": "Additional rejection comments"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request rejected successfully",
  "result": {},
  "time": 1704967200000
}
```

### 8. Respond to Shift Swap Request
**POST** `/shift-swaps/:id/respond`

Employee response to a swap request (accept/decline by target employee).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "action": "accept",
  "notes": "Happy to help with the swap"
}
```

#### Field Descriptions
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| action | string | Yes | Response: accept, decline |
| notes | string | No | Response message (max 500 chars) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Swap request accepted successfully",
  "result": {},
  "time": 1704967200000
}
```

### 9. Execute Approved Swap
**POST** `/shift-swaps/:id/execute`

Execute an approved swap request (Manager/HR only).

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "notes": "Swap executed successfully",
  "executedAt": "2024-01-15T10:00:00Z"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap executed successfully",
  "result": {},
  "time": 1704967200000
}
```

## Bulk Operations

### 10. Bulk Approve Swap Requests
**POST** `/shift-swaps/bulk-approve`

Approve multiple swap requests at once (Manager/HR only).

#### Request Body
```json
{
  "swapRequestIds": [123, 124, 125],
  "comments": "Bulk approved for valid reasons"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Mobile bulk approval completed. 2 successful, 1 failed.",
  "result": {
    "successful": [
      {
        "id": 123,
        "success": true,
        "message": "Swap approved successfully"
      },
      {
        "id": 124,
        "success": true,
        "message": "Swap approved successfully"
      }
    ],
    "failed": [
      {
        "id": 125,
        "success": false,
        "error": "Swap request not found"
      }
    ],
    "summary": {
      "total": 3,
      "successful": 2,
      "failed": 1
    }
  },
  "time": 1704967200000
}
```

### 11. Unified Approve/Reject
**PUT** `/shift-swaps/approve-reject/:id`

Unified endpoint for approving or rejecting a swap request.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Request Body
```json
{
  "status": "Approved",
  "comment": "Approved with conditions",
  "rejectionReason": "Only required if status is Rejected"
}
```

#### Field Descriptions
| Field | Type | Required | Description |
|-------|------|----------|-------------|
| status | string | Yes | Status: Approved, Rejected |
| comment | string | No | Approval/rejection comment |
| rejectionReason | string | No* | Required if status is Rejected |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap request approved successfully",
  "result": {},
  "time": 1704967200000
}
```

### 12. Bulk Approve/Reject (v2)
**PUT** `/v2/shift-swaps/approve-reject`

Bulk approve or reject multiple swap requests.

#### Request Body
```json
{
  "swapRequestIds": [123, 124, 125],
  "status": "Approved",
  "comment": "Bulk approved for operational needs",
  "rejectionReason": "Only required if status is Rejected"
}
```

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Mobile bulk approved completed. 3 successful, 0 failed.",
  "result": {
    "successful": [
      {
        "id": 123,
        "success": true,
        "message": "Swap approved successfully"
      },
      {
        "id": 124,
        "success": true,
        "message": "Swap approved successfully"
      },
      {
        "id": 125,
        "success": true,
        "message": "Swap approved successfully"
      }
    ],
    "failed": [],
    "summary": {
      "total": 3,
      "successful": 3,
      "failed": 0
    }
  },
  "time": 1704967200000
}
```

## Discovery & Analytics

### 13. Get Available Swaps
**GET** `/available-swaps`

Get available shifts for swapping.

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| startDate | date | today | Start date (ISO format) |
| endDate | date | +14 days | End date (ISO format, max 30 days range) |
| shiftType | string | - | Filter by shift type |
| departmentId | integer | - | Filter by department |
| limit | integer | 20 | Max results (1-50) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Available swaps retrieved successfully",
  "result": [
    {
      "id": 201,
      "requester_id": 789,
      "requester_assignment_id": 301,
      "swap_date": "2024-01-16",
      "reason": "Medical appointment",
      "status": "pending",
      "urgency": "medium",
      "requester": {
        "id": 789,
        "name": "Alice Johnson",
        "employee_id": "EMP003",
        "profile_image": ""
      },
      "requester_shift": {
        "id": 301,
        "shift_name": "Night Shift",
        "start_time": "22:00",
        "end_time": "06:00",
        "date": "2024-01-16"
      },
      "created_at": "2024-01-12T14:00:00Z",
      "updated_at": "2024-01-12T14:00:00Z"
    }
  ],
  "time": 1704967200000
}
```

### 14. Get My Swappable Shifts
**GET** `/my-swappable-shifts`

Get current user's shifts that can be swapped.

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| startDate | date | today | Start date (ISO format) |
| endDate | date | +30 days | End date (ISO format, max 60 days range) |
| limit | integer | 20 | Max results (1-50) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "My swappable shifts retrieved successfully",
  "result": [
    {
      "id": 401,
      "shift_name": "Morning Shift",
      "start_time": "09:00",
      "end_time": "17:00",
      "date": "2024-01-17",
      "can_swap": true,
      "swap_restrictions": [],
      "created_at": "2024-01-01T00:00:00Z",
      "updated_at": "2024-01-01T00:00:00Z"
    }
  ],
  "time": 1704967200000
}
```

### 15. Get Pending Approvals
**GET** `/shift-swaps/pending-approvals`

Get pending approvals for current user (Manager/HR view).

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| limit | integer | 20 | Items per page (max 50) |
| departmentId | integer | - | Filter by department |
| urgency | string | - | Filter by urgency: low, medium, high |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Pending approvals retrieved successfully",
  "result": {
    "count": 5,
    "rows": [
      {
        "id": 501,
        "requester_id": 456,
        "target_id": 789,
        "requester_assignment_id": 601,
        "target_assignment_id": 602,
        "swap_date": "2024-01-18",
        "reason": "Family emergency",
        "status": "pending",
        "urgency": "high",
        "can_approve": true,
        "can_reject": true,
        "approver_status": "pending",
        "requester": {
          "id": 456,
          "name": "John Doe",
          "employee_id": "EMP001",
          "profile_image": ""
        },
        "created_at": "2024-01-15T09:00:00Z",
        "updated_at": "2024-01-15T09:00:00Z"
      }
    ]
  },
  "time": 1704967200000
}
```

### 16. Get Swap Statistics
**GET** `/shift-swaps/statistics`

Get swap statistics and analytics for current user.

#### Query Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| startDate | date | Start date for statistics (ISO format) |
| endDate | date | End date for statistics (ISO format) |
| departmentId | integer | Filter by department |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Swap statistics retrieved successfully",
  "result": {
    "total_requests": 25,
    "approved_requests": 18,
    "rejected_requests": 3,
    "pending_requests": 4,
    "executed_swaps": 15,
    "my_requests": 12,
    "requests_for_me": 8,
    "approval_rate": 75.0,
    "response_time_avg": 2.5,
    "most_swapped_shift": "Morning Shift",
    "department_stats": [
      {
        "department": "Operations",
        "total_swaps": 15,
        "approval_rate": 80.0
      }
    ]
  },
  "time": 1704967200000
}
```

### 17. Get Eligible Employees
**GET** `/shift-swaps/:id/eligible-employees`

Get eligible employees for a specific swap request.

#### Path Parameters
| Parameter | Type | Description |
|-----------|------|-------------|
| id | integer | Shift swap request ID |

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| departmentId | integer | - | Filter by department |
| shiftType | string | - | Filter by shift type |
| limit | integer | 50 | Max results (1-100) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Eligible employees retrieved successfully",
  "result": [
    {
      "id": 789,
      "name": "Jane Smith",
      "employee_id": "EMP002",
      "profile_image": "",
      "department": "Operations",
      "designation": "Senior Associate",
      "shift_compatibility": "compatible",
      "availability_score": 95
    }
  ],
  "time": 1704967200000
}
```

## Approver-Specific Endpoints

### 18. Get Employee Shift Swaps (For Approval)
**GET** `/employee-shift-swap`

Get shift swap requests assigned to current user for approval (similar to employee-leave endpoint).

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| size | integer | 10 | Items per page (max 50) |
| swap_status | string | pending | Filter by status: pending, approved, rejected |
| search | string | - | Search term (max 100 chars) |

#### Response
```json
{
  "success": true,
  "status_code": 200,
  "message": "Shift swap requests for approval retrieved successfully",
  "result": {
    "count": 8,
    "rows": [
      {
        "id": 701,
        "requester_id": 456,
        "target_id": 789,
        "requester_assignment_id": 801,
        "target_assignment_id": 802,
        "swap_date": "2024-01-20",
        "reason": "Personal appointment",
        "status": "pending",
        "urgency": "medium",
        "is_my_request": false,
        "can_approve": true,
        "can_reject": true,
        "approver_status": "pending",
        "requester": {
          "id": 456,
          "name": "John Doe",
          "employee_id": "EMP001",
          "profile_image": ""
        },
        "target": {
          "id": 789,
          "name": "Jane Smith",
          "employee_id": "EMP002",
          "profile_image": ""
        },
        "requester_shift": {
          "id": 801,
          "shift_name": "Morning Shift",
          "start_time": "09:00",
          "end_time": "17:00",
          "date": "2024-01-20"
        },
        "target_shift": {
          "id": 802,
          "shift_name": "Evening Shift",
          "start_time": "14:00",
          "end_time": "22:00",
          "date": "2024-01-20"
        },
        "approvals": [
          {
            "approval_id": 999,
            "swap_request_id": 701,
            "approval_status": "Pending",
            "created_by": 456,
            "createdAt": "2024-01-18T10:00:00Z"
          }
        ],
        "created_at": "2024-01-18T10:00:00Z",
        "updated_at": "2024-01-18T10:00:00Z"
      }
    ]
  },
  "time": 1704967200000
}
```

## Legacy Endpoints (Backward Compatibility)

### 19. Get My Swap Requests (Legacy)
**GET** `/my-swap-requests`

Legacy endpoint - same as `/shift-swaps` but with different query parameters.

#### Query Parameters
| Parameter | Type | Default | Description |
|-----------|------|---------|-------------|
| page | integer | 1 | Page number |
| limit | integer | 20 | Items per page |
| status | string | - | Filter by status |
| type | string | all | Filter type: all, sent, received |

### 20. Create Swap Request (Legacy)
**POST** `/swap-requests`

Legacy endpoint - same as `POST /shift-swaps`.

### 21. Get Swap Request Details (Legacy)
**GET** `/swap-requests/:id`

Legacy endpoint - same as `GET /shift-swaps/:id`.

### 22. Respond to Swap Request (Legacy)
**POST** `/swap-requests/:id/respond`

Legacy endpoint - same as `POST /shift-swaps/:id/respond`.

### 23. Cancel Swap Request (Legacy)
**POST** `/swap-requests/:id/cancel`

Legacy endpoint - same as `POST /shift-swaps/:id/cancel`.

## Error Handling

All endpoints return errors in the following format:

```json
{
  "success": false,
  "status_code": 400,
  "message": "Validation error message",
  "result": {},
  "time": 1704967200000
}
```

### Common Error Codes

| Status Code | Description |
|-------------|-------------|
| 400 | Bad Request - Invalid input data |
| 401 | Unauthorized - Invalid or expired token |
| 403 | Forbidden - Insufficient permissions |
| 404 | Not Found - Resource not found |
| 409 | Conflict - Business logic conflict |
| 422 | Unprocessable Entity - Validation failed |
| 500 | Internal Server Error - Server error |

### Common Error Messages

- `"Employee record not found"` - User's employee record not found
- `"Shift swap request not found"` - Swap request doesn't exist
- `"Insufficient permissions to perform this action"` - User lacks required permissions
- `"Swap request cannot be modified in current status"` - Status doesn't allow modification
- `"Invalid swap request data"` - Validation failed
- `"Workflow assignment not found"` - User not assigned to approve this request

## Field Descriptions

### Swap Request Status Values
- `pending` - Initial request, awaiting approval
- `approved` - Approved by manager/HR
- `rejected` - Rejected by manager/HR
- `cancelled` - Cancelled by requester
- `completed` - Swap executed successfully

### Urgency Levels
- `low` - Non-urgent request
- `medium` - Normal priority (default)
- `high` - High priority
- `critical` - Critical/emergency request

### Swap Types
- `direct` - Direct swap with specific employee/shift
- `open` - Open request for any available employee
- `any_shift` - Willing to swap with any available shift

### Permission Flags
- `is_my_request` - True if current user created the request
- `can_respond` - True if current user can accept/decline the request
- `can_cancel` - True if current user can cancel the request
- `can_approve` - True if current user can approve the request
- `can_reject` - True if current user can reject the request

## Rate Limiting

Mobile endpoints have the following rate limits:
- **General endpoints**: 100 requests per minute
- **Bulk operations**: 10 requests per minute
- **Statistics endpoints**: 20 requests per minute

## Mobile-Specific Features

1. **Extended Token Expiry**: 90 days access token, 1 year refresh token
2. **Optimized Pagination**: Smaller page sizes for mobile performance
3. **Compressed Responses**: Minimal data structure for bandwidth efficiency
4. **Offline Support**: Data structure designed for offline caching
5. **Push Notifications**: Integration ready for real-time updates
6. **Date Range Limits**: Shorter date ranges (30-60 days) for mobile performance

## Testing Credentials

For testing the mobile shift swap API:
- **Email**: `<EMAIL>`
- **Password**: `Admin@123`

## Changelog

### Version 1.0.0
- Initial release with complete CRUD operations
- Workflow integration with approval system
- Bulk operations support
- Discovery and analytics endpoints
- Legacy endpoint compatibility
- Mobile-optimized responses
