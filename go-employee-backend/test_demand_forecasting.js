#!/usr/bin/env node

/**
 * Test script for demand forecasting integration with auto-schedule generation
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api/v1';
const AUTH_TOKEN = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9EulOLBEjtUyWPKo9ykpprtJiRvJe1M67bYRXYjl21k';

async function testDemandForecastingSchedule() {
  try {
    console.log('🎯 Testing Demand Forecasting-based Auto Schedule Generation...\n');

    // Test payload for demand forecasting mode
    const payload = {
      forecastMode: {
        startDate: '2025-12-01',
        endDate: '2025-12-15',
        scheduleCategory: 'custom',
        customName: `Demand Forecast Test Schedule ${Date.now()}`,
        customDescription: 'Testing demand forecasting integration with auto-schedule generation'
      },
      constraints: {
        autoAssignEmployees: true,
        respectAvailability: true,
        balanceWorkload: true,
        designationPriority: true
      }
    };

    console.log('📤 Sending request with payload:');
    console.log(JSON.stringify(payload, null, 2));
    console.log('\n');

    const response = await axios.post(
      `${BASE_URL}/rota-schedules/auto-generate`,
      payload,
      {
        headers: {
          'Authorization': `Bearer ${AUTH_TOKEN}`,
          'Content-Type': 'application/json'
        },
        timeout: 60000 // 60 second timeout
      }
    );

    console.log('✅ Response received:');
    console.log('Status:', response.status);
    console.log('Data:', JSON.stringify(response.data, null, 2));

  } catch (error) {
    console.error('❌ Test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testDemandForecastingSchedule();
