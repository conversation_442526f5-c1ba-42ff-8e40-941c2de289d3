const axios = require('axios');

/**
 * Test script for conflict detection and storage in auto-generated schedules
 * Tests the enhanced functionality for preview schedules
 */

const BASE_URL = 'http://localhost:3000/api/v1';

// Test credentials
const testCredentials = {
  email: '<EMAIL>',
  password: 'Admin@123'
};

let authToken = '';

/**
 * <PERSON>gin and get authentication token
 */
async function login() {
  try {
    console.log('🔐 Logging in...');
    const response = await axios.post(`${BASE_URL}/auth/login`, testCredentials);
    
    if (response.data.success && response.data.data && response.data.data.accessToken) {
      authToken = response.data.data.accessToken;
      console.log('✅ Login successful');
      return true;
    } else if (response.data.success && response.data.data && response.data.data.token) {
      authToken = response.data.data.token;
      console.log('✅ Login successful');
      return true;
    } else if (response.data.success && response.data.token) {
      // Alternative token location
      authToken = response.data.token;
      console.log('✅ Login successful');
      return true;
    } else {
      console.error('❌ Login failed:', response.data.message);
      console.error('Response structure:', JSON.stringify(response.data, null, 2));
      return false;
    }
  } catch (error) {
    console.error('❌ Login error:', error.response?.data?.message || error.message);
    return false;
  }
}

/**
 * Create an auto-generated schedule that will likely have conflicts
 */
async function createScheduleWithConflicts() {
  try {
    console.log('\n🎯 Creating auto-generated schedule with potential conflicts...');
    
    const scheduleData = {
      templateMode: {
        shiftTemplateId: 1, // Use existing template
        startDate: '2025-12-01',
        endDate: '2025-12-15',
        scheduleCategory: 'custom',
        customName: `Conflict Test Schedule ${Date.now()}`,
        customDescription: 'Testing conflict detection and storage system'
      },
      constraints: {
        autoAssignEmployees: true,
        respectAvailability: true,
        balanceWorkload: true,
        designationPriority: true,
        maxShiftsPerEmployee: 5, // Low limit to force conflicts
        maxConsecutiveDays: 3    // Low limit to force conflicts
      }
    };

    console.log('📤 Sending request with payload:');
    console.log(JSON.stringify(scheduleData, null, 2));

    const response = await axios.post(
      `${BASE_URL}/rota-schedules/auto-generate`,
      scheduleData,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('\n✅ Schedule created successfully:');
    console.log(`Status: ${response.status}`);
    console.log(`Schedule ID: ${response.data.data.schedule.id}`);
    console.log(`Schedule Name: ${response.data.data.schedule.name}`);
    
    return response.data.data.schedule.id;

  } catch (error) {
    console.error('\n❌ Schedule creation failed:');
    console.error(`Status: ${error.response?.status}`);
    console.error(`Data:`, error.response?.data);
    return null;
  }
}

/**
 * Test fetching schedule with conflicts and settings
 */
async function testScheduleWithConflicts(scheduleId) {
  try {
    console.log(`\n📋 Fetching schedule ${scheduleId} to check conflicts and settings...`);
    
    const response = await axios.get(
      `${BASE_URL}/rota-schedules/${scheduleId}?includeInstances=true&includeStatistics=true`,
      {
        headers: {
          'Authorization': `Bearer ${authToken}`
        }
      }
    );

    const schedule = response.data.data;
    
    console.log('\n✅ Schedule fetched successfully:');
    console.log(`Status: ${schedule.status}`);
    console.log(`Type: ${schedule.type}`);
    console.log(`Schedule Type: ${schedule.scheduleType}`);
    
    // Check if conflicts are included for preview schedules
    if (schedule.status === 'preview') {
      console.log('\n🔍 Preview Schedule - Checking conflicts and settings:');
      
      if (schedule.conflicts) {
        console.log(`📊 Conflicts found: ${schedule.conflicts.length}`);
        schedule.conflicts.forEach((conflict, index) => {
          console.log(`  ${index + 1}. ${conflict.type} - ${conflict.severity} - ${conflict.title}`);
          console.log(`     Description: ${conflict.description}`);
          if (conflict.employee) {
            console.log(`     Employee: ${conflict.employee.name} (${conflict.employee.email})`);
          }
          if (conflict.designation) {
            console.log(`     Designation: ${conflict.designation.name}`);
          }
          console.log(`     Date: ${conflict.date}`);
        });
      } else {
        console.log('📊 No conflicts found');
      }
      
      if (schedule.settings) {
        console.log('\n⚙️ Generation Settings:');
        console.log(`  Mode: ${schedule.settings.mode}`);
        console.log(`  Generated At: ${schedule.settings.generatedAt}`);
        console.log(`  Date Range: ${schedule.settings.parameters.dateRange.startDate} to ${schedule.settings.parameters.dateRange.endDate}`);
        console.log(`  Template ID: ${schedule.settings.parameters.template.shiftTemplateId}`);
        console.log(`  Constraints:`, JSON.stringify(schedule.settings.constraints, null, 4));
        console.log(`  Results: ${schedule.settings.results.summary.successful} successful, ${schedule.settings.results.summary.failed} failed`);
      } else {
        console.log('⚙️ No generation settings found');
      }
      
    } else {
      console.log(`\n📋 Schedule status is '${schedule.status}' - conflicts and settings only shown for preview schedules`);
    }
    
    // Check generation metadata
    if (schedule.generationMetadata) {
      console.log('\n🔧 Generation Metadata present:', typeof schedule.generationMetadata);
    }
    
    return true;

  } catch (error) {
    console.error('\n❌ Failed to fetch schedule:');
    console.error(`Status: ${error.response?.status}`);
    console.error(`Data:`, error.response?.data);
    return false;
  }
}

/**
 * Main test function
 */
async function runConflictDetectionTest() {
  console.log('🧪 Starting Conflict Detection and Storage Test\n');
  console.log('=' .repeat(60));
  
  // Step 1: Login
  const loginSuccess = await login();
  if (!loginSuccess) {
    console.log('\n❌ Test failed: Could not login');
    return;
  }
  
  // Step 2: Create schedule with potential conflicts
  const scheduleId = await createScheduleWithConflicts();
  if (!scheduleId) {
    console.log('\n❌ Test failed: Could not create schedule');
    return;
  }
  
  // Step 3: Test fetching schedule with conflicts and settings
  const fetchSuccess = await testScheduleWithConflicts(scheduleId);
  if (!fetchSuccess) {
    console.log('\n❌ Test failed: Could not fetch schedule details');
    return;
  }
  
  console.log('\n' + '=' .repeat(60));
  console.log('✅ Conflict Detection and Storage Test completed successfully!');
  console.log('\nKey features tested:');
  console.log('  ✓ Auto-schedule generation with conflict detection');
  console.log('  ✓ Conflict storage in database');
  console.log('  ✓ Generation constraints storage in metadata');
  console.log('  ✓ Enhanced getScheduleById API for preview schedules');
  console.log('  ✓ Conflicts and settings inclusion in API response');
}

// Run the test
runConflictDetectionTest().catch(console.error);
