const Joi = require('joi');

// Test the constraints validator to see if defaults are applied
async function testConstraintsDefault() {
  console.log('🧪 Testing Constraints Default Values');
  
  try {
    // Import the validator
    const rotaScheduleValidator = require('./src/api/validators/rotaSchedule.validator.js');
    const generateAutoSchedule = rotaScheduleValidator.generateAutoSchedule;
    
    // Test 1: No constraints provided
    console.log('\n📋 Test 1: No constraints provided');
    const testData1 = {
      templateMode: {
        shiftTemplateId: 1,
        startDate: '2025-12-01',
        endDate: '2025-12-07',
        customName: 'Test Schedule'
      }
    };
    
    const result1 = generateAutoSchedule.validate(testData1);
    if (result1.error) {
      console.log('❌ Validation Error:', result1.error.details);
    } else {
      console.log('✅ Validation Success');
      console.log('📊 Constraints:', JSON.stringify(result1.value.constraints, null, 2));
    }
    
    // Test 2: Empty constraints object provided
    console.log('\n📋 Test 2: Empty constraints object provided');
    const testData2 = {
      templateMode: {
        shiftTemplateId: 1,
        startDate: '2025-12-01',
        endDate: '2025-12-07',
        customName: 'Test Schedule'
      },
      constraints: {}
    };
    
    const result2 = generateAutoSchedule.validate(testData2);
    if (result2.error) {
      console.log('❌ Validation Error:', result2.error.details);
    } else {
      console.log('✅ Validation Success');
      console.log('📊 Constraints:', JSON.stringify(result2.value.constraints, null, 2));
    }
    
    // Test 3: Partial constraints provided
    console.log('\n📋 Test 3: Partial constraints provided');
    const testData3 = {
      templateMode: {
        shiftTemplateId: 1,
        startDate: '2025-12-01',
        endDate: '2025-12-07',
        customName: 'Test Schedule'
      },
      constraints: {
        autoAssignEmployees: false,
        maxConsecutiveDays: 5
      }
    };
    
    const result3 = generateAutoSchedule.validate(testData3);
    if (result3.error) {
      console.log('❌ Validation Error:', result3.error.details);
    } else {
      console.log('✅ Validation Success');
      console.log('📊 Constraints:', JSON.stringify(result3.value.constraints, null, 2));
    }
    
    console.log('\n🎯 Test completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testConstraintsDefault();
