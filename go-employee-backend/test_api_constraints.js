const axios = require('axios');

// Test API call with your exact parameters
async function testAPIConstraints() {
  console.log('🧪 Testing API Call with Your Exact Parameters');
  
  try {
    // Login first
    console.log('🔐 Logging in...');
    const loginResponse = await axios.post('http://localhost:3000/api/v1/auth/login', {
      email: '<EMAIL>',
      password: 'Admin@123'
    });
    
    const token = loginResponse.data.result.accessToken;
    console.log('✅ Login successful');
    
    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    };
    
    // Your exact request data
    const requestData = {
      templateMode: {
        shiftTemplateId: 20,
        startDate: "2025-07-01",
        endDate: "2025-07-30",
        customName: "test scheduljhgeabxvvjadhjhgbfsdfdashkhhgffgffjugjhfjhcsulddydhhhhfghggsdAug"   
      }
      // Note: No constraints field provided (just like your request)
    };
    
    console.log('\n📋 Request Data:');
    console.log(JSON.stringify(requestData, null, 2));
    
    // Make API call
    console.log('\n🚀 Making API call to /api/v1/rota-schedules/auto-generate');
    
    const response = await axios.post(
      'http://localhost:3000/api/v1/rota-schedules/auto-generate',
      requestData,
      { headers }
    );
    
    console.log('✅ API call successful!');
    console.log('\n📊 Response Status:', response.status);
    console.log('📊 Response Message:', response.data.message);
    
    // Check if schedule was created
    if (response.data.data && response.data.data.schedule) {
      const schedule = response.data.data.schedule;
      console.log('\n✅ Schedule Created:');
      console.log(`- ID: ${schedule.id}`);
      console.log(`- Name: ${schedule.name}`);
      console.log(`- Status: ${schedule.status}`);
      
      // Check if constraints are stored in generationMetadata
      if (schedule.generationMetadata) {
        const metadata = typeof schedule.generationMetadata === 'string' 
          ? JSON.parse(schedule.generationMetadata) 
          : schedule.generationMetadata;
        
        if (metadata.constraints) {
          console.log('\n✅ SUCCESS: Constraints found in generationMetadata!');
          console.log('📊 Stored Constraints:');
          console.log(JSON.stringify(metadata.constraints, null, 2));
          
          // Check specific values
          console.log('\n🔍 Key Constraint Values:');
          console.log(`- maxConsecutiveDays: ${metadata.constraints.maxConsecutiveDays}`);
          console.log(`- maxShiftsPerEmployee: ${metadata.constraints.maxShiftsPerEmployee}`);
          console.log(`- rebalanceInterval: ${metadata.constraints.rebalanceInterval}`);
          console.log(`- autoAssignEmployees: ${metadata.constraints.autoAssignEmployees}`);
          
        } else {
          console.log('\n❌ ISSUE: No constraints found in generationMetadata');
        }
      } else {
        console.log('\n❌ ISSUE: No generationMetadata found in response');
      }
      
      // Now test getById to see if constraints are returned
      console.log('\n🔍 Testing getById API to check constraints return...');
      
      const getByIdResponse = await axios.get(
        `http://localhost:3000/api/v1/rota-schedules/${schedule.id}`,
        { headers }
      );
      
      const retrievedSchedule = getByIdResponse.data.data;
      
      if (retrievedSchedule.status === 'preview' && retrievedSchedule.constraints) {
        console.log('\n✅ SUCCESS: Constraints returned in getById API!');
        console.log('📊 Retrieved Constraints:');
        console.log(JSON.stringify(retrievedSchedule.constraints, null, 2));
      } else {
        console.log('\n⚠️  Note: Schedule status is not preview or constraints not returned');
        console.log(`- Status: ${retrievedSchedule.status}`);
        console.log(`- Has constraints: ${!!retrievedSchedule.constraints}`);
      }
      
    } else {
      console.log('\n❌ ISSUE: No schedule data in response');
    }
    
    console.log('\n🎯 Test completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Test failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', JSON.stringify(error.response.data, null, 2));
    } else {
      console.error('Error:', error.message);
    }
  }
}

// Run the test
testAPIConstraints();
